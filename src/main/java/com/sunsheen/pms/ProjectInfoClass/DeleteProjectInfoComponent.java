package com.sunsheen.pms.ProjectInfoClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("DeleteProjectInfoComponent")
@BixComponentPackage(dirname = "采购项目删除", type = "SYSTEM")
@Slf4j
public class DeleteProjectInfoComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Component(
            name = "BusinessInsertComponent",
            memo = "采购项目删除"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @Override
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 获取删除参数
            Object dataObj = param.get("data");
            if (dataObj == null) {
                result.put("success", false);
                result.put("message", "参数 data 不能为空");
                return result;
            }

            if (!(dataObj instanceof Map)) {
                result.put("success", false);
                result.put("message", "参数 data 必须是 Map 类型");
                return result;
            }

            Map<String, Object> deleteParam = (Map<String, Object>) dataObj;
            List<Object> idList = null;

            // 检查是否包含idList参数
            if (deleteParam.containsKey("idList")) {
                Object idListObj = deleteParam.get("idList");
                if (idListObj instanceof List) {
                    idList = new ArrayList<>((List<?>) idListObj);
                }
                if (idList == null || idList.isEmpty()) {
                    result.put("success", false);
                    result.put("message", "idList参数为空或格式错误");
                    return result;
                }

                // 执行批量删除
                for (Object id : idList) {
                    try {
                        HashMap<String, Object> deleteMap = new HashMap<>();
                        deleteMap.put("dataId", "PMS.deleteProjectInfo");
                        deleteMap.put("data", Collections.singletonMap("id", id));

                        Object deleteResult = saveData.run(deleteMap);
                        if (deleteResult instanceof Integer && (Integer) deleteResult > 0) {
                            successCount++;
                        } else {
                            failureCount++;
                        }
                    } catch (Exception e) {
                        log.error("项目信息删除失败，ID: {}", id, e);
                        failureCount++;
                    }
                }
            }
            // 不支持的删除方式
            else {
                result.put("success", false);
                result.put("message", "未指定有效的删除条件，支持idList");
                return result;
            }

            result.put("success", true);
            result.put("message", "删除完成");
            result.put("total", successCount + failureCount);
            result.put("successCount", successCount);
            result.put("failureCount", failureCount);

        } catch (Exception e) {
            log.error("项目信息信息删除请求失败", e);
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }

        return result;
    }
}