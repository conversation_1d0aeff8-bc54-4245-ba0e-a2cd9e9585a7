package com.sunsheen.pms.ProjectInfoClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Controller("InsertAllInfoComponent")
@BixComponentPackage(dirname = "插入表单信息", type = "SYSTEM")
@Slf4j
public class InsertAllInfoComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Component(
            name = "BusinessInsertComponent",
            memo = "插入表单信息"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @Override
    @MultiTransactional
    public Object run(Map param) {
        // 1. 校验项目基本信息参数（移除自动生成的字段，如create_time）
        List<String> projectInfoArgs = Arrays.asList(
                "pid", "pname", "purcharse_type", "Is_center", "rp_deptId",
                "contact", "contact_phone", "content", "fund_source",
                "fund_name", "budget", "ptype", "winner_amount",
                "ticket_amount", "is_imports", "is_argumentation",
                "is_sample", "applyTime"
        );

        projectInfoArgs.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        // 2. 校验附件列表参数（对应pms_project_goods表）
        if (!param.containsKey("goods") || !(param.get("goods") instanceof List)) {
            log.error("缺少附件列表参数goods");
            throw new RuntimeException("缺少附件列表参数goods");
        }

        // 3. 插入项目基本信息
        saveData.run(Map.of(
                "dataId", "PMS.InsertProjectInfo",
                "data", param
        ));

        // 4. 循环插入附件列表
        List<Map> attachments = (List<Map>) param.get("attachments");
        for (Map attachment : attachments) {
            // 校验单个附件的必要参数
            List<String> attachmentArgs = Arrays.asList("aname");
            attachmentArgs.forEach(arg -> {
                if (!attachment.containsKey(arg)) {
                    log.error("附件缺少参数{}", arg);
                    throw new RuntimeException("附件缺少参数" + arg);
                }
            });

            // 确保附件关联到当前项目
            attachment.put("pid", param.get("pid"));
            LocalDateTime currentTime = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedTime = currentTime.format(formatter);

            // 添加到attachment Map
            attachment.put("create_time", formattedTime);

            saveData.run(Map.of(
                    "dataId", "PMS.InsertProjectAttachment",
                    "data", attachment
            ));
        }

        // 5. 循环插入物资列表
        List<Map> goods = (List<Map>) param.get("goods");
        for (Map good : goods) {
            // 校验单个物资的必要参数
            List<String> goodArgs = Arrays.asList("pa_name", "reference", "amount", "unit", "unit_price");
            goodArgs.forEach(arg -> {
                if (!good.containsKey(arg)) {
                    log.error("物资缺少参数{}", arg);
                    throw new RuntimeException("物资缺少参数" + arg);
                }
            });

            // 确保物资关联到当前项目
            good.put("pid", param.get("pid"));

            // 自动计算总价（如果未提供）
            if (!good.containsKey("total_price")) {
                try {
                    BigDecimal amount = new BigDecimal(good.get("amount").toString());
                    BigDecimal unitPrice = new BigDecimal(good.get("unit_price").toString());
                    good.put("total_price", amount.multiply(unitPrice).setScale(2, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    log.error("计算物资总价失败", e);
                    throw new RuntimeException("计算物资总价失败");
                }
            }

            saveData.run(Map.of(
                    "dataId", "PMS.InsertProjectGoods",
                    "data", good
            ));
        }

        return Map.of("success", true, "message", "项目创建成功");
    }
}
