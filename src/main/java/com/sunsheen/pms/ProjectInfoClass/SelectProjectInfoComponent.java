package com.sunsheen.pms.ProjectInfoClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;


@Controller("SelectProjectInfoComponent")
@BixComponentPackage(dirname = "查询项目", type = "SYSTEM")
@Slf4j
public class SelectProjectInfoComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;


    @Component(
            name = "DepartmentSelectComponent",
            memo = "查询项目"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> selectMap = new HashMap<>();
        selectMap.put("dataId", "PMS.selectProjectInfo");
        selectMap.put("deptId", param.get("deptId"));
//        queryForList.run(selectMap);
        return queryForList.run(selectMap);
    }

}
