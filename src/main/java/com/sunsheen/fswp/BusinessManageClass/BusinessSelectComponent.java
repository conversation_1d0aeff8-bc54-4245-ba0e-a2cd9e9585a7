package com.sunsheen.fswp.BusinessManageClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;

@Controller("BusinessSelectComponent")
@BixComponentPackage(dirname = "业务流程信息查询", type = "SYSTEM")
@Slf4j
public class BusinessSelectComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "BusinessSelectComponent",
            memo = "业务流程信息查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> resultMap = new HashMap<>();
        // 分页大小
        Integer pageSize = Integer.valueOf((String) param.get("pageSize"));
        Integer page = Integer.valueOf((String) param.get("page"));
        HashMap<String,Object> tempDataMap = new HashMap<>();
        HashMap<String,Object> countMap = new HashMap<>();
        // 条件查询参数
        String businessName = null;
        StringBuffer tempName = new StringBuffer("%");
        if (param.get("businessName") != null) {
           businessName =  tempName.append(new StringBuffer((String)param.get("businessName"))).append("%").toString();
            tempDataMap.put("businessName",businessName);
            countMap.put("businessName",businessName);
        }

        // 查询业务流程信息
        tempDataMap.put("start", (page - 1) * pageSize);
        tempDataMap.put("limit", pageSize);
        tempDataMap.put("dataId", "BusinessManage.selectBusiness");
        List<Map<String,Object>> tempDataList = (List) this.queryForList.run(tempDataMap);
        // 查询数据总条数
        countMap.put("dataId","BusinessManage.selectCount");
        List<Map<String,Object>> tempDataCountList = (List) this.queryForList.run(countMap);

        // json反序列化
        List<Map<String,Object>> dataList = new ArrayList<>();
        for (Map<String,Object> tempDataCountMap : tempDataList) {
            HashMap<String,Object> dataMap = new HashMap<>();
            dataMap.put("flows",new ArrayList<>());
            String jsonString = (String) tempDataCountMap.get("flows");
            if (jsonString != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    List<Map<String, Object>> list = objectMapper.readValue(jsonString, new TypeReference<List<Map<String, Object>>>() {});
                    dataMap.put("flows",list);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            dataMap.put("businessId",tempDataCountMap.get("businessId"));
            dataMap.put("businessName",tempDataCountMap.get("businessName"));
            dataMap.put("order",tempDataCountMap.get("orders"));
            dataList.add(dataMap);
        }
        long count = (long) tempDataCountList.getFirst().get("count");
        resultMap.put("data",dataList);
        resultMap.put("count",count);
        resultMap.put("code",2);
        return resultMap;
    }
}
