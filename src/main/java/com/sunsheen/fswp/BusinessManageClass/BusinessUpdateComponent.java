package com.sunsheen.fswp.BusinessManageClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("BusinessUpdateComponent")
@BixComponentPackage(dirname = "流程信息更新", type = "SYSTEM")
@Slf4j
public class BusinessUpdateComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Override
    @Component(
            name = "BusinessInsertComponent",
            memo = "流程信息更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        List<Map<String,Object>> dataList = (List<Map<String, Object>>) param.get("data");
        for (Map<String, Object> dataMaps : dataList) {
            String businessId = (String) dataMaps.get("businessId");
            String businessName = dataMaps.get("name").toString();
            List<Map<String, Object>> dataMap = (List<Map<String, Object>>) dataMaps.get("children");
            String jsonString = null;
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                 jsonString = objectMapper.writeValueAsString(dataMap);
                System.out.println(jsonString);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            HashMap<String, Object> tempData = new HashMap<>();
            tempData.put("businessId", businessId);
            tempData.put("businessName", businessName);
            tempData.put("flows", jsonString);
            // 入库
            HashMap<String, Object> updateBusinessMap = new HashMap<>();
            updateBusinessMap.put("dataId", "BusinessManage.updateBusiness");
            updateBusinessMap.put("data", tempData);
            saveData.run(updateBusinessMap);
        }
        return null;
    }
}
