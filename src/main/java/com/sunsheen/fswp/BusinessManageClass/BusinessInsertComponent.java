package com.sunsheen.fswp.BusinessManageClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("BusinessInsertComponent")
@BixComponentPackage(dirname = "流程信息存入", type = "SYSTEM")
@Slf4j
public class BusinessInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Override
    @Component(
            name = "BusinessInsertComponent",
            memo = "流程信息存入"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        List<Map<String,Object>> dataList = (List<Map<String, Object>>) param.get("data");
        // 序列化
        for (Map<String, Object> dataMap : dataList) {
            String jsonString = new String();
            Integer order = (Integer) dataMap.get("order");
            String businessId = (String) dataMap.get("businessId");
            String businessName = dataMap.get("name").toString();
//            List<Map<String, Object>> dataMapList = (List<Map<String, Object>>) dataMap.get("children");
            ObjectMapper objectMapper = new ObjectMapper();
/*            try {
                jsonString = objectMapper.writeValueAsString(dataMapList);
                System.out.println(jsonString);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }*/
            HashMap<String, Object> tempData = new HashMap<>();
            tempData.put("orders",order);
            tempData.put("businessId", businessId);
            tempData.put("businessName", businessName);
//            tempData.put("flows", jsonString);
            // 更新
            HashMap<String, Object> insertBusinessMap = new HashMap<>();
            insertBusinessMap.put("dataId", "BusinessManage.insertBusiness");
            insertBusinessMap.put("data", tempData);
            saveData.run(insertBusinessMap);
        }
        return null;
    }
}
