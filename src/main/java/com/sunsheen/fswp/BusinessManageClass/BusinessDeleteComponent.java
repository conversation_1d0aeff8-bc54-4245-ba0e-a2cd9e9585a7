package com.sunsheen.fswp.BusinessManageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("BusinessDeleteComponent")
@BixComponentPackage(dirname = "流程信息删除", type = "SYSTEM")
@Slf4j
public class BusinessDeleteComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Component(
            name = "BusinessDeleteComponent",
            memo = "流程信息删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Override
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> dataMap = new HashMap<>();
        List<Map<String,Object>> dataList = (List<Map<String, Object>>) param.get("data");
        dataMap.putAll(dataList.getFirst());
        HashMap<String,Object> tempDataCountMap = new HashMap<>();
        tempDataCountMap.put("data",dataMap);
        tempDataCountMap.put("dataId","BusinessManage.deleteBussiness");
        saveData.run(tempDataCountMap);
        return null;
    }
}
