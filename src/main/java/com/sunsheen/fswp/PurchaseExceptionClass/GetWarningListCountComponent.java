package com.sunsheen.fswp.PurchaseExceptionClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

@Controller("GetWarningListCountComponent")
@BixComponentPackage(dirname = "预警的数量统计", type = "SYSTEM")
@Slf4j
public class GetWarningListCountComponent extends ABaseComponent {

    @Autowired
    QueryDataForObjectComponent queryDataForObjComp;

    @Override
    @Component(name = "GetWarningListCountComponent", memo = "预警的数量统计")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的统计数据")})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();


        // 获取黄色预警数量
        Map<String, Object> yellowParams = new HashMap<>();
        yellowParams.put("dataId", "PurchaseException.getYellowWarningCount");
        long yellowCount = (long) queryDataForObjComp.run(yellowParams);
        result.put("warningLevel1", yellowCount);

        //获取红色预警数量
        Map<String, Object> redParams = new HashMap<>();
        redParams.put("dataId", "PurchaseException.getRedWarningCount");
        long redCount = (long) queryDataForObjComp.run(redParams);
        result.put("warningLevel2", redCount);


        long totalCount = 2567; // 总数量暂时使用假数据
        result.put("totalIndicator", totalCount);

        long healthCount = totalCount - redCount - yellowCount;
        result.put("healthIndicator", healthCount);

        // 计算健康度
        double healthRate = (healthCount * 100.0) / 2567;
        BigDecimal bd = new BigDecimal(healthRate);
        bd = bd.setScale(4, RoundingMode.HALF_UP);// 四舍五入，保留四位小数

        result.put("healthScore", bd);
        result.put("code",1);
        return result;
    }
}
