package com.sunsheen.fswp.PurchaseExceptionClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("GetAcceptanceStandardComponent")
@BixComponentPackage(dirname = "验收标准降低异常", type = "SYSTEM")
@Slf4j
public class GetAcceptanceStandardComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    QueryDataForObjectComponent queryDataForObjComp;

    @Override
    @Component(name = "GetAcceptanceStandardComponent", memo = "验收标准降低异常")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {

        int page = Integer.parseInt(param.getOrDefault("page", "1").toString());
        int pageSize = Integer.parseInt(param.getOrDefault("pageSize", "10").toString());
        int offset = (page - 1) * pageSize; // 手动计算偏移量

        Map<String, Object> pageParamsMap = new HashMap<>();
        pageParamsMap.put("page", page);
        pageParamsMap.put("pageSize", pageSize);
        pageParamsMap.put("offset", offset);

        String deptId = (String) param.get("deptId");
        Map<String, Object> resMap = new HashMap<>();
        Long count = 0L;

        //-1表示全校
        if ("000001".equals(deptId)) {
            pageParamsMap.put("dataId", "PurchaseException.getGetAcceptanceStandard");
            resMap.put("data", queryForList.run(pageParamsMap));

            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "PurchaseException.countAcceptanceStandard");
            count = (Long) queryDataForObjComp.run(countParams);
        } else {
            pageParamsMap.put("dataId", "PurchaseException.getAcceptanceStandardByDept");
            pageParamsMap.put("p_dept_id", deptId);
            resMap.put("data", queryForList.run(pageParamsMap));

            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "PurchaseException.countAcceptanceStandardByDept");
            countParams.put("p_dept_id", deptId);
            count = (Long) queryDataForObjComp.run(countParams);
        }

        resMap.put("count", count);
        resMap.put("title","acceptance_standard_lower");
        resMap.put("code",1);
        return resMap;
    }
}
