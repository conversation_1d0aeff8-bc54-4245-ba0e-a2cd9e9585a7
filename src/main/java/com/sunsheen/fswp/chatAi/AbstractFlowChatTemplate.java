package com.sunsheen.fswp.chatAi;


import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Component
public abstract class AbstractFlowChatTemplate implements FlowChatCallBack{

    private WebClient webClient;

    @PostConstruct
    private void init() {
        webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                // .defaultHeader("Authorization","Bearer sk-33Y4z128371kK2f6EmfZ8DR2bt293RdZT8ZXbfk3a694Vour") // 测试接口
                // .defaultHeader("Authorization","Bearer sk-A8QdaUwxcYuHs9EUH1K115exviOF1uppn55K1EbzOJrtQ9hv") //正式接口
            .build();
    }

    private final ConcurrentHashMap<Long, FlowChatSubscriber> subscriberMap = new ConcurrentHashMap<>();

    /**
     * 问答入口
     */
    public Flux<String> request(FlowChatContext context) {

        // 请求大模型问答之前的逻辑处理
        doPreRequest(context);

        // 请求大模型、处理回调逻辑
        return Flux.create(emitter -> {
            Flux<String> response = this.doRequest(context, buildRequest(context));
            log.info("subscriberMap in AbstractChatService before put: {}", JsonUtils.toJson(subscriberMap));
            FlowChatSubscriber subscriber = new FlowChatSubscriber(emitter, this, context, subscriberMap);
            subscriberMap.put(context.getRequestParam().getSessionId(), subscriber);
//            log.info("subscriberMap in AbstractChatService after put: " + JsonUtils.toJson(subscriberMap));
            response.subscribe(subscriber);
            emitter.onDispose(subscriber);
        });
    }

    public void stop(Long sessionId) {
        FlowChatSubscriber subscriber = subscriberMap.get(sessionId);
        if (subscriber == null) {
            return;
        }
        subscriber.stop();
    }

    /**
     * 保存对话内容
     */
    protected Long saveChatMsg(FlowChatContext context, String query) {

        // 保存对话query

        return null;
    }

    /**
     * 更新对话内容
     */
    protected void updateChatMsg(FlowChatContext context) {

        // 更新answer

    }

    /**
     * 构建响应参数
     */
    protected Map<String, Object> buildAnswer(String answer) {
        Map<String, Object> res = new HashMap<>(4, 1);
        res.put("answer", answer);
        res.put("code", 2);
        return res;
    }

    /**
     * 请求大模型
     */
    private Flux<String> doRequest(FlowChatContext context, FLowChatRequest request) {
        log.info("请求大模型开始，URL:{}, 参数:{},表头:{}", request.getUrl(), request.getJsonBody(),request.getHeaders());

        WebClient.RequestBodySpec reqSpec = webClient.post().uri(request.getUrl());
        if (request.getHeaders() != null) {
            for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
                reqSpec = reqSpec.header(entry.getKey(), entry.getValue());
            }
        }
        return reqSpec
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(request.getJsonBody())
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(WebClientResponseException.class, ex -> {
                    subscriberMap.remove(context.getRequestParam().getSessionId());
                    log.error("请求大模型接口异常", ex);
                    return Flux.just(JsonUtils.toJson(buildAnswer("服务器繁忙，请稍后重试！")));
                })
                .onErrorResume(Throwable.class, ex -> {
                    subscriberMap.remove(context.getRequestParam().getSessionId());
                    log.error("系统异常", ex);
                    return Flux.just(JsonUtils.toJson(buildAnswer("系统繁忙，请稍后重试!")));
                });
    }

    /**
     * 前置逻辑处理
     */
    protected abstract void doPreRequest(FlowChatContext context);

    /**
     * 构建大模型请求参数
     */
    protected abstract FLowChatRequest buildRequest(FlowChatContext context);
}
