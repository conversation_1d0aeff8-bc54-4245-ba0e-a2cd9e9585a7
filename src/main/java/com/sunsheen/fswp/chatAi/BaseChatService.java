package com.sunsheen.fswp.chatAi;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("baseChatService")
public class BaseChatService extends AbstractFlowChatTemplate {


//    @Override
//    public FlowChatSceneEnum getFlowChatEnum() {
//        return FlowChatSceneEnum.BASE_CHAT;
//    }

    public String onNext(String data, FlowChatContext context) {

        // 解析响应数据项
        JsonNode jsonNode = JsonUtils.toJsonNode(data);
        if (jsonNode == null || !jsonNode.isObject()) {
            return data;
        }
//        ObjectNode objectNode = (ObjectNode) jsonNode;
//        log.info("BaseService --生成数据：---",objectNode.toString());
//        String answer = objectNode.get("choices").get(0).get("delta").get("content").asText();
//        // 拼接answer
//        context.getAnswer().append(answer);

        return data;
    }

    public void completed(FlowChatContext context) {
        // 更新对话
        updateChatMsg(context);
    }

    public void error(FlowChatContext context) {
        // 更新对话
        updateChatMsg(context);
    }


    @Override
    protected FLowChatRequest buildRequest(FlowChatContext context) {
        String query = context.getRequestParam().getQuery();
        FLowChatRequest request = new FLowChatRequest();

        // 请求大模型API地址
        String apiurl;
        Map<String, String> headers = new HashMap<>();
        if (query != null && query.contains("体检报告")) {
            // apiurl= "http://api.agent-14.default.epai.cuit.edu.cn:30080/v1/chat/completions";
            // headers.put("Authorization", "Bearer sk-1iI13BJKoD3P1W0hHt44I12P1lK9160Pm8zac27qfa80u80s");

            // api-13
            // apiurl = "http://api.agent-13.default.epai.cuit.edu.cn:30080/v1/chat/completions";
            // headers.put("Authorization", "Bearer sk-4J5i54b44Rqg16W86os2gakidzde5bc9cib5R6oi16fhcX0N");

            // api-15
            apiurl = "http://api.agent-15.default.epai.cuit.edu.cn:30080/v1/chat/completions";
            headers.put("Authorization", "Bearer sk-ee29iECq948911A7bKHA7Fe972dFu8g8Z8hSJP12m9V2d39M");

        } else {
            // 原有模型API逻辑
            // String url = "http://api.agent-2.default.epai.cuit.edu.cn:30080/v1/chat/completions"; // 测试接口
            apiurl = "http://api.agent-6.default.epai.cuit.edu.cn:30080/v1/chat/completions"; // 正是接口
            // ("Authorization","Bearer sk-A8QdaUwxcYuHs9EUH1K115exviOF1uppn55K1EbzOJrtQ9hv")
            headers.put("Authorization", "Bearer sk-A8QdaUwxcYuHs9EUH1K115exviOF1uppn55K1EbzOJrtQ9hv");
        }

        // 构建请求参数
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("model","ddd");
            HashMap<Object, Object> msgMap = new HashMap<>();
            msgMap.put("role","user");
            msgMap.put("content",context.getRequestParam().getQuery());
        reqMap.put("messages",new ArrayList<Map>(){{
            add(msgMap);
        }});
        reqMap.put("stream",true);

        return FLowChatRequest.builder()
                .url(apiurl)
                .headers(headers)
                .jsonBody(JsonUtils.toJson(reqMap))
                .build();
    }

    @Override
    protected void doPreRequest(FlowChatContext context) {

        String query = context.getRequestParam().getQuery();

        // 处理校验逻辑...

        // 保存query
        super.saveChatMsg(context, query);
    }
}
