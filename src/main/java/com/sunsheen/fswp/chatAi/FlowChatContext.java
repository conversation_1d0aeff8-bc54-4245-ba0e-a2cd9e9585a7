package com.sunsheen.fswp.chatAi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlowChatContext {

    /**
     * 前端请求参数
     */
    private ChatBaseParam requestParam;

    /**
     * 问答id
     */
    private Long msgId;

    /**
     * 对话回答
     */
    private StringBuilder answer;
    /**
     * 对话对应索引
     */
    private StringBuilder docs;

    public FlowChatContext(ChatBaseParam requestParam) {
        this.requestParam = requestParam;
    }
}
