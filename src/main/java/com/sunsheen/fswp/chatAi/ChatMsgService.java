package com.sunsheen.fswp.chatAi;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;



@Slf4j
@Service
public class ChatMsgService {

//    @Autowired
//    private FlowChatFactory chatFactory;

    @Autowired
    private BaseChatService baseChatService;

//    @Override
//    public SessionVO createSession(CreateSessionParam param) {
//        // 创建会话...
//        return new SessionVO();
//    }

//    @Override
    public Flux<String> baseChat(ChatBaseParam param) {
        return baseChatService.request(new FlowChatContext(param));
    }

//    @Override
//    public void stopChat(SessionBaseParam param) {
//        chatFactory.getInstance(FlowChatSceneEnum.BASE_CHAT)
//                .stop(param.getSessionId());
//    }
}
