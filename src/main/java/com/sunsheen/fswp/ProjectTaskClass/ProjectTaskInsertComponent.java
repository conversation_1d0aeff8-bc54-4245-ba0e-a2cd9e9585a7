package com.sunsheen.fswp.ProjectTaskClass;

import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("ProjectTaskInsertComponent")
@BixComponentPackage(dirname = "任务/项目添加", type = "SYSTEM")
@Slf4j
public class ProjectTaskInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Component(
            name = "ProjectTaskInsertComponent",
            memo = "任务/项目添加"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @Override
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {

        List<String> args = Arrays.asList("name", "type", "participants","ownerId","ownerName");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        param.put("status", "进行中");

        // 获取参数
        Object participantListObject = (Object) this.getCallParam(param, "participants");
        if (!(participantListObject instanceof List)) {
            log.error("participants 非数组对象");
            throw new RuntimeException("participants 应该为数组对象");
        }

        // 判重
        List participantList = (List) participantListObject;
        HashSet<String> checkSet = new HashSet<>();
        participantList.forEach(participantObject -> {
            Map participant = (Map) participantObject;
            if (checkSet.contains(participant.get("participantId").toString())) {
                log.error("参数人重复");
                throw new RuntimeException("参数人重复");
            }
            checkSet.add(participant.get("participantId").toString());
        });

        String type = (String) this.getCallParam(param, "type");

        if (!type.equals("项目") && !type.equals("任务")) {
            log.error("type应为项目/任务");
            throw new RuntimeException("type应为项目/任务");
        }

        // 获取登录的用户名
        String loginAccount = UserUtil.getLogAccount();

        Map accountInfo = (Map) queryForMap.run(Map.of(
                "dataId", "RegularTask.queryAccount",
                "loginAccount", loginAccount
        ));

        if (accountInfo == null || accountInfo.isEmpty()) {
            log.error("用户未登录或不存在");
            throw new RuntimeException("用户未登录或不存在");
        }

        param.put("departmentId", accountInfo.get("deptId"));
        param.put("departmentName", accountInfo.get("dept"));
        param.put("type", type);

        LocalDateTime now = LocalDateTime.now();
        String dateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        param.put("createdAt", dateTime);

        Object result = null;
        Object dataId = saveData.run(Map.of(
                "dataId", "RegularTask.insertProjectTask",
                "data", param
        ));

        // 获取task自增ID
        Long taskId = Long.valueOf(queryForObject.run(Map.of(
                "dataId", "RegularTask.getKey"
        )).toString());

        // 组装参与人
        List<Map<String, Object>> participants = new ArrayList<>();

        participantList.forEach(participantObject -> {
            Map participant = (Map) participantObject;
            String participantId = (String) participant.get("participantId");

            Map<String, Object> participation = new HashMap<>();
            Map participantInfo = (Map) queryForMap.run(Map.of(
                    "dataId", "RegularTask.queryAccount",
                    "loginAccount", participantId
            ));

            if (participantInfo == null || participantInfo.isEmpty()) {
                log.error("{} 用户未登录或不存在", participant);
                throw new RuntimeException(participant + "为不合法用户");
            }
            participation.put("type", type);
            participation.put("participantId", participantInfo.get("nickName"));
            participation.put("participantName", participantInfo.get("userName"));
            participation.put("createdAt", dateTime);
            participation.put("refId", taskId);
            participation.put("position", (String) participant.get("position"));
            participation.put("content", (String) participant.get("content"));
            participants.add(participation);
        });

        result = saveData.run(Map.of(
                "dataId", "RegularTask.insertTaskAssignment",
                "data", participants
        ));
        return result;
    }
}
