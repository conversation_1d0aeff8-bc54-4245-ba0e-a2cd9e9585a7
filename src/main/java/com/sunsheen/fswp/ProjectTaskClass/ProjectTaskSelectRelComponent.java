package com.sunsheen.fswp.ProjectTaskClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Slf4j
@Controller("ProjectTaskSelectRelComponent")
public class ProjectTaskSelectRelComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "ProjectTaskSelectRelComponent",
            memo = "仅查询登录用户相关任务或项目"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {

        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            try {
                int page = Integer.parseInt(pageV);
                int pageSize = Integer.parseInt(pageSizeV);
                Integer start = (page - 1) * pageSize;
                Integer limit = pageSize;
                param.put("start", start);
                param.put("limit", limit);
            } catch (Exception e) {
                log.error("page和pageSize非法");
                throw new RuntimeException("page和pageSize非法");
            }
        }

        // 获取登录的用户名
        String loginAccount = UserUtil.getLogAccount();

        // 查询登录用户相关的数据
        param.put("loginAccount", loginAccount);

        param.put("dataId", "RegularTask.countSelectProjectTaskRel");
        Long count = (Long) queryForObject.run(param);
        param.put("dataId", "RegularTask.selectProjectTaskRel");

        return Map.of(
                "count", count,
                "data", queryForList.run(param)
        );
    }
}
