package com.sunsheen.fswp.ProjectTaskClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller("ProjectTaskSelectComponent")
public class ProjectTaskSelectComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "ProjectTaskSelectComponent",
            memo = "任务进度查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {

        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            try {
                int page = Integer.parseInt(pageV);
                int pageSize = Integer.parseInt(pageSizeV);
                Integer start = (page - 1) * pageSize;
                Integer limit = pageSize;
                param.put("start", start);
                param.put("limit", limit);
            } catch (Exception e) {
                log.error("page和pageSize非法");
                throw new RuntimeException("page和pageSize非法");
            }
        }

        param.put("dataId", "RegularTask.countSelectProjectTask");
        Long count = (Long) queryForObject.run(param);
        param.put("dataId", "RegularTask.selectProjectTask");

        HashMap<Object, Object> result = new HashMap<>();
        result.put("count", count);
        List list = (List) queryForList.run(param);
        list.forEach( infoObject -> {
            Map map = (Map) infoObject;
            Long refId = (Long) map.get("id");
            map.put("participants", queryForList.run(Map.of(
                    "dataId", "RegularTask.selectTaskAssignmentByRefId",
                    "refId", refId
            )));
        });
        result.put("data", list);
        return result;
    }
}
