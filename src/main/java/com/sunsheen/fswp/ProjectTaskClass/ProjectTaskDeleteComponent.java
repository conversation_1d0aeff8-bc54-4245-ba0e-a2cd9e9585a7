package com.sunsheen.fswp.ProjectTaskClass;

import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Controller("ProjectTaskDeleteComponent")
@BixComponentPackage(dirname = "项目/任务删除", type = "SYSTEM")
@Slf4j
public class ProjectTaskDeleteComponent extends ABaseComponent {


    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveDataPort;


    @Component(name = "ProjectTaskDeleteComponent", memo = "项目/任务删除")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "删除成功的记录数")})
    @Override
    @LogArgs
    public Object run(Map param) {

        Long id = Long.parseLong(this.getCallParam(param, "id").toString());

        Map pjInfo = (Map) queryForMap.run(Map.of("dataId", "RegularTask.selectProjectTask", "id", id));

        if (pjInfo == null || pjInfo.isEmpty()) {
            log.error("项目/任务不存在");
            throw new RuntimeException("项目/任务不存在");
        }

        if (pjInfo.get("status").equals("已结束")) {
            log.error("已结束的项目/任务无法删除");
            throw new RuntimeException("已结束的项目/任务无法删除");
        }

        return saveDataPort.run(Map.of("dataId", "RegularTask.deleteProjectTask", "data", param));
    }
}
