package com.sunsheen.fswp.ProjectTaskClass;

import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Controller("ProjectTaskUpdateComponent")
@BixComponentPackage(dirname = "项目/任务更新", type = "SYSTEM")
@Slf4j
public class ProjectTaskUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @LogArgs
    @Component(name = "ProjectTaskUpdateComponent", memo = "项目/任务更新")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "更新数量")})
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("id", "name", "status", "ownerId");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        // 合法性校验
        Long id = Long.parseLong(this.getCallParam(param, "id").toString());
        String ownerId = (String) this.getCallParam(param, "ownerId");

        Map pjInfo = (Map) queryForMap.run(Map.of("dataId", "RegularTask.selectProjectTask", "id", id));

        if (pjInfo == null || pjInfo.isEmpty()) {
            log.error("项目/任务不存在");
            throw new RuntimeException("项目/任务不存在");
        }

        if (pjInfo.get("status").equals("已结束")) {
            log.error("已结束的项目/任务无法修改");
            throw new RuntimeException("已结束的项目/任务无法修改");
        }

        Map ownerInfo = (Map) queryForMap.run(Map.of("dataId", "RegularTask.queryAccount", "loginAccount", ownerId));

        if (ownerInfo == null || ownerInfo.isEmpty()) {
            log.error("{}不存在", ownerId);
            throw new RuntimeException(ownerId + "不存在");
        }

        param.put("ownerId", ownerId);
        param.put("ownerName", ownerInfo.get("name"));
        param.put("departmentId", ownerInfo.get("unit_sys"));
        param.put("departmentName", ownerInfo.get("unit"));

        return saveData.run(Map.of("dataId", "RegularTask.updateProjectTask", "data", param));
    }
}