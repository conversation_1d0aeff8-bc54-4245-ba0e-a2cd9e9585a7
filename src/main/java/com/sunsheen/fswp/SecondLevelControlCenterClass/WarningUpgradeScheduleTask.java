package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.aop.LogParamsAspect;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
@BixComponentPackage(dirname = "自动升级", type = "BUSINESS")
public class WarningUpgradeScheduleTask {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort queryForList;
    @Autowired
    private LogParamsAspect logParamsAspect;


    /**
     * 每小时执行一次预警升级检查
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时整点执行
    @LogArgs
    public void executeWarningUpgradeTask() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "WarningUpgrade.callWarningUpgrade");
            queryForList.run(param);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}