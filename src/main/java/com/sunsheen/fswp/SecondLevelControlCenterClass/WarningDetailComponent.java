package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service("WarningDetailComponent")
@BixComponentPackage(dirname = "二阶管控中心", type = "BUSINESS")
public class WarningDetailComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(WarningDetailComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "WarningDetailComponent", memo = "获取预警指标详情")
    @Params({
            @ParamItem(type = "java.lang.String", name = "warningId", comment = "预警ID")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回预警指标详情数据")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始获取预警指标详情...");

        try {
            // 参数处理
            String warningId = (String) this.getCallParam(param, "warningId");
            if (warningId == null || warningId.isEmpty()) {
                logger.error("预警ID不能为空");
                return new HashMap<String, Object>() {{
                    put("error", "预警ID不能为空");
                }};
            }

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "SecondLevelControlCenter.getWarningDetail");
            queryParams.put("warningId", warningId);

            // 执行查询
            Map<String, Object> warningDetail = (Map<String, Object>) queryDataForMap.run(queryParams);

            // 如果没有数据，记录日志
            if (warningDetail == null || warningDetail.isEmpty()) {
                logger.warn("未查询到ID为 {} 的预警指标详情", warningId);
                return new HashMap<String, Object>() {{
                    put("error", "未查询到预警指标详情");
                }};
            } else {
                logger.info("获取预警指标详情成功");
                return warningDetail;
            }
        } catch (Exception e) {
            logger.error("获取预警指标详情失败: {}", e.getMessage(), e);
            return new HashMap<String, Object>() {{
                put("error", e.getMessage());
            }};
        }
    }

    /**
     * 从参数中获取值
     */
    protected Object getCallParam(Map param, String key) {
        if (param == null || !param.containsKey(key)) {
            return null;
        }
        return param.get(key);
    }
} 