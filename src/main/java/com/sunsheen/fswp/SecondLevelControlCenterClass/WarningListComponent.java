package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Controller("WarningListComponent")
public class WarningListComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(WarningListComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "WarningListComponent", memo = "获取预警指标列表")
    @Params({
            @ParamItem(type = "java.lang.String", name = "warningLevel", comment = "预警级别"),
            @ParamItem(type = "java.lang.String", name = "business", comment = "业务名称"),
            @ParamItem(type = "java.lang.String", name = "department", comment = "部门名称"),
            @ParamItem(type = "java.lang.String", name = "startDate", comment = "开始日期"),
            @ParamItem(type = "java.lang.String", name = "endDate", comment = "结束日期"),
            @ParamItem(type = "java.lang.Integer", name = "pageNum", comment = "页码"),
            @ParamItem(type = "java.lang.Integer", name = "pageSize", comment = "每页条数")
    })
    @LogArgs
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回分页的预警指标列表数据")
    })
    public Object run(Map param) {
        logger.info("开始获取预警指标列表...");

        try {
            // 参数处理
            String warningLevel = (String) this.getCallParam(param, "warningLevel");
            String business = (String) this.getCallParam(param, "business");
            String department = (String) this.getCallParam(param, "department");
            String startDate = (String) this.getCallParam(param, "startDate");
            String endDate = (String) this.getCallParam(param, "endDate");
            
            // 正确处理数值类型参数，避免类型转换错误
            Integer pageNum = null;
            Integer pageSize = null;
            try {
                Object pageNumObj = this.getCallParam(param, "pageNum");
                Object pageSizeObj = this.getCallParam(param, "pageSize");
                
                if (pageNumObj instanceof Integer) {
                    pageNum = (Integer) pageNumObj;
                } else if (pageNumObj != null) {
                    pageNum = Integer.parseInt(pageNumObj.toString());
                }
                
                if (pageSizeObj instanceof Integer) {
                    pageSize = (Integer) pageSizeObj;
                } else if (pageSizeObj != null) {
                    pageSize = Integer.parseInt(pageSizeObj.toString());
                }
            } catch (NumberFormatException e) {
                logger.warn("分页参数转换异常: {}", e.getMessage());
            }

            // 设置默认值
            if (pageNum == null || pageNum < 1) pageNum = 1;
            if (pageSize == null || pageSize < 1) pageSize = 10;
            
            // 计算offset值
            int offset = (pageNum - 1) * pageSize;

            // 准备查询参数 - 获取总数
            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "SecondLevelControlCenter.countWarningList");
            if (warningLevel != null && !warningLevel.isEmpty()) countParams.put("warningLevel", warningLevel);
            if (business != null && !business.isEmpty()) countParams.put("business", business);
            if (department != null && !department.isEmpty()) countParams.put("department", department);
            if (startDate != null && !startDate.isEmpty()) countParams.put("startDate", startDate);
            if (endDate != null && !endDate.isEmpty()) countParams.put("endDate", endDate);

            // 执行查询 - 获取总数
            Map<String, Object> countResult = (Map<String, Object>) queryDataForMap.run(countParams);
            int total = 0;
            if (countResult != null && countResult.containsKey("total")) {
                total = Integer.parseInt(countResult.get("total").toString());
            }

            // 准备查询参数 - 获取分页数据
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "SecondLevelControlCenter.getWarningList");
            if (warningLevel != null && !warningLevel.isEmpty()) queryParams.put("warningLevel", warningLevel);
            if (business != null && !business.isEmpty()) queryParams.put("business", business);
            if (department != null && !department.isEmpty()) queryParams.put("department", department);
            if (startDate != null && !startDate.isEmpty()) queryParams.put("startDate", startDate);
            if (endDate != null && !endDate.isEmpty()) queryParams.put("endDate", endDate);
            
            // 使用命名参数（与SQL中的:参数名对应）
            queryParams.put("offset", Integer.valueOf(offset));
            queryParams.put("pageSize", Integer.valueOf(pageSize));
            
            // 计算开始位置（用于LIMIT子句的第一个参数）
            queryParams.put("pageStart", Integer.valueOf(offset));
            
            // 保留这些参数用于结果封装
            queryParams.put("pageNum", pageNum);
            queryParams.put("pageSize", pageSize);

            // 执行查询 - 获取分页数据
            List<Map<String, Object>> warningList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            // 封装分页结果
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("list", warningList);

            // 如果没有数据，记录日志
            if (warningList == null || warningList.isEmpty()) {
                logger.warn("未查询到预警指标列表数据");
            } else {
                logger.info("获取预警指标列表数据成功，共 {} 条记录", warningList.size());
            }

            return result;
        } catch (Exception e) {
            logger.error("获取预警指标列表数据失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("total", 0);
            errorResult.put("pageNum", 1);
            errorResult.put("pageSize", 10);
            errorResult.put("list", new HashMap<>());
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }

    /**
     * 从参数中获取值
     */
    protected Object getCallParam(Map param, String key) {
        if (param == null || !param.containsKey(key)) {
            return null;
        }
        return param.get(key);
    }
} 