package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller("SecondLevelListComponent")
@BixComponentPackage(dirname = "二阶列表查询", type = "BUSINESS")
public class SecondLevelListComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;
    
    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(name = "SecondLevelListComponent", memo = "查询二阶列表数据(带分页)")
    @Params({
        @ParamItem(name = "pageNum", type = "Integer", comment = "页码，从1开始"),
        @ParamItem(name = "pageSize", type = "Integer", comment = "每页记录数")
    })
    @Returns(retValue = {
        @ReturnItem(type = "java.util.Map", name = "data", comment = "返回分页数据和总记录数")
    })
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取分页参数
            Integer pageNum = param.containsKey("pageNum") ? Integer.parseInt(param.get("pageNum").toString()) : 1;
            Integer pageSize = param.containsKey("pageSize") ? Integer.parseInt(param.get("pageSize").toString()) : 10;
            
            // 计算offset
            Integer offset = (pageNum - 1) * pageSize;
            
            // 查询列表数据
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "SecondLevelControlCenter.selectList");
            queryParams.put("pageSize", pageSize);
            queryParams.put("offset", offset);
            
            // 复制其它查询条件
            for (Object key : param.keySet()) {
                if (!key.equals("pageNum") && !key.equals("pageSize")) {
                    queryParams.put(key.toString(), param.get(key));
                }
            }
            
            // 执行分页查询
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) queryForList.run(queryParams);
            
            // 查询总记录数
            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "SecondLevelControlCenter.selectListCount");
            
            // 复制查询条件（除了分页参数）
            for (Object key : param.keySet()) {
                if (!key.equals("pageNum") && !key.equals("pageSize")) {
                    countParams.put(key.toString(), param.get(key));
                }
            }
            
            // 使用queryForList获取count结果，并从第一个元素中获取count值
            List<Map<String, Object>> countResult = (List<Map<String, Object>>) queryForList.run(countParams);
            int total = 0;
            if (countResult != null && !countResult.isEmpty() && countResult.get(0).containsKey("count(*)")) {
                total = Integer.parseInt(countResult.get(0).get("count(*)").toString());
            }
            
            // 组装返回结果
            result.put("rows", dataList);
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (total + pageSize - 1) / pageSize); // 计算总页数
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.put("error", e.getMessage());
            return result;
        }
    }
} 