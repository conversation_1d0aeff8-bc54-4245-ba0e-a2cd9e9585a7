package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("WarningDetailsComponent")
@BixComponentPackage(dirname = "预警详情查询", type = "BUSINESS")
public class WarningDetailsComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "WarningDetailsComponent", memo = "查询预警详情信息")
    @Params({
        @ParamItem(name = "indicator_id", type = "String", comment = "指标ID"),
        @ParamItem(name = "start_time", type = "String", comment = "开始时间"),
        @ParamItem(name = "end_time", type = "String", comment = "结束时间")
    })
    @Returns(retValue = {
        @ReturnItem(type = "java.util.Map", name = "data", comment = "返回预警详情")
    })
    @Transactional(rollbackFor = Exception.class)
    @Cacheable(value = "warningDetails", key = "#param.indicator_id + '_' + #param.start_time + '_' + #param.end_time")
    @LogArgs
    public Object run(Map param) {
        try {
            // 查询预警详情（包含资产信息）
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "indicatorDetails.getWarningList");
            
            if (param.containsKey("indicator_id")) {
                queryParams.put("indicator_id", param.get("indicator_id"));
            }
            if (param.containsKey("start_time")) {
                queryParams.put("start_time", param.get("start_time"));
            }
            if (param.containsKey("end_time")) {
                queryParams.put("end_time", param.get("end_time"));
            }

            List<Map<String, Object>> warningList = (List<Map<String, Object>>) queryForList.run(queryParams);
            
            if (warningList == null || warningList.isEmpty()) {
                return null;
            }

            // 获取第一条预警记录
            Map<String, Object> warningDetail = warningList.get(0);

            // 查询预警级别信息
            Map<String, Object> levelParams = new HashMap<>();
            levelParams.put("dataId", "indicatorDetails.getWarning_level");
            levelParams.put("indicator_id", warningDetail.get("indicator_id"));
            List<Map<String, Object>> levelInfoList = (List<Map<String, Object>>) queryForList.run(levelParams);
            
            if (levelInfoList != null && !levelInfoList.isEmpty()) {
                warningDetail.put("indicator_level", levelInfoList);
            }

            // 查询追踪记录
            Map<String, Object> traceParams = new HashMap<>();
            traceParams.put("dataId", "indicatorDetails.getTrace");
            traceParams.put("warning_id", warningDetail.get("warning_id"));
            List<Map<String, Object>> traceList = (List<Map<String, Object>>) queryForList.run(traceParams);
            
            if (traceList != null && !traceList.isEmpty()) {
                warningDetail.put("trace", traceList);
            }

            return warningDetail;

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }
}