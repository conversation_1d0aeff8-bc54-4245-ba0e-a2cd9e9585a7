package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("IndicatorSummaryComponent")
public class IndicatorSummaryComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(IndicatorSummaryComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "IndicatorSummaryComponent", memo = "获取指标监控概览数据")
    @Params({
            @ParamItem(type = "java.lang.String", name = "startDate", comment = "开始日期，可选"),
            @ParamItem(type = "java.lang.String", name = "endDate", comment = "结束日期，可选")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回指标监控概览数据")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始获取指标监控概览数据...");

        try {
            // 参数处理
            String startDate = (String) this.getCallParam(param, "startDate");
            String endDate = (String) this.getCallParam(param, "endDate");

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "SecondLevelControlCenter.getIndicatorSummary");

            // 只有在提供了时间参数时才添加到查询参数中
            if (startDate != null && !startDate.isEmpty()) {
                queryParams.put("startDate", startDate);
            }

            if (endDate != null && !endDate.isEmpty()) {
                queryParams.put("endDate", endDate);
            }

            // 执行查询
            Map<String, Object> summaryData = (Map<String, Object>) queryDataForMap.run(queryParams);

            // 处理空结果
            if (summaryData == null) {
                summaryData = new HashMap<>();
                summaryData.put("totalCount", 0);
                summaryData.put("healthyCount", 0);
                summaryData.put("yellowWarningCount", 0);
                summaryData.put("redWarningCount", 0);
                summaryData.put("healthPercent", 0.0);
            }

            logger.info("获取指标监控概览数据成功");
            return summaryData;
        } catch (Exception e) {
            logger.error("获取指标监控概览数据失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }

    protected Object getCallParam(Map param, String key) {
        if (param == null || !param.containsKey(key)) {
            return null;
        }
        return param.get(key);
    }
}