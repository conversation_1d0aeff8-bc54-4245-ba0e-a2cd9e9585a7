package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("IndicatorDetailComponent")
public class IndicatorDetailComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(IndicatorDetailComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "IndicatorDetailComponent", memo = "获取指标详细信息及二级预警级别")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicatorId", comment = "指标ID")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回指标详细信息及二级预警级别")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始获取指标详细信息...");

        try {
            // 参数处理
            String indicatorId = (String) this.getCallParam(param, "indicatorId");

            // 参数验证
            if (indicatorId == null || indicatorId.isEmpty()) {
                logger.error("指标ID不能为空");
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "指标ID不能为空");
                return errorResult;
            }

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "SecondLevelControlCenter.getIndicatorDetail");
            queryParams.put("indicatorId", indicatorId);

            // 执行查询
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            // 处理查询结果
            if (resultList == null || resultList.isEmpty()) {
                logger.warn("未找到指标ID为{}的指标信息或没有二级预警级别", indicatorId);

                // 尝试查询基础信息（不带预警级别过滤）
                Map<String, Object> baseQueryParams = new HashMap<>();
                baseQueryParams.put("dataId", "SecondLevelControlCenter.getIndicatorBaseInfo");
                baseQueryParams.put("indicatorId", indicatorId);

                List<Map<String, Object>> baseInfoList = (List<Map<String, Object>>) queryDataForList.run(baseQueryParams);

                if (baseInfoList == null || baseInfoList.isEmpty()) {
                    Map<String, Object> emptyResult = new HashMap<>();
                    emptyResult.put("success", false);
                    emptyResult.put("message", "未找到指标信息");
                    return emptyResult;
                }

                // 存在基础信息但没有二级预警级别
                Map<String, Object> indicatorInfo = new HashMap<>();
                Map<String, Object> baseInfo = baseInfoList.get(0);

                indicatorInfo.put("indicatorId", baseInfo.get("indicator_id"));
                indicatorInfo.put("indicatorName", baseInfo.get("indicator_name"));
                indicatorInfo.put("business", baseInfo.get("business"));
                indicatorInfo.put("process", baseInfo.get("process"));
                indicatorInfo.put("subProcess", baseInfo.get("sub_process"));
                indicatorInfo.put("referReguDescribe", baseInfo.get("refer_regu_describe"));
                indicatorInfo.put("riskDescription", baseInfo.get("risk_description"));
                indicatorInfo.put("prevensionMeasure", baseInfo.get("prevension_measure"));

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("indicatorInfo", indicatorInfo);
                result.put("warningLevels", new ArrayList<>());
                result.put("message", "找到指标信息但没有二级预警级别");

                return result;
            }

            // 构造返回结果
            Map<String, Object> indicatorInfo = new HashMap<>();
            List<Map<String, Object>> warningLevels = new ArrayList<>();

            // 提取指标基本信息（从第一条记录）
            Map<String, Object> firstRecord = resultList.get(0);
            indicatorInfo.put("indicatorId", firstRecord.get("indicator_id"));
            indicatorInfo.put("indicatorName", firstRecord.get("indicator_name"));
            indicatorInfo.put("business", firstRecord.get("business"));
            indicatorInfo.put("process", firstRecord.get("process"));
            indicatorInfo.put("subProcess", firstRecord.get("sub_process"));
            indicatorInfo.put("referReguDescribe", firstRecord.get("refer_regu_describe"));
            indicatorInfo.put("riskDescription", firstRecord.get("risk_description"));
            indicatorInfo.put("prevensionMeasure", firstRecord.get("prevension_measure"));

            // 提取所有预警级别（只有二级的）
            for (Map<String, Object> record : resultList) {
                if (record.get("warning_level") != null) {
                    Map<String, Object> levelInfo = new HashMap<>();
                    levelInfo.put("warningLevel", record.get("warning_level"));
                    levelInfo.put("levelDescription", record.get("level_description"));
                    levelInfo.put("supervisorLevel", record.get("supervisor_level"));
                    warningLevels.add(levelInfo);
                }
            }

            // 最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("indicatorInfo", indicatorInfo);
            result.put("warningLevels", warningLevels);

            logger.info("获取指标详细信息成功");
            return result;
        } catch (Exception e) {
            logger.error("获取指标详细信息失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", e.getMessage());
            return errorResult;
        }
    }

    protected Object getCallParam(Map param, String key) {
        if (param == null || !param.containsKey(key)) {
            return null;
        }
        return param.get(key);
    }
}