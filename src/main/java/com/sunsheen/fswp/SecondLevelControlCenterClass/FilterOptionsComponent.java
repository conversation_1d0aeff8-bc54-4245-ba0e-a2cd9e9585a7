package com.sunsheen.fswp.SecondLevelControlCenterClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("FilterOptionsComponent")
@BixComponentPackage(dirname = "二阶管控中心", type = "BUSINESS")
public class FilterOptionsComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(FilterOptionsComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "FilterOptionsComponent", memo = "获取预警筛选选项")
    @Params({
            @ParamItem(type = "java.lang.String", name = "type", comment = "筛选类型：business/department/warningLevel")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "data", comment = "返回筛选选项列表")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始获取筛选选项...");

        try {
            // 参数处理
            String type = (String) this.getCallParam(param, "type");
            if (type == null || type.isEmpty()) {
                logger.error("筛选类型不能为空");
                return new HashMap<String, Object>() {{
                    put("error", "筛选类型不能为空");
                }};
            }

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            
            // 根据不同筛选类型设置不同的查询参数
            switch (type) {
                case "business":
                    queryParams.put("dataId", "SecondLevelControlCenter.getBusinessOptions");
                    break;
                case "department":
                    queryParams.put("dataId", "SecondLevelControlCenter.getDepartmentOptions");
                    break;
                case "warningLevel":
                    queryParams.put("dataId", "SecondLevelControlCenter.getWarningLevelOptions");
                    break;
                default:
                    logger.error("不支持的筛选类型: {}", type);
                    return new HashMap<String, Object>() {{
                        put("error", "不支持的筛选类型");
                    }};
            }

            // 执行查询
            List<Map<String, Object>> optionsList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            // 如果没有数据，记录日志
            if (optionsList == null || optionsList.isEmpty()) {
                logger.warn("未查询到 {} 筛选选项", type);
            } else {
                logger.info("获取 {} 筛选选项成功，共 {} 条", type, optionsList.size());
            }

            return optionsList;
        } catch (Exception e) {
            logger.error("获取筛选选项失败: {}", e.getMessage(), e);
            return new HashMap<String, Object>() {{
                put("error", e.getMessage());
            }};
        }
    }

    /**
     * 从参数中获取值
     */
    protected Object getCallParam(Map param, String key) {
        if (param == null || !param.containsKey(key)) {
            return null;
        }
        return param.get(key);
    }
} 