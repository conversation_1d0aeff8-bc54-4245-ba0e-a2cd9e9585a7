package com.sunsheen.fswp.TaskAssignmentClass;

import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller("TaskAssignmentDeleteComponent")
@BixComponentPackage(dirname = "分工删除", type = "SYSTEM")
@Slf4j
public class TaskAssignmentDeleteComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveDataPort;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Component(
            name = "TaskAssignmentDeleteComponent",
            memo = "分工删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "删除成功的记录数")})
    @Override
    @LogArgs
    @Transactional
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("refId", "participantId");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        Long refId = Long.parseLong(this.getCallParam(param, "refId").toString());
        String participantId = (String) this.getCallParam(param, "participantId");

        List assign = (List) queryForList.run(Map.of(
                "dataId", "RegularTask.selectTaskAssignment",
                "refId", refId,
                "participantId", participantId
        ));
        if (assign == null || assign.isEmpty()) {
            log.error("分工中不存在{}", participantId);
            throw new RuntimeException("分工中不存在" + participantId);
        }

        int result = assign.size();

        assign.forEach(item -> {
            saveDataPort.run(Map.of(
                    "dataId", "RegularTask.deleteTaskAssignment",
                    "data", Map.of(
                            "taskId", refId,
                            "id", ((Map)item).get("id")
                    )
            ));
        });

        return result;
    }
}