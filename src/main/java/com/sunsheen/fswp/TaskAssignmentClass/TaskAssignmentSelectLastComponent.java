package com.sunsheen.fswp.TaskAssignmentClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Controller("TaskAssignmentSelectLastComponent")
public class TaskAssignmentSelectLastComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Component(
            name = "TaskAssignmentSelectLastComponent",
            memo = "项目任务查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @LogArgs
    @Override
    public Object run(Map param) {
        param.put("dataId", "RegularTask.selectLatestTaskProgressByTaskId");
        return queryForList.run(param);
    }
}
