package com.sunsheen.fswp.TaskAssignmentClass;


import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@BixComponentPackage(dirname = "常规任务进程添加", type = "SYSTEM")
@Controller("TaskAssignmentInsertComponent")
@Slf4j
public class TaskAssignmentInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Component(
            name = "TaskAssignmentInsertComponent",
            memo = "常规任务进程添加"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @Transactional
    @LogArgs
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("refId", "participantId", "content", "position");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        Long refId = Long.parseLong(this.getCallParam(param, "refId").toString());
        String participantId = (String) this.getCallParam(param, "participantId");

        List assign = (List) queryForList.run(Map.of(
                "dataId", "RegularTask.selectTaskAssignment",
                "refId", refId,
                "participantId", participantId
        ));
        if (assign != null && !assign.isEmpty()) {
            log.error("分工已存在!");
            throw new RuntimeException("分工已存在!");
        }

        Map participant = (Map) queryForMap.run(Map.of(
                "dataId", "RegularTask.queryAccount",
                "loginAccount", participantId
        ));

        if (participant == null || participant.isEmpty()) {
            log.error("{}不存在!", participantId);
            throw new RuntimeException(participantId + "不存在!");
        }

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("type", "任务");
        paramMap.put("refId", refId);
        paramMap.put("participantId", participantId);
        paramMap.put("participantName", participant.get("userName"));
        paramMap.put("position", param.get("position"));
        paramMap.put("content", param.get("content"));
        paramMap.put("createdAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return saveData.run(Map.of(
                "dataId", "RegularTask.insertTaskAssignment",
                "data", paramMap
        ));
    }
}
