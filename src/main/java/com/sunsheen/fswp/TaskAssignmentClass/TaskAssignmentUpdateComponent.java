package com.sunsheen.fswp.TaskAssignmentClass;


import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller("TaskAssignmentUpdateComponent")
@BixComponentPackage(dirname = "常规任务更新", type = "SYSTEM")
@Slf4j
public class TaskAssignmentUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @LogArgs
    @Component(
            name = "TaskAssignmentUpdateComponent",
            memo = "常规任务更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "更新数量")})
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("id", "position", "content");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        return saveData.run(Map.of(
                "dataId", "RegularTask.updateTaskAssignment",
                "data", param
        ));
    }
}
