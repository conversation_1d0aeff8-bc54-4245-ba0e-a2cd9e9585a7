package com.sunsheen.fswp.SysMenuClass;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.hearken.dev.util.StringUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.util.*;

@Controller("SysMenuTreeEditComponent")
@BixComponentPackage(dirname = "编辑角色的菜单树", type = "SYSTEM")
@Slf4j
public class SysMenuTreeEditComponent extends ABaseComponent {

    @Autowired
    QueryDataForListComponent queryDataForList;

    @Override
    @Component(
            name = "SysMenuTreeEditComponent",
            memo = "编辑角色的菜单树"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "编辑角色的菜单树"
    )})
    @LogArgs
    public Object run(Map param) {
        // 参数校验
        param.putAll(DBUtil.getLimitPageParams(param));
        param.put("dataId","SysMenu.SysMenuTree");
        List<Map> tempDataList = (List)this.queryDataForList.run(param);
        System.out.println("tempDataList = " + tempDataList);

        // 获取当前角色的菜单列表
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("dataId", "SysRole.selectMenuByRole");
        queryMap.put("roleId", param.get("roleId"));
        List<Map<String, String>> menuIds = (List<Map<String, String>>) this.queryDataForList.run(queryMap);
        System.out.println("menuIds = " + menuIds);
        // 将 menuList 转换为直接的列表
        List<Object> menuList = new ArrayList<>();
        menuIds.forEach(menuItem -> {
            String menuId = menuItem.get("menuId");
            System.out.println("menuId = " + menuId);
            ObjectMapper objectMapper = new ObjectMapper();
            // 将字符串转换为数组
            if (menuId != null) {
                // 如果 menuId 是字符串，尝试将其转换为 List
                try {
                    List<Long> list = objectMapper.readValue(menuId, new TypeReference<List<Long>>() {});
                    menuList.addAll(list);
                } catch (Exception e) {
                    log.error("menuId 转换失败: {}", menuId, e);
                }
            } else {
                log.warn("menuId 类型不符合预期: {}", menuId);
            }
        });
        //将menuList有的id进行标记，将tempDataList中对应的元素添加 checked 属性

        // 为每个 tempDataList 中的元素添加 checked 属性,menuList有的id标记为 true，没有的标记为 false
        tempDataList.forEach(tempItem -> {
            Object menuId = tempItem.get("menuId");
            boolean isChecked = menuId instanceof Long && menuList.contains(menuId);
            System.out.println("菜单的checked状态 = " + isChecked+ ", menuId = " + tempItem.get("menuId"));
            tempItem.put("checked", isChecked);
        });
        System.out.println("tempDataList after adding menuList = " + tempDataList);

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("code",1);
        resMap.put("data",tempDataList);
        if(StringUtil.isEmpty((String)param.get("menuName")) && StringUtil.isEmpty((String)param.get("menuId"))){
            handleMenuList(tempDataList);
        }
        System.out.println("resMap = " + resMap);
        return resMap;
    }


    private void  handleMenuList(List<Map> menuList){
        // 只处理visible=1的菜单
        menuList.removeIf(menuItem -> {
            Object visible = menuItem.get("visible");
            return visible == null || !"1".equals(visible.toString());
        });

        menuList.stream().forEach((menuItem)->{
            Long menuId = (Long)menuItem.get("menuId");
            ArrayList<Map> childs = new ArrayList<>();
            menuList.stream().forEach((item)->{
                if((Long)item.get("parentId") == menuId){
                    childs.add(item);
                }
            });
            childs.stream().sorted((v1,v2)->{
                return (int)v2.get("orderNum") - (int)v1.get("orderNum");
            });
            menuItem.put("childs",childs);
        });
        Iterator<Map> iter = menuList.iterator();
        while(iter.hasNext()){
            Map item = iter.next();
            if((Long)item.get("parentId") != 0 ){
                iter.remove();
            }
        }
        menuList.stream().sorted((m1,m2)->{
        return (int)m2.get("orderNum") - (int)m1.get("orderNum");
        });
    }

}
