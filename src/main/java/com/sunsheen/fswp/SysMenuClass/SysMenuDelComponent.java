package com.sunsheen.fswp.SysMenuClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.hearken.dev.service.datasource.SaveDataComponent;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("SysMenuDelComponent")
@BixComponentPackage(dirname = "删除菜单", type = "SYSTEM")
@Slf4j
public class SysMenuDelComponent extends ABaseComponent {

    @Autowired
    SaveDataComponent saveData;

    @Autowired
    QueryDataForListComponent queryForlist;

    @Override
    @Component(
            name = "SysMenuDelComponent",
            memo = "菜单删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "菜单id"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        // 参数校验
        List<String> args = Arrays.asList("menuId");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        //  获取索要删除的菜单项以及其下所有的子菜单的id列表
        Map<String, String> dataId = Map.of("dataId", "SysMenu.selectMenu");
        List<Map> menus = (List<Map>)this.queryForlist.run(dataId);
        Long menuDelId = ((Integer)param.get("menuId")).longValue();
        List<Long> subMenuIds = getSubMenuIds(menus,menuDelId);
        subMenuIds.add(menuDelId);

        HashMap<String, Object> delMap = new HashMap<>();
        delMap.put("dataId", "SysMenu.delMenu");
        delMap.put("data",Map.of("menuIds",subMenuIds));

        return saveData.run(delMap);
    }


    private List<Long>  getSubMenuIds(List<Map> menuList,Long menuDelId){
        menuList.stream().forEach((menuItem)->{
            Long menuId = (Long)menuItem.get("menuId");
            ArrayList<Map> childs = new ArrayList<>();
            menuList.stream().forEach((item)->{
                if((Long)item.get("parentId") == menuId){
                    childs.add(item);
                }
            });
            menuItem.put("childs",childs);
        });

        Iterator<Map> iter = menuList.iterator();
        while(iter.hasNext()){
            Map item = iter.next();
            if((Long)item.get("parentId") != 0 ){
                iter.remove();
            }
        }

        List<Map> targetTreeMenus = getSubMenu(menuList, menuDelId);
        return getAllSubMenuIds(targetTreeMenus);

    }

    private List<Map> getSubMenu( List<Map> menuList, Long menuDelId){
        List<Map> targetMenuList = new ArrayList<>();
        for (Map subMenu : menuList) {
            if((Long)subMenu.get("menuId") == menuDelId){
                targetMenuList =   (List<Map>)subMenu.get("childs");
                break;
            }else{
                targetMenuList =  getSubMenu((List<Map>)subMenu.get("childs"),menuDelId);
            }
        }
        return targetMenuList;
    }

    private List<Long> getAllSubMenuIds(List<Map> targetMenuList){
        List<Long> menuids = new ArrayList<>();
        for (Map subMenu : targetMenuList) {
            if(!((ArrayList<Map>) subMenu.get("childs")).isEmpty()){
                menuids.addAll(getAllSubMenuIds((List<Map>) subMenu.get("childs")));
            }
            else{
                menuids.add((Long)subMenu.get("menuId"));
            }
        }
        return menuids;

    }

}
