package com.sunsheen.fswp.SysMenuClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.hearken.dev.util.StringUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("SysMenuSelComponent")
@BixComponentPackage(dirname = "查询菜单列表", type = "SYSTEM")
@Slf4j
public class SysMenuSelComponent extends ABaseComponent {

    @Autowired
    QueryDataForListComponent queryDataForList;

    @Override
    @Component(
            name = "SysMenuSelComponent",
            memo = "菜单列表查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "查询菜单参数"
    )})
    @LogArgs
    public Object run(Map param) {
        // 参数校验

        param.putAll(DBUtil.getLimitPageParams(param));

        param.put("dataId","SysMenu.selectMenu");

        List<Map> tempDataList = (List)this.queryDataForList.run(param);
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("code",1);
        resMap.put("data",tempDataList);
        if(StringUtil.isEmpty((String)param.get("menuName")) && StringUtil.isEmpty((String)param.get("menuId"))){
            handleMenuList(tempDataList);
        }

        return resMap;
    }
    
    
    private void  handleMenuList(List<Map> menuList){

        menuList.stream().forEach((menuItem)->{
            Long menuId = (Long)menuItem.get("menuId");
            ArrayList<Map> childs = new ArrayList<>();
            menuList.stream().forEach((item)->{
                if((Long)item.get("parentId") == menuId){
                    childs.add(item);
                }
            });
            childs.stream().sorted((v1,v2)->{
                return (int)v2.get("orderNum") - (int)v1.get("orderNum");
            });
            menuItem.put("childs",childs);
        });
        Iterator<Map> iter = menuList.iterator();
        while(iter.hasNext()){
            Map item = iter.next();
            if((Long)item.get("parentId") != 0 ){
                iter.remove();
            }
        }
        menuList.stream().sorted((m1,m2)->{
        return (int)m2.get("orderNum") - (int)m1.get("orderNum");
        });
    }

}
