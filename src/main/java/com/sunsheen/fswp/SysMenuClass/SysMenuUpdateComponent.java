package com.sunsheen.fswp.SysMenuClass;


import cn.hutool.core.date.DateTime;
import com.mysql.cj.util.StringUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("SysMenuUpdateComponent")
@BixComponentPackage(dirname = "修改菜单", type = "SYSTEM")
@Slf4j
public class SysMenuUpdateComponent extends ABaseComponent {

    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "SysMenuUpdateComponent",
            memo = "新增菜单"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        // 参数校验
        List<String> args = Arrays.asList("menuId");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });
        String menuName = (String) param.get("menuName");

            if(!StringUtils.isEmptyOrWhitespaceOnly(menuName)){
                Map<String, Object> queryByNameMap = new HashMap<>();
                queryByNameMap.put("dataId", "SysMenu.selectMenuLock");
                queryByNameMap.put("menuId",  param.get("menuId"));
                queryByNameMap.put("menuName", menuName);
                List<Object> menuList = (List<Object>) this.queryForList.run(queryByNameMap);
                if (menuList.size() > 0 || !menuList.isEmpty()) {
                    throw new RuntimeException("修改的菜单不能重名！");
                }
            }

            param.put("updateTime",new DateTime());
            HashMap<String, Object> saveMap = new HashMap<>();
            saveMap.put("dataId", "SysMenu.updateMenu");
            saveMap.put("data", param);
            return this.saveData.run(saveMap);


    }

}
