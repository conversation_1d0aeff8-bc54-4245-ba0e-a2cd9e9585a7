package com.sunsheen.fswp.SysMenuClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("SysMenuInsertComponent")
@BixComponentPackage(dirname = "新增菜单", type = "SYSTEM")
@Slf4j
public class SysMenuInsertComponent extends ABaseComponent {

    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    QueryDataForListComponent queryForList;

    @Override
    @Component(
            name = "SysMenuInsertComponent",
            memo = "新增菜单"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        // 参数校验
        List<String> args = Arrays.asList("menuName", "parentName", "parentId", "orderNum", "path",
                "routeName","menuType","visible","icon");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

//        try{
            Map queryByNameMap = new HashMap();
            queryByNameMap.put("dataId", "SysMenu.selectMenuLock");
            queryByNameMap.put("menuName",param.get("menuName"));
            List<Map<String,Map>> menuList = (List<Map<String, Map>>) this.queryForList.run(queryByNameMap);
            if (menuList.size()>0 || !menuList.isEmpty()) {
                throw new RuntimeException("新增菜单不能重名！");
            }

            return this.saveData.run(Map.of(
                    "dataId", "SysMenu.insertMenu",
                    "data", param
            ));

//        }catch(Exception e ){
//            System.out.println(e);
//           throw new RuntimeException("保存失败，请稍后重试！");
//        }
    }


}
