package com.sunsheen.fswp.ProjectProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.TimeUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Map;

@Controller("ProjectProgressSelectWeekComponent")
@BixComponentPackage(dirname = "项目进度查询(week)", type = "SYSTEM")
@Slf4j
public class ProjectProgressSelectWeekComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "ProjectProgressSelectWeekComponent",
            memo = "项目进度查询(week)"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {

        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            try {
                int page = Integer.parseInt(pageV);
                int pageSize = Integer.parseInt(pageSizeV);
                Integer start = (page - 1) * pageSize;
                Integer limit = pageSize;
                param.put("start", start);
                param.put("limit", limit);
            } catch (Exception e) {
                log.error("page和pageSize非法");
                throw new RuntimeException("page和pageSize非法");
            }
        }
        String logAccount = UserUtil.getLogAccount();
        if(StringUtil.isEmpty(logAccount)){
            throw new RuntimeException("未能获取用户账号，请登录！");
        }
        param.put("reporterId",logAccount);

        param.put("dataId", "ProjectWork.countSelectProjectProgressByTime");
        String startTime = TimeUtil.getStartOfLastMonday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = TimeUtil.getStartOfThisMonday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        param.put("startTime", startTime);
        param.put("endTime", endTime);

        log.info("上周一 {}", startTime);
        log.info("本周一 {}", endTime);

        Long count = (Long) queryForObject.run(param);
        param.put("dataId", "ProjectWork.selectProjectProgressByTime");

        return Map.of(
                "count", count,
                "data", queryForList.run(param)
        );

    }


}
