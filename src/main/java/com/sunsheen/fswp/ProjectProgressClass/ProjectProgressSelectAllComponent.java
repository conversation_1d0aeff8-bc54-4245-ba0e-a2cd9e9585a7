package com.sunsheen.fswp.ProjectProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.fswp.util.TimeUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Map;

@Controller("ProjectProgressSelectAllComponent")
@BixComponentPackage(dirname = "项目进度查询", type = "SYSTEM")
@Slf4j
public class ProjectProgressSelectAllComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "ProjectProgressSelectAllComponent",
            memo = "项目进度查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {
        String logAccount = UserUtil.getLogAccount();
         if(StringUtil.isNullOrEmpty(logAccount)){
             throw new RuntimeException("用户未登录！");
         }

         //        处理 项目填报-》上次填报内容-》子任务进展   项目经理能查看所有上周的填报
        if(param.containsKey("type") && "week".equals((String)param.get("type"))){
            if(!param.containsKey("projectId")){
                throw  new RuntimeException("缺少项目参数！");
            }
            Map<String, Object> queryParam = Map.of("id", param.get("projectId"),
                    "ownerId",logAccount,
                    "dataId","RegularTask.countSelectProjectTask");
            if((Long)queryForObject.run(queryParam)== 0L){
                return Map.of(
                        "count", 0,
                        "data", new ArrayList<>()
                );
            }

            String startTime = TimeUtil.getStartOfLastMonday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String endTime = TimeUtil.getStartOfThisMonday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            param.put("startTime", startTime);
            param.put("endTime", endTime);
        }

        Map<String, Object> limitPageParams = DBUtil.getLimitPageParams(param);
        param.putAll(limitPageParams);

        param.put("dataId", "ProjectWork.countSelectProjectProgress");
        Long count = (Long) queryForObject.run(param);
        param.put("dataId", "ProjectWork.selectProjectProgress");

        return Map.of(
                "count", count,
                "data", queryForList.run(param)
        );

    }
}
