package com.sunsheen.fswp.ProjectProgressClass;

import com.sunsheen.fswp.TaskProgressClass.RegularTaskUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("ProjectProgressInsertComponent")
@BixComponentPackage(dirname = "项目进度提交", type = "SYSTEM")
@Slf4j
public class ProjectProgressInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Autowired
    RegularTaskUtils regularTaskUtils;

    @Component(
            name = "ProjectProgressInsertComponent",
            memo = "项目进度提交"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @Override
    @LogArgs
    @Transactional
    public Object run(Map param) {
        String loginAccount = UserUtil.getLogAccount();
        if(StringUtil.isNullOrEmpty(loginAccount)){
            throw new RuntimeException("用户未登录！");
        }

        // 参数校验
        List<String> args = Arrays.asList("progressName", "projectId", "lastWeekPlan", "completion",
                "gapReason", "nextWeekPlan", "currentProgress", "progressPercent", "deviationPercent", "weekNumber");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        // 合法性校验
        Long projectId = Long.valueOf(this.getCallParam(param, "projectId").toString());
        Map accountInfo = (Map) queryForMap.run(Map.of(
                "dataId", "RegularTask.queryAccount",
                "loginAccount", loginAccount
        ));

        if (!regularTaskUtils.isOwner(projectId, loginAccount) && !regularTaskUtils.isParticipant(projectId, loginAccount)) {
            log.error("{} 用户不属于该任务", loginAccount);
            throw new RuntimeException(loginAccount + " 用户不属于该任务");
        }

        param.put("createdAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        param.put("reporterId", loginAccount);
        param.put("reporterName", accountInfo.get("userName"));

        return saveData.run(Map.of(
                "dataId", "ProjectWork.insertProjectProgress",
                "data", param
        ));
    }
}