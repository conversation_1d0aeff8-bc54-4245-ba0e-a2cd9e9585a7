package com.sunsheen.fswp.ProjectProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller("ProjectProgressUpdateComponent")
@BixComponentPackage(dirname = "项目进度更新", type = "SYSTEM")
@Slf4j
public class ProjectProgressUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @LogArgs
    @Component(name = "ProjectProgressUpdateComponent", memo = "项目进度更新")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "更新数量")})
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("id", "progressName", "lastWeekPlan", "completion", "gapReason", "nextWeekPlan", "currentProgress", "progressPercent", "deviationPercent", "weekNumber");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        Map ProjectProgressInfo = (Map) queryForMap.run(Map.of("dataId", "ProjectWork.selectProjectProgress", "id", this.getCallParam(param, "id")));

        if (ProjectProgressInfo == null || ProjectProgressInfo.isEmpty()) {
            log.error("任务进度不存在");
            throw new RuntimeException("任务进度不存在");
        }

        return saveData.run(Map.of("dataId", "ProjectWork.updateProjectProgress", "data", param));
    }
}
