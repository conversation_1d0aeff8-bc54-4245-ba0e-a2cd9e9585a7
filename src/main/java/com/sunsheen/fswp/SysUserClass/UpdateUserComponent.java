package com.sunsheen.fswp.SysUserClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: chentong
 * date: 2025/3/20
 * 学校信息维护页面-用户更新业务层
 */

@Controller("UpdateUserComponent")
@BixComponentPackage(dirname = "用户角色更新", type = "SYSTEM")
@Slf4j
public class UpdateUserComponent extends ABaseComponent {
    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;



    @Override
    @Component(
            name = "updateRoleComponent",
            memo = "修改角色及菜单权限"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        HashMap hashMap = new HashMap();
        ArrayList<Map> listData = new ArrayList<>();

        // 获取前端传入data数据(用户信息列表[用户名、角色])
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) param.get("data");
        // HashMap<String,Object> dataMap = (HashMap<String, Object>) param.get("data");
        for (Map<String, Object> dataMap : dataList) {

            dataMap.put("userId", dataMap.get("nickName"));
            Map<String, Object> tempMap = new HashMap<>();

            // 用户单角色的更新
            // Integer selectRoleUserMap;
            // // 如果sys_role_user表中没有该用户的角色信息，则进行插入操作
            // Map<String, Object> selectMap = new HashMap<>();
            // selectMap.put("dataId", "SysRole.selectRoleByUser");
            // selectMap.put("userId", dataMap.get("nickName"));
            // List<Map> roleUserList = (List<Map>) queryForList.run(selectMap);
            // // System.out.println("roleUserList = " + roleUserList);
            // if (roleUserList != null && roleUserList.size() > 0) {
            //     selectRoleUserMap = 1; // 表示该用户已经有角色信息
            // } else {
            //     selectRoleUserMap = 0; // 表示该用户没有角色信息
            // }
            //
            // Integer saveRoleUserMap = 0;
            // if (selectRoleUserMap == 0) {
            //     Map<String, Object> insertMap = new HashMap<>();
            //     // 如果是第一次分配角色，则在表 sys_role_user 中插入一条记录
            //     insertMap.put("dataId", "SysRole.insertRoleByUserId");
            //     insertMap.put("data", dataMap);
            //     saveRoleUserMap = (Integer) saveData.run(insertMap);
            // } else {// 如果不是第一次分配角色，进行sys_role_user 表的更新操作
            //     Map<String, Object> roleMap = new HashMap<>();
            //     roleMap.put("dataId", "SysRole.updateRoleByUserId");
            //     roleMap.put("data", dataMap);
            //     saveRoleUserMap = (Integer) saveData.run(roleMap);
            // }
            //
            // Map<String, Object> updateuserMap = new HashMap<>();
            // //更新sys_user表
            // updateuserMap.put("dataId", param.get("dataId"));
            // updateuserMap.put("data", dataMap);
            //
            // Integer saveDataMap = (Integer) saveData.run(updateuserMap);

            //
            // if (saveDataMap != null && saveDataMap > 0 && saveRoleUserMap != null && saveRoleUserMap > 0) {
            //     hashMap.put("result", "用户角色信息更新成功！");
            // } else {
            //     hashMap.put("result", "用户角色信息更新失败，请检查！");
            // }


            // 多角色的修改 （先删除原有角色，在插入新角色）
            List<Map<String, Object>> roleList = (List<Map<String, Object>>) dataMap.get("role");
            List<String> roleNames = new ArrayList<>();
            List<String> roleIds = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();

            if (roleList != null) {
                for (Map<String, Object> role : roleList) {
                    if (role.get("roleName") != null) {
                        roleNames.add((String) role.get("roleName"));
                    }
                    if (role.get("roleId") != null) {
                        roleIds.add(role.get("roleId").toString());
                    }
                }
            }
            try {
                dataMap.put("roleName", objectMapper.writeValueAsString(roleNames));
            } catch (JsonProcessingException e) {
                dataMap.put("roleName", "[]");
                log.error("角色名序列化失败", e);
            }

            // 删除用户原有角色信息 sys_role_user
            Map<String, Object> deleteRoleMap = new HashMap<>();
            deleteRoleMap.put("dataId", "SysRole.deleteUserRole");
            deleteRoleMap.put("data", dataMap);
            Integer deleteRoleUser = (Integer) saveData.run(deleteRoleMap);


            // 批量插入用户新角色信息 sys_role_user
            boolean allSuccess = true;  // 标志位，判断是否所有角色都成功添加
            for (String roleId : roleIds) {
                Map<String, Object> insertRoleMap = new HashMap<>();
                insertRoleMap.put("dataId", "SysRole.insertRoleByUserId");

                Map<String, Object> roleData = new HashMap<>();
                roleData.put("nickName", dataMap.get("nickName"));
                roleData.put("roleId", roleId);

                insertRoleMap.put("data", roleData);
                Integer saveRoleUserMap = (Integer) saveData.run(insertRoleMap);
                if (saveRoleUserMap == null || saveRoleUserMap <= 0) {
                    allSuccess = false; // 如果有一个角色添加失败，则标志位为false
                }
            }

            // 更新用户信息 sys_user
            Map<String, Object> updateUserMap = new HashMap<>();
            updateUserMap.put("dataId", param.get("dataId"));
            updateUserMap.put("data", dataMap);
            Integer updateUse = (Integer) saveData.run(updateUserMap);

            if (updateUse != null && updateUse > 0 && deleteRoleUser != null && deleteRoleUser > 0 && allSuccess) {
                hashMap.put("result", "用户角色信息更新成功！");
            } else {
                hashMap.put("result", "用户角色信息更新失败，请检查！");
            }
        }

        listData.add(hashMap);
        return listData;
    }
}
