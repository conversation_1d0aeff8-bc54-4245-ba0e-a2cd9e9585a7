package com.sunsheen.fswp.SysUserClass;

import cn.hutool.core.util.ObjUtil;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("GetColleaguesByUserAccountComponent")
@BixComponentPackage(dirname = "查询同部门用户", type = "SYSTEM")
@Slf4j
public class GetColleaguesByUserAccountComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;
    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;
    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "GetColleaguesByUserAccountComponent",
            memo = "查询同部门用户"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Override
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();

        String account = UserUtil.getLogAccount();
        Map<String, Object> runParam = new HashMap<>();
        runParam.put("nickName", account);
        runParam.put("dataId", "SysUser.selectDeptIdByUser");
        Map<String, Object> tempDataMap = (Map<String, Object>) this.queryForMap.run(runParam);
        String deptId = tempDataMap.get("deptId").toString();
        HashMap<String, Object> tempParam = new HashMap<>();
//        if(roleName == "") {
//            tempParam.put("dataId", "SysUser.selectAllNickNameAndUserName");
//        }
        tempParam.put("dataId", "SysUser.selectCountNickNameAndUserNameByDeptId");
        tempParam.put("deptId", deptId);
        Long count = (Long) queryForObject.run(tempParam);
        tempParam.put("dataId", "SysUser.selectNickNameAndUserNameByDeptId");
        //判断是否需要分页，默认不需要
        tempParam.putAll(DBUtil.getLimitPageParams(param));
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) this.queryForList.run(tempParam);
        result.put("data", dataList);
        result.put("count", count);

        return result;
    }


}
