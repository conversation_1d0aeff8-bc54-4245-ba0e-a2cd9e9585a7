package com.sunsheen.fswp.SysUserClass;

import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门列表查询
 */
@Controller("SelectDeptListComponent")
@BixComponentPackage(dirname = "部门信息查询", type = "SYSTEM")
@Slf4j
public class SelectDeptListComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "selectDeptListComponent",
            memo = "部门信息查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})

    public Object run(Map param) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map> deptList = (List<Map>) this.queryForList.run(param);

        resultMap.put("data", deptList);

        if (!(deptList.size() > 0)) {
            try {
                throw new RuntimeException("查询不到相关部门信息！");
            } catch (RuntimeException e) {
                HashMap<String, Object> exceptionMap = new HashMap<>();
                exceptionMap.put("code", 0);
                exceptionMap.put("message", e.getMessage());
                resultMap.putAll(exceptionMap);
            }
        } else {
            resultMap.put("code", 2);
        }
        return resultMap;
    }
}