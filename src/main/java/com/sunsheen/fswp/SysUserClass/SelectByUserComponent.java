package com.sunsheen.fswp.SysUserClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: chentong
 * date: 2025/3/20
 * 用户信息维护页面-根据用户名查询数据
 */
@Controller("SelectByUserComponent")
@BixComponentPackage(dirname = "根据用户名搜索", type = "SYSTEM")
@Slf4j
public class SelectByUserComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "SelectUserComponent",
            memo = "根据用户名搜索"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        // 结果集
        HashMap<String,Object> resultMap = new HashMap<>();


        // 分页大小
        Integer pageSize = Integer.valueOf((String) param.get("pageSize"));
        Integer page = Integer.valueOf((String) param.get("page"));
        String username = (String) param.get("userName");
        String nickname = (String) param.get("nickName");
        String dept = (String) param.get("dept");
        HashMap<String,Object> tempDataMap = new HashMap<>();

        //查询数量
        HashMap<String,Object> countMap = new HashMap<>();

        countMap.put("dataId","SysUser.selectByUserCount");
        countMap.put("userName",username);
        countMap.put("nickName",nickname);
        countMap.put("dept",dept);

        List<Map> countList = (List<Map>) this.queryForList.run(countMap);
        Map<String,Object> counts = countList.get(0);

        // 查询用户信息
        List<Map> tempDataList = (List) this.queryForList.run(param);
        param.put("dataId","SysUser.selectByUser");
        param.put("start", (page - 1) * pageSize);
        param.put("limit", pageSize);
        tempDataList = (List) this.queryForList.run(param);
        if (!(tempDataList.size()>0)) {
            resultMap.put("code", 0);
            resultMap.put("message", "查询不到该用户");
        }else {
            resultMap.put("code", 1);
        }

        resultMap.putAll(counts);
        resultMap.put("data",tempDataList);
        return resultMap;
    }
}
