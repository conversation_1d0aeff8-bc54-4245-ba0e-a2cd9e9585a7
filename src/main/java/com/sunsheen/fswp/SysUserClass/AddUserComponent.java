package com.sunsheen.fswp.SysUserClass;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户新增
 */
@Controller("AddUserComponent")
@BixComponentPackage(dirname = "用户新增", type = "SYSTEM")
@Slf4j
public class AddUserComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Override
    @Component(
            name = "AddUserComponent",
            memo = "新增用户"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        HashMap<String, Object> result = new HashMap<>();
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) param.get("data");

        for (Map<String, Object> dataMap : dataList) {
             // 遍历角色数组，实现用户多角色
            List<Map<String, Object>> roleList = (List<Map<String, Object>>) dataMap.get("role");

            // 新增用户 sys_user
            Map<String, Object> insertUserMap = new HashMap<>();
            insertUserMap.put("dataId", "SysUser.addUser");

            // 拼接 rolaName json ["",""]
            ObjectMapper objectMapper = new ObjectMapper();
            String roleName = "[]";
            if (roleList != null && !roleList.isEmpty()) {
                List<String> roleNames = roleList.stream()
                    .map(r -> (String) r.get("roleName"))
                    .filter(n -> n != null)
                    .toList();
                try {
                    roleName = objectMapper.writeValueAsString(roleNames); // 将角色名称列表转换为 JSON 字符串
                } catch (Exception e) {
                    log.error("Error converting role names to JSON", e);
                }
            }
            dataMap.put("roleName", roleName); // 将角色名称添加到dataMap中
            insertUserMap.put("data", dataMap);
            Integer userResult = (Integer) saveData.run(insertUserMap);


            boolean allSuccess = true;  // 标志位，判断是否所有角色都成功添加
            if (roleList != null) {
                for (Map<String, Object> role : roleList) {
                    // 新增用户角色 sys_role_user
                    Map<String, Object> roleMap = new HashMap<>();
                    roleMap.put("dataId", "SysRole.insertRoleByUserId");

                    Map<String, Object> roleData = new HashMap<>();
                    roleData.put("nickName", dataMap.get("nickName"));
                    roleData.put("roleId", role.get("roleId"));

                    roleMap.put("data", roleData);
                    Integer roleResult = (Integer) saveData.run(roleMap);
                    if (roleResult == null || roleResult <= 0) {
                        allSuccess = false;
                    }
                }
            }

            //
            // // 新增用户角色 sys_role_user
            // Map<String, Object> insertRoleMap = new HashMap<>();
            // insertRoleMap.put("dataId", "SysRole.insertRoleByUserId");
            // insertRoleMap.put("data", dataMap);
            // Integer roleResult = (Integer) saveData.run(insertRoleMap);

            if (userResult != null && userResult > 0 && allSuccess) {
                result.put("result", "用户新增成功！");
            } else {
                result.put("result", "用户新增失败，请检查！");
            }
        }
        return result;
    }
}