package com.sunsheen.fswp.SysUserClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Author: jingky
 * date: 2025/7/2
 */

@Controller("SelectUserLogComponent")
@BixComponentPackage(dirname = "用户登录登出日志展示", type = "SYSTEM")
@Slf4j
public class SelectUserLogComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "SelectUserComponent",
            memo = "用户登录登出日志展示"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> resultMap = new HashMap<>();
        // 分页大小
        Integer pageSize = Integer.valueOf((String) param.get("pageSize"));
        Integer page = Integer.valueOf((String) param.get("page"));
        String username = (String) param.get("userName");
        String nickname = (String) param.get("nickName");
        String dept = (String) param.get("dept");
        String type = (String) param.get("type");
        HashMap<String,Object> tempDataMap = new HashMap<>();
        // 查询用户日志信息
        List<Map> tempDataList = new ArrayList<>();
        param.put("dataId","SysUserLog.selectUserLog");
        param.put("userName",username);
        param.put("nickName",nickname);
        param.put("dept",dept);
        param.put("type",type);
        param.put("start", (page - 1) * pageSize);
        param.put("limit", pageSize);
        tempDataList = (List) this.queryForList.run(param);
        // 查询数据总条数
        HashMap<String,Object> countMap = new HashMap<>();
        countMap.put("dataId","SysUserLog.selectUserLogCount");
        countMap.put("userName",username);
        countMap.put("nickName",nickname);
        countMap.put("dept",dept);
        countMap.put("type",type);

        List<Map> countList = (List<Map>) this.queryForList.run(countMap);
        Map<String,Object> counts = countList.get(0);
        resultMap.put("data",tempDataList);
        resultMap.putAll(counts);
        resultMap.put("code",2);
        return resultMap;
    }
}
