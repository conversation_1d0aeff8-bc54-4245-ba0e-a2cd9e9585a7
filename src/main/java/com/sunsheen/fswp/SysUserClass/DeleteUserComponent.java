package com.sunsheen.fswp.SysUserClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("DeleteUserComponent")
@BixComponentPackage(dirname = "用户信息删除", type = "SYSTEM")
@Slf4j
public class DeleteUserComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "DeleteUserComponent",
            memo = "用户信息删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "用户id-nickName"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) param.get("data");
        for (HashMap<String, Object> map : data) {
            // 删除用户角色绑定信息
            HashMap<String, Object> roleUserMap = new HashMap<>();
            roleUserMap.put("data", map);
            roleUserMap.put("dataId", "SysRole.deleteUserRole"); // 删除 sys_role_user 表信息
            Integer deleteUserRoleResult = (Integer) saveData.run(roleUserMap);

            // 删除用户本身
            HashMap<String, Object> userMap = new HashMap<>();
            userMap.put("data", map);
            userMap.put("dataId", "SysUser.deleteUser"); // 删除 sys_user 表信息
            Integer deleteUserResult = (Integer) saveData.run(userMap);

            if (deleteUserRoleResult != null && deleteUserRoleResult > 0 && deleteUserResult != null && deleteUserResult > 0) {
                resultMap.put("code", 1);
                resultMap.put("message", "用户删除成功");
            } else {
                resultMap.put("message", "用户删除失败");
            }

        }
        return resultMap;
    }
}
