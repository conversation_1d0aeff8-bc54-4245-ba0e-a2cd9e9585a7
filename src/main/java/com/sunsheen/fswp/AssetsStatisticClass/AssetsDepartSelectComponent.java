package com.sunsheen.fswp.AssetsStatisticClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("AssetsDepartSelectComponent")
@BixComponentPackage(dirname = "部门查询", type = "SYSTEM")
@Slf4j
public class AssetsDepartSelectComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "AssetsDepartSelectComponent",
            memo = "部门查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> resultMap = new HashMap<>();
        // 获取参数
        HashMap<String,Object> tempDataMap = new HashMap<>();
        tempDataMap.put("dataId", "AssetsCateDep.selectDep");
        // 查询业务流程信息
        List<Map<String,Object>> tempDataList = (List) this.queryForList.run(tempDataMap);
        resultMap.put("data",tempDataList);
        resultMap.put("code",2);
        return resultMap;
    }
}
