package com.sunsheen.fswp.aop;

import lombok.extern.slf4j.Slf4j;
import org.apereo.cas.client.authentication.AttributePrincipal;
import org.apereo.cas.client.util.AbstractCasFilter;
import org.apereo.cas.client.validation.Assertion;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import jakarta.servlet.http.HttpServletRequest;

import java.lang.reflect.Method;
import java.util.Arrays;

@Aspect
@Component
@Slf4j
public class LogParamsAspect {

    @Pointcut("@annotation(LogArgs)")
    public void logArgs() {
    }

    // 在方法执行前打印日志
    @Before("logArgs()")
    public void logMethodParams(org.aspectj.lang.JoinPoint joinPoint) {

        // 获取用户名
        // 这里可以根据实际情况获取用户名，例如从安全上下文中获取
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        Assertion assertion = null;
        String loginName = "anonymous";
        if (request != null) {
            assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
            if (assertion != null) {
                AttributePrincipal principal = assertion.getPrincipal();
                loginName = principal.getName();
            }
        }

        // 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();

        // 获取方法参数
        Object[] args = joinPoint.getArgs();

        // 获取类名
        String className = joinPoint.getTarget().getClass().getName();

        // 打印用户名 类名、方法名和参数
        log.info("User: " + loginName);
        log.info("Class: " + className);
        log.info("Method: " + method.getName());
        log.info("Parameters: " + Arrays.toString(args));
    }

    // 在方法执行后打印返回值
    @AfterReturning(pointcut = "logArgs()", returning = "result")
    public void logMethodReturnValue(Object result) {
        // 打印方法的返回值
        log.info("Return value: " + result);
    }
}
