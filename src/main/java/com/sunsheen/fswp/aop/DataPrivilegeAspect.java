package com.sunsheen.fswp.aop;


import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Aspect
@Component
@Slf4j
public class DataPrivilegeAspect {
//    校领导  系统管理员  资产管理有查看所有数据的权限
    static Set<String> superDataAccessList = Set.of("SYS_ADMIN", "SYS_ZCGL", "SYS_XLD","SYS_JW");
//      校领导  系统管理员  资产管理 二级部门领导 有查看数据的权限
    static Set<String> dataAccessList = Set.of("SYS_ADMIN", "SYS_ZCGL", "SYS_XLD","SYS-EJDWLD","SYS_JW");
//    二级部门领导有查看本部门数据权限
    static Set<String> deptDataAccessList = Set.of("SYS-EJDWLD");

    @Autowired
    private QueryDataForListComponent queryForListComp;

    @Pointcut("@annotation(CustomDataPrivilege)")
    public void DataPrivilegePointcut() {
    }

    // 在方法执行前打印日志
    @Around("DataPrivilegePointcut()")
    public Object dataPrevilegeArge(ProceedingJoinPoint joinPoint) throws Throwable {
        // 根据用户名获取角色
        String userId = (String) UserUtil.getLogAccount();

        Map<String, Object> querymap = new HashMap<>();
        querymap.put("dataId", "SysUser.selectDeptRoleNameByUserId");
        querymap.put("userId", userId);

        List<Map<String, Object>> userInfoListFromDb = (List<Map<String, Object>>)queryForListComp.run(querymap);
        if (!userInfoListFromDb.isEmpty()) {
            Map<String, Boolean> dataAccessCheckRes = checkRole(userInfoListFromDb);
            if(!dataAccessCheckRes.get("dataAccess")){
                HashMap<String, Object> errorMap = new HashMap<>();
                errorMap.put("code", 0);
                errorMap.put("message", "您没有查看数据权限");
                errorMap.put("data", new ArrayList<>());
                return errorMap;
            }
            Object[] args = joinPoint.getArgs();
            Map<String, Object> userInfoFromDb = userInfoListFromDb.get(0);
            Map<String,Object> paramMap = (HashMap<String,Object>)args[0];
            if(dataAccessCheckRes.get("deptAccess")){
                paramMap.put("userDept", userInfoFromDb.get("dept"));
            }
            if(dataAccessCheckRes.get("superDataAccess")){
                paramMap.put("userDept","all");
            }
            return joinPoint.proceed(args);
        }else{
            HashMap<String, Object> errorMap = new HashMap<>();
            errorMap.put("code", 0);
            errorMap.put("message", "您没有查看数据权限");
            errorMap.put("data", new ArrayList<>());
            return errorMap;
        }
    }


    private Map<String,Boolean> checkRole(List<Map<String, Object>> userInfoListFromDb){
        HashMap<String, Boolean> resMap = new HashMap<>();
        resMap.put("superDataAccess", false);
        resMap.put("dataAccess", false);
        resMap.put("deptAccess", false);
        for (Map<String, Object> userInfoMap : userInfoListFromDb) {
            if(superDataAccessList.contains(String.valueOf(userInfoMap.get("roleKey")))){
                resMap.put("superDataAccess", true);
            }
            if(dataAccessList.contains(String.valueOf(userInfoMap.get("roleKey")))){
                resMap.put("dataAccess", true);
            }
            if(deptDataAccessList.contains(String.valueOf(userInfoMap.get("roleKey")))){
                resMap.put("deptAccess", true);
            }
        }
        return resMap;
    }

}
