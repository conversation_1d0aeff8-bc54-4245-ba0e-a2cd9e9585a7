package com.sunsheen.fswp.SysRoleClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller("DeleteRoleComponent")
@BixComponentPackage(dirname = "角色信息删除", type = "SYSTEM")
@Slf4j
public class DeleteRoleComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "DeleteRoleComponent",
            memo = "角色信息删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "角色id"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        HashMap<String, Object> resultMap = new HashMap<>();
        // 删除角色信息
        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) param.get("data");
        for (HashMap<String, Object> map : data) {
            // 删除用户表的角色绑定信息（更新sys_user表的肉了Name信息 为空或者暂无角色分配）
            // 获取之前是这个角色的userList
            HashMap<String, Object> selectUserByRoleIdMap = new HashMap<>();
            selectUserByRoleIdMap.put("dataId", "SysRole.selectUserIdByRoleId");
            selectUserByRoleIdMap.put("roleId", map.get("roleId"));
            List<Map<String, Object>> userList = (List<Map<String, Object>>) queryForList.run(selectUserByRoleIdMap);
            for (Map<String, Object> user : userList) {
                // 更新sys_user表的roleName信息
                HashMap<String, Object> userMap = new HashMap<>();
                userMap.put("dataId", "SysRole.updateRoleByRoleName"); //修改 sys_user 表 roleName信息
                userMap.put("data", Map.of("nickName", user.get("userId"), "roleName", "暂无角色分配"));
                Integer saveUserMap = (Integer) saveData.run(userMap);
                if (saveUserMap == 0) {
                    log.error("更新用户角色信息失败，用户ID: {}", user.get("userId"));
                }
            }
            // 删除角色用户绑定信息
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", map);
            dataMap.put("dataId", "SysRole.deleteRoleUser"); //删除 sys_role_user 表信息
            saveData.run(dataMap);

            // 删除角色菜单绑定信息(菜单鉴权时，role_user表中该用户无角色绑定，默认无菜单权限)
            HashMap<String, Object> menuMap = new HashMap<>();
            menuMap.put("data", map);
            menuMap.put("dataId", "SysRole.deleteRoleMenu"); //删除 sys_role_menu 表信息
            saveData.run(menuMap);

            // 删除角色
            HashMap<String,Object> tempMap = new HashMap<>();
            tempMap.put("data", map);
            tempMap.put("dataId", "SysRole.deleteRole"); // 删除 sys_role 表信息
            saveData.run(tempMap);




        }
        return resultMap;
    }
}
