package com.sunsheen.fswp.SysRoleClass;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.util.*;

/**
 * 获取菜单信息
 */

@Controller("SelectMenuComponent")
@BixComponentPackage(dirname = "获取菜单信息", type = "SYSTEM")
@Slf4j
public class SelectMenuComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

//    public UserUtil userUtil;

    @SneakyThrows
    @Component(
            name = "SelectMenuComponent",
            memo = "获取菜单信息"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})

    @Override
    @LogArgs
    public Object run(Map param) {
        // 结果集
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map> tempDataList = new ArrayList<>();
        // 根据用户名获取角色
        String userId = (String) UserUtil.getLogAccount();
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("dataId", "SysRole.selectRoleByUser");
        List<LinkedCaseInsensitiveMap> roleList = (List<LinkedCaseInsensitiveMap>) queryForList.run(paramMap);
        Object roleId = (String) roleList.getFirst().get("roleId");
        if (roleId == null || "".equals(roleId)) {
            param.put("dataId", "SysRole.selectMenuList");
            tempDataList = (List) this.queryForList.run(param);
        }else {
            param.put("dataId", "SysRole.selectMenuByRole");
            param.put("roleId", roleId);
            List menuList = (List<?>) queryForList.run(param);
            LinkedCaseInsensitiveMap menuId = (LinkedCaseInsensitiveMap) menuList.getFirst();
            String menuIds = (String) menuId.get("menuId");
            ObjectMapper mapper = new ObjectMapper();
            List<Integer> numbers = mapper.readValue(menuIds, new TypeReference<List<Integer>>() {});
            // ,获取菜单详细信息然后转换为List<HashMap>
            for (Integer num : numbers) {
                HashMap<String, Object> tempData = new HashMap<>();
                tempData.put("dataId", "SysRole.selectMenuList");
                tempData.put("menuId", num);
                List<Map> resultMenus = (List<Map>) queryForList.run(tempData);
                tempDataList.add(resultMenus.getFirst());
            }
        }

        // 转为树形结构
        // 创建映射表，用于快速查找节点
        Map<Integer, Map<String, Object>> nodeMap = new HashMap<>();

        // 首先将所有数据项存入映射表
        for (Map<String, Object> item : tempDataList) {
            int menuId = (int) item.get("menuId");
            nodeMap.put(menuId, item);
        }

        // 构建树结构
        List<Map<String, Object>> tree = new ArrayList<>();
        for (Map<String, Object> item : tempDataList) {
                Integer parentId = (Integer) item.get("parentId");

                if (parentId==0 || !nodeMap.containsKey(parentId)) {
                    // 如果没有父节点或父节点不存在，则添加到根树
                    tree.add(item);
                } else {
                    // 如果有父节点，则添加到父节点的children列表中
                    Map<String, Object> parent = nodeMap.get(parentId);
                    if (parent != null) {
                        // 确保父节点有children列表
                        if (!parent.containsKey("children")) {
                            parent.put("children", new ArrayList<Map<String, Object>>());
                        }
                        List<Map<String, Object>> children = (List<Map<String, Object>>) parent.get("children");
                        children.add(item);
                    }
                }
            }

        // 对树中的每个节点及其子节点按orderNum排序
        sortTree(tree);

        resultMap.put("data", tree);
        resultMap.put("code", 2);
        return resultMap;
    }

    private static void sortTree(List<Map<String, Object>> nodes) {
           if (nodes == null || nodes.isEmpty()) {
            return;
        }

        // 对当前层级节点按orderNum排序
        Collections.sort(nodes, Comparator.comparingInt(a -> (int) a.get("orderNum")));

        // 递归排序每个节点的子节点
        for (Map<String, Object> node : nodes) {
            if (node.containsKey("children")) {
                List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
                sortTree(children);
            }
        }
    }
}
