package com.sunsheen.fswp.SysRoleClass;

import cn.hutool.core.date.DateTime;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForListComponent;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("UpdateRoleComponent")
@BixComponentPackage(dirname = "角色信息更新", type = "SYSTEM")
@Slf4j
public class UpdateRoleComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;


    @Override
    @Component(
            name = "UpdateRoleComponent",
            memo = "角色信息更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "角色id"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {
        HashMap hashMap = new HashMap();
        ArrayList<Map> listData = new ArrayList<>();

        // 获取前端传入data数据(用户名、角色)
        HashMap<String,Object> dataMap = (HashMap<String, Object>) param.get("data");
        dataMap.put("createTime",new DateTime());
        Map<String,Object> tempMap = new HashMap<>();
        tempMap.put("dataId","SysRole.updateRole");
        tempMap.put("data",dataMap);
        Integer saveDataMap = (Integer) saveData.run(tempMap);
        // 是否更新菜单
        Integer saveMenuMap = null;
        if (dataMap.containsKey("menuList")) {
                Map<String,Object> menuMap = new HashMap<>();
                ArrayList<Map> menuList = (ArrayList<Map>) dataMap.get("menuList");
                dataMap.put("menuList",menuList.toString());
                menuMap.put("dataId","SysRole.updateRoleMenu");
                menuMap.put("data",dataMap);
                saveMenuMap = (Integer) saveData.run(menuMap);
        }

        // 更新sys_user 表的角色绑定信息
        // 获取之前是这个角色的userList
        HashMap<String, Object> selectUserByRoleIdMap = new HashMap<>();
        selectUserByRoleIdMap.put("dataId", "SysRole.selectUserIdByRoleId");
        selectUserByRoleIdMap.put("roleId", dataMap.get("roleId"));
        List<Map<String, Object>> userList = (List<Map<String, Object>>) queryForList.run(selectUserByRoleIdMap);
        for (Map<String, Object> user : userList) {
            // 更新sys_user表的roleName信息
            HashMap<String, Object> userMap = new HashMap<>();
            userMap.put("dataId", "SysRole.updateRoleByRoleName"); //修改 sys_user 表 roleName信息
            userMap.put("data", Map.of("nickName", user.get("userId"), "roleName", dataMap.get("roleName")));
            Integer saveUserMap = (Integer) saveData.run(userMap);
            if (saveUserMap == 0) {
                log.error("更新用户角色信息失败，用户ID: {}", user.get("userId"));
            }
        }

        if (!saveDataMap.equals(0)&&(!saveMenuMap.equals(0))) {
            hashMap.put("result", "用户角色信息更新成功！");
        }else {
            hashMap.put("result", "用户角色信息更新失败，请检查！");
        }
        listData.add(hashMap);
        return listData;
    }
}
