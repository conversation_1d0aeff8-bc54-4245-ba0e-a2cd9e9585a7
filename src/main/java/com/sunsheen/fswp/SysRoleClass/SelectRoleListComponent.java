package com.sunsheen.fswp.SysRoleClass;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 所有角色查询展示
 */

@Controller("SelectRoleListComponent") 
@BixComponentPackage(dirname = "学校信息查询", type = "SYSTEM")
@Slf4j
public class SelectRoleListComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "selectRoleListComponent",
            memo = "角色信息查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        // 结果集
        HashMap<String,Object> resultMap = new HashMap<>();
        param.put("dataId","SysRole.selectRoleList");
        List<Map> tempDataList = (List)this.queryForList.run(param);

        // 遍历 tempDataList，根据每个 roleId 查询对应的 menuList
        tempDataList.forEach(tempItem -> {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("dataId", "SysRole.selectMenuByRole");
            queryMap.put("roleId", tempItem.get("roleId"));

            // 查询 menuList
            List<Map<String, String>> menuIds = (List<Map<String, String>>) this.queryForList.run(queryMap);
            List<Object> menuList = new ArrayList<>();
            menuIds.forEach(menuItem -> {
                String menuId = menuItem.get("menuId");
                if (menuId != null) {
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        List<Long> list = objectMapper.readValue(menuId, new TypeReference<List<Long>>() {});
                        menuList.addAll(list);
                    } catch (Exception e) {
                        log.error("menuId 转换失败: {}", menuId, e);
                    }
                } else {
                    log.warn("menuId 类型不符合预期: {}", menuId);
                }
            });

            // 将 menuList 添加到 tempItem
            tempItem.put("menuList", menuList);
        });

        resultMap.put("data",tempDataList);

        if (!(tempDataList.size() >0)) {
            try {
                throw new RuntimeException("查询不到相关角色信息！");
            }catch (RuntimeException e) {
                HashMap<String, Object> exceptionMap = new HashMap<>();
                exceptionMap.put("code", 0);
                exceptionMap.put("message", e.getMessage());
                resultMap.putAll(exceptionMap);
            }
        }else{
            resultMap.put("code",2);
        }
        return resultMap;
    }
}
