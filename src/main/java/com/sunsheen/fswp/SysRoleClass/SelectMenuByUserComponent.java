package com.sunsheen.fswp.SysRoleClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import dm.jdbc.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据用户获取菜单信息
 */

@Controller("SelectMenuByUserComponent")
@BixComponentPackage(dirname = "获取菜单信息", type = "SYSTEM")
@Slf4j
public class SelectMenuByUserComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

//    public UserUtil userUtil;

    @SneakyThrows
    @Component(
            name = "SelectMenuByUserComponent",
            memo = "获取菜单信息"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})

    @Override
    @LogArgs
    public Object run(Map param) {
        // 结果集
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map> tempDataList = new ArrayList<>();
        List<Map<String, Object>> tree = new ArrayList<>();

        // 根据用户名获取角色
        String userId = (String) UserUtil.getLogAccount();

        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("userId", userId);
            paramMap.put("dataId", "SysRole.selectRoleByUser");
            List<LinkedCaseInsensitiveMap> roleList = (List<LinkedCaseInsensitiveMap>) queryForList.run(paramMap);

            List<String> roleIdList = roleList.stream()
                    .map(v ->  String.valueOf(v.get("roleId")))
                    .collect(Collectors.toList());

            if (!roleIdList.isEmpty()) {
                param.put("dataId", "SysRole.selectMenuByRoles");
                param.put("roleIds", roleIdList);
                List<LinkedCaseInsensitiveMap> menuList = (List<LinkedCaseInsensitiveMap>) queryForList.run(param);

                List<Long> numbers = new ArrayList<>();
                ObjectMapper mapper = new ObjectMapper();

                menuList.forEach(v -> {
                            try {
                                List<Long> menuId = mapper.readValue(String.valueOf(v.get("menuId")), new TypeReference<List<Long>>() {});
                                numbers.addAll(menuId);
                            } catch (JsonProcessingException e) {
                               log.error(SelectMenuByUserComponent.class.getName() + "转化menuId失败--：", e);
                            }
                        });

                HashMap<String, Object> tempData = new HashMap<>();
                tempData.put("dataId", "SysMenu.selectMenuInList");
                tempData.put("menuIds", numbers);
                List<Map> resultMenus = (List<Map>) queryForList.run(tempData);
                tempDataList.addAll(resultMenus);
            }

            // 转为树形结构
            // 创建映射表，用于快速查找节点
            Map<Long, Map<String, Object>> nodeMap = new HashMap<>();

            // 首先将所有数据项存入映射表
            for (Map<String, Object> item : tempDataList) {
                Long menuId = (Long) item.get("menuId");
                nodeMap.put(menuId, item);
            }

            // 构建树结构
            for (Map<String, Object> item : tempDataList) {
                Long parentId = (Long) item.get("parentId");

                if (parentId == 0) {
                    // 如果没有父节点或父节点不存在，则添加到根树
                    tree.add(item);
                } else {
                    // 如果有父节点，则添加到父节点的children列表中
                    Map<String, Object> parent = nodeMap.get(parentId);
                    if (parent != null) {
                        // 确保父节点有children列表
                        if (!parent.containsKey("children")) {
                            parent.put("children", new ArrayList<Map<String, Object>>());
                        }
                        List<Map<String, Object>> children = (List<Map<String, Object>>) parent.get("children");
                        children.add(item);
                    }
                }
            }

            // 对树中的每个节点及其子节点按orderNum排序
            sortTree(tree);

            resultMap.put("data", tree);
            resultMap.put("code", 2);
            return resultMap;
        }catch(Exception e){
            log.error(e.getStackTrace().toString());
            resultMap.put("data", tree);
            resultMap.put("code", 2);
            return resultMap;
        }
    }

    private static void sortTree(List<Map<String, Object>> nodes) {
           if (nodes == null || nodes.isEmpty()) {
            return;
        }

        // 对当前层级节点按orderNum排序
        Collections.sort(nodes, Comparator.comparingInt(a -> (int) a.get("orderNum")));

        // 递归排序每个节点的子节点
        for (Map<String, Object> node : nodes) {
            if (node.containsKey("children")) {
                List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
                sortTree(children);
            }
        }
    }
}
