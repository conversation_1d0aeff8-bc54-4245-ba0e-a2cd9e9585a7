package com.sunsheen.fswp.SysRoleClass;

import cn.hutool.core.date.DateTime;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.hearken.dev.dao.jform.config.MultiTransactional;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;


/**
 * 新增角色及菜单权限
 */

@Controller("InsertRoleComponent")
@BixComponentPackage(dirname = "角色信息新增", type = "SYSTEM")
@Slf4j
public class InsertRoleComponent extends ABaseComponent {

    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Override
    @Component(
            name = "InsertRoleComponent",
            memo = "新增角色及菜单权限"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @MultiTransactional
    public Object run(Map param) {

        HashMap hashMap = new HashMap();
        ArrayList<Map> listData = new ArrayList<>();

        List<HashMap<String,Object>> dataList = (List<HashMap<String, Object>>) param.get("data");

        for (HashMap<String,Object> dataMap : dataList) {
            // 查询是否有同名角色
            HashMap tempData = new HashMap();
            tempData.put("dataId", "SysRole.selectRoleList");
            tempData.put("roleName", dataMap.get("roleName"));
            ArrayList<HashMap> roleNameList = (ArrayList<HashMap>)queryForList.run(tempData);
            if (roleNameList.size()>0 || !roleNameList.isEmpty()) {
                throw new RuntimeException("该角色名已存在！");
            }
            // 新增角色
            dataMap.put("delFlag","0");
            dataMap.put("createTime",new DateTime());
            Map<String,Object> tempMap = new HashMap<>();
            tempMap.put("dataId","SysRole.insertRole");
            tempMap.put("data",dataMap);
            saveData.run(tempMap);

            // 查询存入的角色id
            Map<String,Object> queryMap = new HashMap<>();
            queryMap.put("dataId","SysRole.selectRoleList");
            queryMap.put("roleName",dataMap.get("roleName"));
            List<Map<String,Integer>> tempDataList = (List<Map<String, Integer>>) this.queryForList.run(queryMap);
            String roleId = String.valueOf(tempDataList.get(0).get("roleId"));
            // 将角色id及其权限菜单存入
            List<Object> menuList = (List<Object>) dataMap.get("menuList");
            if (menuList != null && menuList.size() > 0) {
                    Map<String,Object> meunMap = new HashMap<>();
                    meunMap.put("menuId", menuList.toString());
                    meunMap.put("roleId",roleId);
                    Map<String,Object> roleMeunMap = new HashMap<>();
                    roleMeunMap.put("data",meunMap);
                    roleMeunMap.put("dataId","SysRole.insertRoleMeun");
                    saveData.run(roleMeunMap);
            }
        }
        hashMap.put("result", "角色信息保存成功！");
        listData.add(hashMap);
        return listData;
    }
}
