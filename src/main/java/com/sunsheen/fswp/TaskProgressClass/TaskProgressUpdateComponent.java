package com.sunsheen.fswp.TaskProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("TaskProgressUpdateComponent")
@BixComponentPackage(dirname = "常规任务更新", type = "SYSTEM")
@Slf4j
public class TaskProgressUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @LogArgs
    @Component(
            name = "TaskProgressUpdateComponent",
            memo = "常规任务更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "更新数量")})
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("id", "subTaskName", "workContent", "workResult", "issues");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        Map taskProgressInfo = (Map) queryForMap.run(Map.of(
                "dataId", "RegularTask.selectTaskProgress",
                "id", this.getCallParam(param, "id")
        ));

        if (taskProgressInfo == null || taskProgressInfo.isEmpty()) {
            log.error("任务进度不存在");
            throw new RuntimeException("任务进度不存在");
        }

        return saveData.run(Map.of(
                "dataId", "RegularTask.updateTaskProgress",
                "data", param
        ));
    }
}
