package com.sunsheen.fswp.TaskProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RegularTaskUtils {
    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @LogArgs
    public boolean isOwner(Long id, String loginName) {
        if (loginName == null) {
            return false;
        }
        Long isOwner = (Long) queryForObject.run(Map.of(
                "dataId", "RegularTask.countProjectTask",
                "refId", id,
                "loginAccount", loginName
        ));
        return isOwner > 0;
    }

    @LogArgs
    public boolean isParticipant(Long id, String loginName) {
        if (loginName == null) {
            return false;
        }
        Long isParticipant = (Long) queryForObject.run(Map.of(
                "dataId", "RegularTask.countTaskAssignment",
                "refId", id,
                "loginAccount", loginName
        ));
        return isParticipant > 0;
    }

    @LogArgs
    public boolean isReporter(Long id, String loginName) {
        if (loginName == null) {
            return false;
        }
        Long isReporter = (Long) queryForObject.run(Map.of(
                "dataId", "RegularTask.countSelectTaskProgress",
                "reporterId", loginName,
                "id", id
        ));
        return isReporter > 0;
    }

}
