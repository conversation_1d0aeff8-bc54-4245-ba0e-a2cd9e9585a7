package com.sunsheen.fswp.TaskProgressClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@BixComponentPackage(dirname = "常规任务进程添加", type = "SYSTEM")
@Controller("TaskProgressInsertComponent")
@Slf4j
public class TaskProgressInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Autowired
    RegularTaskUtils regularTaskUtils;

    @Component(name = "TaskProgressInsertComponent", memo = "常规任务进程添加")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @Transactional
    @LogArgs
    @Override
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("taskId", "subTaskName", "workContent", "workResult", "issues");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });

        // 校验合法性
        Long taskId = Long.valueOf(this.getCallParam(param, "taskId").toString());
        // 获取当前用户
        String loginAccount = UserUtil.getLogAccount();

        Map accountInfo = (Map) queryForMap.run(Map.of("dataId", "RegularTask.queryAccount", "loginAccount", loginAccount));

        if (!regularTaskUtils.isOwner(taskId, loginAccount) && !regularTaskUtils.isParticipant(taskId, loginAccount)) {
            log.error("{} 用户不属于该任务", loginAccount);
            throw new RuntimeException(loginAccount + " 用户不属于该任务");
        }

        LocalDateTime now = LocalDateTime.now();
        String dateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        param.put("createdAt", dateTime);
        param.put("reporterName", accountInfo.get("userName"));
        param.put("reporterId", loginAccount);

        return saveData.run(Map.of("dataId", "RegularTask.insertTaskProgress", "data", param));
    }
}
