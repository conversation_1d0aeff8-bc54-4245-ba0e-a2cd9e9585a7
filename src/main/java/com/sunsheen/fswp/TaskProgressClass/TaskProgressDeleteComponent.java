package com.sunsheen.fswp.TaskProgressClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("TaskProgressDeleteComponent")
@BixComponentPackage(dirname = "任务进度删除", type = "SYSTEM")
@Slf4j
public class TaskProgressDeleteComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @LogArgs
    @Component(
            name = "TaskProgressDeleteComponent",
            memo = "任务进度删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "更新数量")})
    @Override
    public Object run(Map param) {
        return saveData.run(Map.of(
                "dataId", "RegularTask.deleteTaskProgress",
                "data", param
        ));
    }
}
