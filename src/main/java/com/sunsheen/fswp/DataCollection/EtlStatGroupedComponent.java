package com.sunsheen.fswp.DataCollection;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("EtlStatGroupedComponent")
@BixComponentPackage(dirname = "数据采集组件", type = "BUSINESS")
public class EtlStatGroupedComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(EtlStatGroupedComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForListComponent;

    @Override
    @Component(name = "EtlStatGroupedComponent", memo = "获取ETL统计数据，按部门和表分组的复杂对象结构")
    @Params({})
    @Returns(retValue = {
        @ReturnItem(type = "java.util.Map", name = "result", comment = "按部门分组的ETL统计数据")
    })
    public Object run(Map param) {
        logger.info("查询ETL统计数据分组结构");

        try {
            return getEtlStatGroupedData();
        } catch (Exception e) {
            logger.error("查询ETL统计数据失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    private Map<String, Object> getEtlStatGroupedData() {
        // 查询原始数据
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "DataCollection.getEtlStatTableData");

        logger.debug("查询ETL统计原始数据");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rawData = (List<Map<String, Object>>) queryDataForListComponent.run(queryParams);

        if (rawData == null || rawData.isEmpty()) {
            logger.info("未查询到ETL统计数据");
            return Collections.emptyMap();
        }

        // 按部门分组处理数据
        Map<String, Object> result = new LinkedHashMap<>();

        for (Map<String, Object> record : rawData) {
            String deptName = (String) record.get("dept_name");
            String tableComment = (String) record.get("table_comment");
            Object counts = record.get("counts");
            Object updateTime = record.get("update_time");

            if (deptName == null) continue;

            // 获取或创建部门数据
            @SuppressWarnings("unchecked")
            Map<String, List<Map<String, Object>>> deptData = 
                (Map<String, List<Map<String, Object>>>) result.get(deptName);
            
            if (deptData == null) {
                deptData = new LinkedHashMap<>();
                result.put(deptName, deptData);
            }

            // 获取或创建表数据列表
            List<Map<String, Object>> tableDataList = deptData.get(tableComment);
            if (tableDataList == null) {
                tableDataList = new ArrayList<>();
                deptData.put(tableComment, tableDataList);
            }

            // 添加数据记录
            Map<String, Object> dataRecord = new HashMap<>();
            dataRecord.put("counts", counts);
            dataRecord.put("time", updateTime);
            tableDataList.add(dataRecord);
        }

        logger.info("ETL统计数据分组完成，共{}个部门", result.size());
        return result;
    }
}
