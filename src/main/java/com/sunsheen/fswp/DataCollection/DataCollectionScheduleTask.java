package com.sunsheen.fswp.DataCollection;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@BixComponentPackage(dirname = "自动统计", type = "BUSINESS")
public class DataCollectionScheduleTask {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    /**
     * 每小时执行一次资产教师统计任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @LogArgs
    public void executeWarningUpgradeTask() {
        try {
            log.info("开始执行资产教师统计任务");
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "DataCollection.updateAssetTeacherStats");
            Object result = queryForList.run(param);
            log.info("资产教师统计任务执行完成，结果: {}", result);

        } catch (Exception e) {
            log.error("资产教师统计任务执行失败", e);
        }
    }

    /**
     * 每小时执行一次科研统计任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @LogArgs
    public void executeKeyanTask() {
        try {
            log.info("开始执行科研统计任务");
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "DataCollection.updateKeyan");
            Object result = queryForList.run(param);
            log.info("科研统计任务执行完成，结果: {}", result);

        } catch (Exception e) {
            log.error("科研统计任务执行失败", e);
        }
    }
}
