package com.sunsheen.fswp.DataCollection;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("EtlStatTrendComponent")
@BixComponentPackage(dirname = "数据采集组件", type = "BUSINESS")
public class EtlStatTrendComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(EtlStatTrendComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForListComponent;

    @Override
    @Component(name = "EtlStatTrendComponent", memo = "获取数据集采集趋势统计，按日期统计")
    @Params({
        @ParamItem(name = "days", comment = "统计天数，默认30天", type = "java.lang.Integer")
    })
    @Returns(retValue = {
        @ReturnItem(type = "java.util.List", name = "result", comment = "每日数据采集趋势列表")
    })
    public Object run(Map param) {
        Integer days = parseInteger(param.get("days"));
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }

        logger.info("查询数据采集趋势，统计天数: {}", days);

        try {
            return getEtlStatDailyTrend(days);
        } catch (Exception e) {
            logger.error("查询数据采集趋势失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private List<Map<String, Object>> getEtlStatDailyTrend(Integer days) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "DataCollection.getEtlStatDailyTrend");
        queryParams.put("days", days);

        logger.debug("查询每日数据采集趋势，参数: {}", queryParams);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rawData = (List<Map<String, Object>>) queryDataForListComponent.run(queryParams);

        if (rawData == null || rawData.isEmpty()) {
            logger.info("未查询到数据采集趋势数据");
            return Collections.emptyList();
        }

        // 处理数据，确保返回格式一致
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> record : rawData) {
            Map<String, Object> trendData = new HashMap<>();
            trendData.put("date", record.get("date"));
            trendData.put("tableCount", record.get("table_count"));
            trendData.put("totalCounts", record.get("total_counts"));
            trendData.put("jobCount", record.get("job_count"));
            trendData.put("deptCount", record.get("dept_count"));
            result.add(trendData);
        }

        logger.info("数据采集趋势查询完成，共{}天数据", result.size());
        return result;
    }

    private Integer parseInteger(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
