package com.sunsheen.fswp.ProcessMonitoringLevelOneClass;

import cn.hutool.db.Db;
import com.sunsheen.fswp.aop.CustomDataPrivilege;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("WorkflowDataWithWarningsComponent")
@BixComponentPackage(dirname = "查询资产审批流程及预警数据", type = "BUSINESS")
public class WorkflowDataWithWarningsComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;
    @Autowired
    private QueryDataForObjectComponent queryForObjComp;

    @Override
    @Component(name = "WorkflowDataWithWarningsComponent", memo = "查询资产审批流程数据及关联的预警数据")
    @Params({})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回资产审批流程及预警数据")})
    @LogArgs
    @CustomDataPrivilege
    public Object run(Map param) {
        List<String> list = Arrays.asList("business_type","warning_level");
        list.forEach((v)->{
            if(!param.containsKey(v)){
               throw new RuntimeException("缺少参数--:"+v);
            }
        });
        Map<String, Object> limitPageParams = DBUtil.getLimitPageParams(param);
        Map<String, Object> queryParams = new HashMap<>(limitPageParams);
        queryParams.putAll(param);

//        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryAllWorkflowDataWithWarnings");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.queryAllWorkflowDataWithWarnings");
        List<Map<String,Object>> warningDataList = (List<Map<String,Object>>)queryForList.run(queryParams);
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.countAllWorkflowDataWithWarnings");
        Long count = (Long) queryForObjComp.run(queryParams);

        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("data", warningDataList);
        resMap.put("count", count);
        resMap.put("code", 1);
        return resMap;
    }
}


