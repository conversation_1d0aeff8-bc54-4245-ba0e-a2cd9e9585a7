package com.sunsheen.fswp.ProcessMonitoringLevelOneClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("WarningDetComponent")
@BixComponentPackage(dirname = "预警详情查询", type = "BUSINESS")
public class WarningDetailsComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "WarningDetailsComponent", memo = "查询资产审批流程与预警数据的关联字段")
    @Params({})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的预警数据")})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryWarningDetails");

        return queryForList.run(queryParams);
    }
}

