package com.sunsheen.fswp.ProcessMonitoringLevelOneClass;

import com.sunsheen.fswp.aop.CustomDataPrivilege;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("ProcessMonitoringComponent")
@BixComponentPackage(dirname = "流程监控", type = "BUSINESS")
public class ProcessMonitoringComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "ProcessMonitoringComponent", memo = "流程监控功能")
    @Params({
            @ParamItem(type = "java.lang.String", name = "startTime", comment = "查询开始时间")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "data", comment = "返回完整流程监控数据")})
    @LogArgs
    @CustomDataPrivilege
    public Object run(Map param) {
        String startTime = (String) this.getCallParam(param, "startTime");

        Map<String, Object> queryParams = new HashMap<>();
        if (startTime != null && !startTime.trim().isEmpty()) {
            queryParams.put("startTime", startTime);
        }
        queryParams.putAll(param);

        // 查询所有流程类型
//        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryBusinessTypes");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.queryBusinessTypes");
        List<Map<String, Object>> businessTypes = (List<Map<String, Object>>) queryForList.run(queryParams);

        // 查询申请提交（所有流程总数）
//        queryParams.put("dataId", "ProcessMonitoringLevelOne.querySubmittedProcesses");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.querySubmittedProcesses");
        List<Map<String, Object>> submittedProcesses = (List<Map<String, Object>>) queryForList.run(queryParams);

        // 查询进行中的流程数
//        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryOngoingProcesses");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.queryOngoingProcesses");
        List<Map<String, Object>> ongoingProcesses = (List<Map<String, Object>>) queryForList.run(queryParams);

        // 查询已完成的流程数
//        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryCompletedProcesses");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.queryCompletedProcesses");
        List<Map<String, Object>> completedProcesses = (List<Map<String, Object>>) queryForList.run(queryParams);

        // 查询预警数据
//        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryWarnings");
        queryParams.put("dataId", "ProcessMonitoringLevelOne_Pri.queryWarnings");
        List<Map<String, Object>> warnings = (List<Map<String, Object>>) queryForList.run(queryParams);

        // 整理最终结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("businessTypes", businessTypes);
        resultMap.put("submittedProcesses", submittedProcesses);
        resultMap.put("ongoingProcesses", ongoingProcesses);
        resultMap.put("completedProcesses", completedProcesses);
        resultMap.put("warnings", warnings);

        return resultMap;
    }
}

