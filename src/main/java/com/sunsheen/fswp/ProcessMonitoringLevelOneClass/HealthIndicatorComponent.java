package com.sunsheen.fswp.ProcessMonitoringLevelOneClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("HealthIndicatorComponent")
@BixComponentPackage(dirname = "计算健康指标", type = "BUSINESS")
public class HealthIndicatorComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "HealthIndicatorComponent", memo = "计算全校健康指标")
    @Params({})
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "data", comment = "返回健康指标计算结果")})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> queryParams = new HashMap<>();

        // 查询参数的当前部门
        String currentDepartment = (String) param.get("department_name");
        if (!"全校".equals(currentDepartment)) {
            queryParams.put("department_name", currentDepartment);
        }
        // 1. 查询全校监测指标（总数据条数）
        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryTotalMonitoringIndicator");

        List<Map<String, Object>> totalIndicatorResult = (List<Map<String, Object>>) queryForList.run(queryParams);
        Number totalIndicatorNumber = totalIndicatorResult.isEmpty() ? 0 : (Number) totalIndicatorResult.getFirst().get("total_indicator");
        int totalIndicator = totalIndicatorNumber.intValue();

        // 2. 查询预警数据
        queryParams.put("dataId", "ProcessMonitoringLevelOne.queryWarningCount");
        List<Map<String, Object>> warningResult = (List<Map<String, Object>>) queryForList.run(queryParams);
        // 获取总警告数
        Number totalWarningsNumber = warningResult.isEmpty() ? 0 : (Number) warningResult.getFirst().get("total_warnings");
        int totalWarnings = totalWarningsNumber != null ? totalWarningsNumber.intValue() : 0;

// 获取一级警告数
        Number warningLevel1Number = warningResult.isEmpty() ? 0 : (Number) warningResult.getFirst().get("warning_level_1");
        int warningLevel1 = warningLevel1Number != null ? warningLevel1Number.intValue() : 0;

// 获取二级警告数
        Number warningLevel2Number = warningResult.isEmpty() ? 0 : (Number) warningResult.getFirst().get("warning_level_2");
        int warningLevel2 = warningLevel2Number != null ? warningLevel2Number.intValue() : 0;
        // 3. 计算健康指标（排除预警数据）
        int healthIndicator = totalIndicator - totalWarnings;

        // 4. 计算健康度
        double healthScore = totalIndicator > 0 ? (double) healthIndicator / totalIndicator : 0.00;

        // 5. 组织返回数据
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalIndicator", totalIndicator);
        resultMap.put("totalWarnings", totalWarnings);
        resultMap.put("warningLevel1", warningLevel1);
        resultMap.put("warningLevel2", warningLevel2);
        resultMap.put("healthIndicator", healthIndicator);
        resultMap.put("healthScore", healthScore);

        return resultMap;
    }

}


