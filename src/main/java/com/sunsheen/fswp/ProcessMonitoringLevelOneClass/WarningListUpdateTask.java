package com.sunsheen.fswp.ProcessMonitoringLevelOneClass;

import java.util.HashMap;
import java.util.Map;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@BixComponentPackage(dirname = "预警更新", type = "BUSINESS")
public class WarningListUpdateTask {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort queryForList;

    /**
     * 每天凌晨执行一次预警更新检查
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天午夜执行一次
    @LogArgs
    public void executeWarningListUpdateTask() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "WarningListUpdate.callWarningListUpdate");
            queryForList.run(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预警更新任务失败", e);
        }
    }

    /**
     * 每天凌晨执行一次异常数据捕获
     */
    @Scheduled(cron = "0 10 0 * * ?") // 每天午夜 00:10 执行
    @LogArgs
    public void executeCaptureAssetExceptionsTask() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "WarningListUpdate.callCaptureAssetExceptions");
            queryForList.run(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("执行异常数据捕获任务失败", e);
        }
    }

    /**
     * 每天凌晨执行一次警告列表更新
     */
    @Scheduled(cron = "0 20 0 * * ?") // 每天午夜 00:20 执行
    @LogArgs
    public void executeUpdateWarningListTask() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("dataId", "WarningListUpdate.callUpdateWarningList");
            queryForList.run(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("执行警告列表更新任务失败", e);
        }
    }
}

