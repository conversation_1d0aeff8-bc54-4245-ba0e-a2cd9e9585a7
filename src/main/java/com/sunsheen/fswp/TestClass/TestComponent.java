package com.sunsheen.fswp.TestClass;

import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Map;
import com.sunsheen.hearken.dev.service.rest.vo.GridDataObject;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;


/**
 * 表格数据源
 */
@Controller("TestComponent")
@BixComponentPackage(dirname = "平台构件", type = "SYSTEM")
public class TestComponent extends ABaseComponent {
    //表格所有数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    //表格数据条数
    @Autowired
    @Qualifier("QueryCountComponent")
    IDataPort queryCount;

    //表格统计
    //TODO，需要修改
//    @Autowired
//    @Qualifier("DataBaseQueryForAllPageStat")
//    IDataPort queryForAllPageStat;

    @Override
    @Component(name = "TestComponent", memo = "表格数据源查询，支持关系型数据库及MyBatis",type = "data")
    @Params({
            @ParamItem(type = "java.lang.String", name = "dataId", comment = "数据源id"),
            @ParamItem(type = "java.lang.Object...", name = "params", comment = "数据源查询参数")})
    @Returns(retValue = {@ReturnItem(type = "com.sunsheen.hearken.dev.service.rest.vo.GridDataObject", name = "data", comment = "返回查询结果")})
    public Object run(Map param) {
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) this.queryForList
                .run(param);


//        String allPageStat = (String) this.getCallParam(param, "allPageStat");
//        // 生成统计数据
//        if ("true".equals(allPageStat) && queryForAllPageStat != null) {
//            Map<String, Object> allPageStatData = (Map<String, Object>) queryForAllPageStat
//                    .run(param);
//            gdc.setAllPageStatData(allPageStatData);
//        }
        return dataList;
    }


}
