package com.sunsheen.fswp;

//import com.sunsheen.jfids.das.spring.registry.client.annotation.EnableRegistryClient;
//CAS认证依赖包
import org.apereo.cas.client.boot.configuration.EnableCasClient;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;



//执行包就放开，依赖包就注释
//CAS认证
@EnableCasClient
@SpringBootApplication(scanBasePackages = {"com.sunsheen"})
@EnableTransactionManagement//开始注解版事务
@MapperScan("com.sunsheen.fswp.mapper")
@ComponentScan(basePackages = "com.sunsheen")
public class ArchetypeDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(ArchetypeDemoApplication.class, args);
    }

}
