package com.sunsheen.fswp.SchoolManageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: chentong
 * date: 2025/3/18
 * 学校信息维护页面-数据查询业务层
 */

@Controller("SchoolQueryComponent")
@BixComponentPackage(dirname = "学校信息查询", type = "SYSTEM")
@Slf4j
public class SchoolQueryComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "SchoolQueryComponent",
            memo = "学校信息查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> resultMap = new HashMap<>();
        // 分页大小
        List<Map> tempDataList = (List)this.queryForList.run(param);
        // 查询数据总条数
        HashMap<String,Object> countMap = new HashMap<>();
        countMap.put("dataId","SchoolManage.selectCounts");
        List<Map> countList = (List<Map>) this.queryForList.run(countMap);
        Map<String,Object> counts = countList.get(0);
        resultMap.put("data",tempDataList);
        resultMap.putAll(counts);
        resultMap.put("code",2);
        return resultMap;
    }
}
