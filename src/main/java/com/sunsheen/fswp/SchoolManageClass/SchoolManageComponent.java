package com.sunsheen.fswp.SchoolManageClass;

import com.google.protobuf.ServiceException;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Author: chentong
 * date: 2025/3/18
 * 学校信息维护页面-数据保存业务层
 */

@Controller("SchoolManageComponent")
@BixComponentPackage(dirname = "学校信息录入", type = "SYSTEM")
@Slf4j
public class SchoolManageComponent extends ABaseComponent {
    public static String ID = "ID";
    public static String NAME = "name";

    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Override
    @Component(
            name = "SaveDataComponent",
            memo = "学校信息存入"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {

        log.info("学校信息导入参数" + param.toString());
        ArrayList<Map> listData = new ArrayList<>();
        HashMap hashMap = new HashMap();

        // 获取前端参数中的data
        HashMap<String,String> schoolMessageMap = (HashMap<String, String>) this.getCallParam(param, "data");
        // 获取参数中的ID及name
        String idStr = schoolMessageMap.get(ID);
        String nameStr = schoolMessageMap.get(NAME);

        // 校验该学校是否已经在数据库中存在
        Map<String,String> paraMap = new HashMap();
        paraMap.put("ID",idStr);
        paraMap.put("name",nameStr);
        paraMap.put("dataId","SchoolManage.selectById");
        List dataList = (List)queryForList.run(paraMap);
        if (dataList.size()>0) {
            throw new ServiceException("当前id已经存在，请检查ID是否有效！");
        } else {
            // 学校信息存入数据库
            HashMap<String, Object> insertSchoolMap = new HashMap<>();
            insertSchoolMap.put("dataId", "SchoolManage.insertSchool");
            insertSchoolMap.put("data", schoolMessageMap);
            saveData.run(insertSchoolMap);
            // 返回存入成功信息
            hashMap.put("result", "学校信息保存成功！");
            listData.add(hashMap);
        }
        return listData;
    }
}
