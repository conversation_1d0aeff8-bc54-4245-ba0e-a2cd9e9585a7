package com.sunsheen.fswp.SchoolManageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Author: chentong
 * date: 2025/3/18
 * 学校信息维护页面-数据删除业务层
 */

@Controller("SchoolDeleteComponent")
@BixComponentPackage(dirname = "学校信息删除", type = "SYSTEM")
@Slf4j
public class SchoolDeleteComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Override
    @Component(
            name = "SaveDataComponent",
            memo = "学校信息删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "com.sunsheen.hearken.dev.service.rest.vo.GridDataObject", name = "data", comment = "返回查询结果")})
    @LogArgs
    public Object run(Map param) {
        ArrayList<Map> listData = new ArrayList<>();
        Map<String,String> schoolIdMap = (Map<String, String>) param.get("data");
        String schoolId = schoolIdMap.get("ID");
        HashMap<String, String> deleteSchoolMap = new HashMap<>();
        deleteSchoolMap.put("schoolId", schoolId);
        HashMap<String, Object> deleteSchoolReturnMap = new HashMap<>();
        deleteSchoolReturnMap.put("dataId", "SchoolManage.deleteSchool");
        deleteSchoolReturnMap.put("data", deleteSchoolMap);
        Integer isDelete = (Integer) saveData.run(deleteSchoolReturnMap);
        HashMap hashMap = new HashMap();
        if (isDelete == 1) {
            hashMap.put("result","学校信息删除成功！");
            listData.add(hashMap);
        }
        else{
            hashMap.put("result","学校信息删除失败！");
            listData.add(hashMap);
        }
        return listData;
    }
}
