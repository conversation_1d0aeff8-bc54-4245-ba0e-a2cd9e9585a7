package com.sunsheen.fswp.SchoolManageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Controller("SchoolUpdateComponent")
@BixComponentPackage(dirname = "学校信息更新", type = "SYSTEM")
@Slf4j
public class SchoolUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Component(
            name = "SchoolUpdateComponent",
            memo = "学校信息更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})

    @Override
    @LogArgs
    public Object run(Map param) {
        HashMap hashMap = new HashMap();
        Map<String,Object> tempMap = new HashMap<>();
        tempMap.put("dataId",param.get("dataId"));
        tempMap.put("data",param.get("data"));
        Integer saveDataMap = (Integer) saveData.run(tempMap);
        if (!saveDataMap.equals(0)){
            hashMap.put("result", "学校信息更新成功！");
        }else {
            hashMap.put("result", "学校信息更新失败，请检查！");
        }
        return hashMap;
    }
}
