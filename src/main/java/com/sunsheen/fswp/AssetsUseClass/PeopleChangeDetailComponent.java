package com.sunsheen.fswp.AssetsUseClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("PeopleChangeDetailComponent")
@BixComponentPackage(dirname = "人员变动及部门变动明细", type = "SYSTEM")
@Slf4j
public class PeopleChangeDetailComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "PeopleChangeDetailComponent",
            memo = "人员变动及部门变动明细"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> tempDataMap = new HashMap<>();
        HashMap<String,Object> countMap = new HashMap<>();
        HashMap<String,Object> resultMap = new HashMap<>();
        // 分页大小
        Integer pageSize = Integer.valueOf((String) param.get("pageSize"));
        Integer page = Integer.valueOf((String) param.get("page"));
        String cateCode = (String)param.get("cateCode");
        Integer start1 = (page - 1) * pageSize;
        Integer limit1 = pageSize;
        tempDataMap.put("start1", start1);
        tempDataMap.put("limit1", limit1);
        if (cateCode.equals("0")){
            tempDataMap.put("dataId", "PeopleChange.selectPeoChangeDetail");
            countMap.put("dataId","PeopleChange.selectPeoCounts");
        }else if (cateCode.equals("1")){
            tempDataMap.put("dataId", "PeopleChange.selectDepChangeDetail");
            countMap.put("dataId","PeopleChange.selectDepCounts");
        }
        List<Map<String,Object>> peopleList = (List<Map<String, Object>>) queryForList.run(tempDataMap);
        List<Map<String,Object>> countList = (List<Map<String, Object>>) queryForList.run(countMap);
        resultMap.put("data",peopleList);
        resultMap.putAll(countList.getFirst());
        resultMap.put("code","2");
        return resultMap;
    }
}
