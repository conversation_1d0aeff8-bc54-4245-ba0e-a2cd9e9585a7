package com.sunsheen.fswp.AssetsUseClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 展示变动人员总人数、总金额和总数量，明细
 */
@Controller("PeopleChangeComponent")
@BixComponentPackage(dirname = "人员变动及部门变动总览", type = "SYSTEM")
@Slf4j
public class PeopleChangeComponent extends ABaseComponent
{
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "PeopleChangeComponent",
            memo = "人员变动及部门变动总览"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> tempDataMap = new HashMap<>();
        HashMap<String,Object> resultMap = new HashMap<>();
        String cateCode = (String)param.get("cateCode");
        if (cateCode.equals("0")){
            tempDataMap.put("dataId", "PeopleChange.selectPeopleChange");
        }else if (cateCode.equals("1")){
            tempDataMap.put("dataId", "PeopleChange.selectDepartChange");
        }
        List<Map<String,Object>> peopleList = (List<Map<String, Object>>) queryForList.run(tempDataMap);
        resultMap.put("code","2");
        resultMap.put("data",peopleList);
        return resultMap;
    }
}
