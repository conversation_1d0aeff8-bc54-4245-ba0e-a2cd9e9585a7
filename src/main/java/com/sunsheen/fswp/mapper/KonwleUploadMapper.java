package com.sunsheen.fswp.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface KonwleUploadMapper extends BaseMapper {

    @Select("${sql}")
    List<HashMap<String,Object>> selectLawsBy(@Param("sql") String sql);
}

