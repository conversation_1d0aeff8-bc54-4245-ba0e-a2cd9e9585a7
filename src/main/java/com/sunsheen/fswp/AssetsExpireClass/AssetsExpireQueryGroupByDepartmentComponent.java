package com.sunsheen.fswp.AssetsExpireClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("AssetsExpireQueryGroupByDepartmentComponent")
@BixComponentPackage(dirname = "资产到期信息查询", type = "SYSTEM")
@Slf4j
public class AssetsExpireQueryGroupByDepartmentComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryDataForList;

    @SneakyThrows
    @Override
    @Component(
            name = "AssetsExpireQueryComponent",
            memo = "资产到期信息按部门分类查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "AssetsExpire.sumByDepartmentCode");
        paramMap.put("data", param);

        return queryDataForList.run(paramMap);
    }
}
