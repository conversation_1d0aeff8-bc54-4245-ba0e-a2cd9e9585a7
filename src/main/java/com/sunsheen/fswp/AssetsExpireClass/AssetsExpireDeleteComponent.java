package com.sunsheen.fswp.AssetsExpireClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("AssetsExpireDeleteComponent")
@BixComponentPackage(dirname = "资产到期信息删除", type = "SYSTEM")
@Slf4j
public class AssetsExpireDeleteComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Override
    @Component(
            name = "AssetsExpireDeleteComponent",
            memo = "资产到期信息删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.lang.Integer", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "AssetsExpire.deleteAssetsExpire");
        paramMap.put("data", param);

        Map<String, Object> resultMap = new HashMap<>();
        Integer code = (Integer) saveData.run(paramMap);
        if (code > 0) {
            resultMap.put("result", "删除" + code + "条数据");
        } else {
            throw new Exception("删除失败");
        }
        return resultMap;
    }
}
