package com.sunsheen.fswp.AssetsExpireClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("AssetsExpireQueryComponent")
@BixComponentPackage(dirname = "资产到期信息查询", type = "SYSTEM")
@Slf4j
public class AssetsExpireQueryComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort dataForList;

    @SneakyThrows
    @Override
    @Component(
            name = "AssetsExpireQueryComponent",
            memo = "资产到期信息查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {

        Map<String, Object> paramMap = new HashMap<>();

        // 查询参数
        String id = (String) this.getCallParam(param, "id");
        String department = (String) this.getCallParam(param, "department");
        String departmentCode = (String) this.getCallParam(param, "department_code");
        String name = (String) this.getCallParam(param, "name");
        String employeeId = (String) this.getCallParam(param, "employee_id");
        String employeeStatus = (String) this.getCallParam(param, "employee_status");
        String fiscalTypeCode = (String) this.getCallParam(param, "fiscal_type_code");
        String fiscalTypeName = (String) this.getCallParam(param, "fiscal_type_name");
        String educationTypeCode = (String) this.getCallParam(param, "education_type_code");
        String educationTypeName = (String) this.getCallParam(param, "education_type_name");
        String assetName = (String) this.getCallParam(param, "asset_name");
        String storageDate = (String) this.getCallParam(param, "storage_date");
        String service_months = (String) this.getCallParam(param, "service_months");

        paramMap.put("id", id);
        paramMap.put("department", department);
        paramMap.put("department_code", departmentCode);
        paramMap.put("name", name);
        paramMap.put("employee_id", employeeId);
        paramMap.put("employee_status", employeeStatus);
        paramMap.put("fiscal_type_code", fiscalTypeCode);
        paramMap.put("fiscal_type_name", fiscalTypeName);
        paramMap.put("education_type_code", educationTypeCode);
        paramMap.put("education_type_name", educationTypeName);
        paramMap.put("asset_name", assetName);
        paramMap.put("storage_date", storageDate);
        paramMap.put("service_months", service_months);

        paramMap.put("dataId", "AssetsExpire.selectAssetsExpireCount");

        Long total = (Long) ((List<Map>) dataForList.run(paramMap)).getFirst().get("count");
        paramMap.put("dataId", "AssetsExpire.selectAssetsExpire");

        // 分页参数
        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            int page = Integer.parseInt(pageV);
            int pageSize = Integer.parseInt(pageSizeV);
            Integer start = (page - 1) * pageSize;
            Integer limit = pageSize;
            paramMap.put("start", start);
            paramMap.put("limit", limit);
        }

        // 数据总条数
        val obj = (List<?>) dataForList.run(paramMap);

        Map<String, Object> result = new HashMap<>();
        result.put("data", obj);

        result.put("total", total);
        return result;
    }
}
