package com.sunsheen.fswp.AssetsExpireClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Controller("AssetsExpireInsertComponent")
@BixComponentPackage(dirname = "资产到期信息存入", type = "SYSTEM")
@Slf4j
public class AssetsExpireInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @SneakyThrows
    @Override
    @Component(
            name = "AssetsExpireInsertComponent",
            memo = "资产到期信息存入"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "AssetsExpire.insertAssetsExpire");
        paramMap.put("data", param);

        Integer code = (Integer) saveData.run(paramMap);
        Map<String, Object> result = new HashMap<>();
        if (code > 0) {
            result.put("result", "保存成功");
        } else {
            throw new Exception("保存失败");
        }
        return result;
    }
}
