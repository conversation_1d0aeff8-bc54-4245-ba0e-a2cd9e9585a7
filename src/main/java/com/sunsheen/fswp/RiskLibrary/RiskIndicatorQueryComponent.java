package com.sunsheen.fswp.RiskLibrary;

import java.util.HashMap;
import java.util.Map;

import com.sunsheen.fswp.aop.LogArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.stereotype.Service;

/**
 * 风险指标知识库查询构件
 */
@Controller("RiskIndicatorQueryComponent")
public class RiskIndicatorQueryComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(RiskIndicatorQueryComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryDataForListComponent;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryDataForMapComponent;

    @Override
    @Component(name = "RiskIndicatorQueryComponent", memo = "风险指标知识库分页查询构件")
    @Params({
            @ParamItem(type = "java.lang.String", name = "businessName", comment = "业务名称"),
            @ParamItem(type = "java.lang.String", name = "processName", comment = "流程名称"),
            @ParamItem(type = "java.lang.String", name = "subProcessName", comment = "子流程名称"),
            @ParamItem(type = "java.lang.String", name = "indicatorName", comment = "指标名称"),
            @ParamItem(type = "java.lang.Integer", name = "pageSize", comment = "每页记录数"),
            @ParamItem(type = "java.lang.Integer", name = "offset", comment = "起始记录索引")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "list", comment = "查询结果列表"),
            @ReturnItem(type = "java.lang.Integer", name = "total", comment = "总记录数"),
            @ReturnItem(type = "java.lang.Boolean", name = "success", comment = "操作是否成功")
    })
    @LogArgs
    public Object run(Map param) {
        // 获取参数并设置默认值
        String businessName = (String) this.getCallParam(param, "businessName");
        String processName = (String) this.getCallParam(param, "processName");
        String subProcessName = (String) this.getCallParam(param, "subProcessName");
        String indicatorName = (String) this.getCallParam(param, "indicatorName");
        Integer pageSize = 10;
        Integer offset = 0;

        if (param.containsKey("pageSize") && param.get("pageSize") != null) {
            try {
                pageSize = Integer.parseInt(param.get("pageSize").toString());
            } catch (NumberFormatException e) {
                logger.warn("pageSize参数格式错误，使用默认值10");
            }
        }
        if (param.containsKey("offset") && param.get("offset") != null) {
            try {
                offset = Integer.parseInt(param.get("offset").toString());
            } catch (NumberFormatException e) {
                logger.warn("offset参数格式错误，使用默认值0");
            }
        }
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询分页数据
            Map<String, Object> listParams = new HashMap<>();
            listParams.put("dataId", "RiskLibrary.queryKnowledgeIndicators");
            if (businessName != null && !businessName.isEmpty()) {
                listParams.put("businessName", businessName);
            }
            if (processName != null && !processName.isEmpty()) {
                listParams.put("processName", processName);
            }
            if (subProcessName != null && !subProcessName.isEmpty()) {
                listParams.put("subProcessName", subProcessName);
            }
            if (indicatorName != null && !indicatorName.isEmpty()) {
                listParams.put("indicatorName", indicatorName);
            }
            listParams.put("pageSize", pageSize);
            listParams.put("offset", offset);

            Object listResult = queryDataForListComponent.run(listParams);

            // 查询总记录数
            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "RiskLibrary.countKnowledgeIndicators");
            if (businessName != null && !businessName.isEmpty()) {
                countParams.put("businessName", businessName);
            }
            if (processName != null && !processName.isEmpty()) {
                countParams.put("processName", processName);
            }
            if (subProcessName != null && !subProcessName.isEmpty()) {
                countParams.put("subProcessName", subProcessName);
            }
            if (indicatorName != null && !indicatorName.isEmpty()) {
                countParams.put("indicatorName", indicatorName);
            }

            Map<String, Object> countResult = (Map<String, Object>) queryDataForMapComponent.run(countParams);
            int total = Integer.parseInt(countResult.get("total").toString());
            result.put("success", true);
            result.put("list", listResult);
            result.put("total", total);
            result.put("pageSize", pageSize);
            result.put("offset", offset);

            logger.info("风险指标查询成功，共{}条记录", total);

        } catch (Exception e) {
            logger.error("风险指标查询失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
}