package com.sunsheen.fswp.RiskLibrary;

import java.util.HashMap;
import java.util.Map;

import com.sunsheen.fswp.aop.LogArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;

/**
 * 更新知识指标构件
 */
@Controller("UpdateIndicatorComponent")
public class UpdateIndicatorComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(UpdateIndicatorComponent.class);

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveDataComponent;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryDataForMapComponent;

    @Override
    @Component(name = "UpdateIndicatorComponent", memo = "更新知识指标记录")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicator_id", comment = "指标ID"),
            @ParamItem(type = "java.lang.String", name = "indicator_name", comment = "指标名称"),
            @ParamItem(type = "java.lang.String", name = "describe", comment = "指标描述"),
            @ParamItem(type = "java.lang.String", name = "business", comment = "业务名称"),
            @ParamItem(type = "java.lang.String", name = "process", comment = "流程名称"),
            @ParamItem(type = "java.lang.String", name = "sub_process", comment = "子流程名称"),
            @ParamItem(type = "java.lang.String", name = "monitor_obj", comment = "监控对象"),
            @ParamItem(type = "java.lang.String", name = "risk_description", comment = "风险描述"),
            @ParamItem(type = "java.lang.String", name = "prevension_measure", comment = "预防措施"),
            @ParamItem(type = "java.lang.String", name = "refer_regu_describe", comment = "参考规则描述"),
            @ParamItem(type = "java.lang.String", name = "editor", comment = "编辑人"),
            @ParamItem(type = "java.lang.Integer", name = "valid_status", comment = "有效状态")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.lang.Boolean", name = "success", comment = "操作是否成功"),
            @ReturnItem(type = "java.lang.Integer", name = "affected", comment = "影响的记录数"),
            @ReturnItem(type = "java.lang.String", name = "message", comment = "消息提示"),
            @ReturnItem(type = "java.util.Map", name = "data", comment = "更新后的数据")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始执行更新知识指标操作...");

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取指标ID
            String indicatorId = (String) this.getCallParam(param, "indicator_id");

            if (indicatorId == null || indicatorId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "指标ID不能为空");
                return result;
            }

            // 准备更新数据
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("indicator_id", indicatorId);

            // 检查必填字段
            String indicatorName = (String) this.getCallParam(param, "indicator_name");
            if (indicatorName == null || indicatorName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "指标名称不能为空");
                return result;
            }
            updateData.put("indicator_name", indicatorName);

            // 设置其他字段
            String[] fields = {
                    "describe", "business", "process", "sub_process", "monitor_obj",
                    "risk_description", "prevension_measure", "refer_regu_describe", "editor"
            };

            for (String field : fields) {
                Object value = this.getCallParam(param, field);
                if (value != null) {
                    updateData.put(field, value);
                }
            }

            // 设置有效状态
            Object validStatus = this.getCallParam(param, "valid_status");
            if (validStatus != null) {
                try {
                    int status = Integer.parseInt(validStatus.toString());
                    updateData.put("valid_status", status);
                } catch (NumberFormatException e) {
                    logger.warn("有效状态参数格式错误，默认设置为1");
                    updateData.put("valid_status", 1);
                }
            } else {
                updateData.put("valid_status", 1);  // 默认有效
            }

            // 准备更新参数
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("dataId", "RiskLibrary.updateKnowledgeIndicator");
            updateParams.put("data", updateData);

            // 执行更新操作
            Object updateResult = saveDataComponent.run(updateParams);
            int affected = Integer.parseInt(updateResult.toString());

            // 获取更新后的记录
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "RiskLibrary.getKnowledgeIndicatorById");
            queryParams.put("indicator_id", indicatorId);

            Object queryResult = queryDataForMapComponent.run(queryParams);

            // 封装返回结果
            result.put("success", affected > 0);
            result.put("affected", affected);
            result.put("data", queryResult);

            if (affected > 0) {
                result.put("message", "更新成功");
                logger.info("成功更新指标记录，ID: {}", indicatorId);
            } else {
                result.put("message", "未找到符合条件的记录");
                logger.warn("未找到ID为{}的指标记录", indicatorId);
            }

        } catch (Exception e) {
            logger.error("更新指标记录失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }

        return result;
    }
}