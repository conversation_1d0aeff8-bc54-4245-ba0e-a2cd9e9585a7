package com.sunsheen.fswp.RiskLibrary;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sunsheen.fswp.aop.LogArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 按业务名称查询流程
 */
@Controller("FlowByBusinessNameComponent")
public class FlowByBusinessNameComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(FlowByBusinessNameComponent.class);
    private static final ObjectMapper jsonMapper = new ObjectMapper();

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryDataForListComponent;

    @Override
    @Component(name = "FlowByBusinessNameComponent", memo = "根据业务名称查询流程")
    @Params({
            @ParamItem(type = "java.lang.String", name = "businessName", comment = "业务名称")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "list", comment = "流程信息列表"),
            @ReturnItem(type = "java.lang.Boolean", name = "success", comment = "操作是否成功")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始按业务名称查询流程信息...");

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取业务名称参数
            String businessName = (String) this.getCallParam(param, "businessName");
            if (businessName == null || businessName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "业务名称不能为空");
                return result;
            }

            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "RiskLibrary.queryFlowsByBusinessName");
            queryParams.put("businessName", businessName.trim());

            Object queryResult = queryDataForListComponent.run(queryParams);

            List<Map<String, Object>> flowsList = extractFlowsFromBusinesses(queryResult);

            // 封装返回结果
            result.put("success", true);
            result.put("list", flowsList);

            logger.info("按业务名称'{}'查询流程成功，共查询到{}条流程记录",
                    businessName, flowsList.size());

        } catch (Exception e) {
            logger.error("按业务名称查询流程失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 从业务查询结果中提取所有流程信息
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractFlowsFromBusinesses(Object queryResult) throws Exception {
        List<Map<String, Object>> flowsList = new ArrayList<>();

        if (queryResult instanceof List) {
            List<Map<String, Object>> businessList = (List<Map<String, Object>>) queryResult;

            for (Map<String, Object> business : businessList) {
                String businessName = (String) business.get("businessName");
                String businessId = (String) business.get("businessId");
                Object flowsObj = business.get("flows");

                // 处理flows字段
                List<Map<String, Object>> flows = parseFlowsData(flowsObj);

                // 为每个流程添加业务信息
                for (Map<String, Object> flow : flows) {
                    Map<String, Object> flowInfo = new HashMap<>();
                    flowInfo.put("businessName", businessName);
                    flowInfo.put("businessId", businessId);
                    flowInfo.put("flowId", flow.get("flowId"));
                    flowInfo.put("flowName", flow.get("name"));
                    flowInfo.put("order", flow.get("order"));
                    flowInfo.put("children", flow.get("children"));

                    flowsList.add(flowInfo);
                }
            }
        }

        return flowsList;
    }

    /**
     * 解析flows数据
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseFlowsData(Object flowsObj) throws Exception {
        List<Map<String, Object>> flows = new ArrayList<>();

        if (flowsObj == null) {
            return flows;
        }

        if (flowsObj instanceof String) {
            flowsObj = jsonMapper.readValue((String) flowsObj, Object.class);
        }

        if (flowsObj instanceof List) {
            flows = (List<Map<String, Object>>) flowsObj;
        }

        return flows;
    }
}