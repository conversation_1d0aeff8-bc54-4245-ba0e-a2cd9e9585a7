package com.sunsheen.fswp.RiskLibrary;

import java.util.HashMap;
import java.util.Map;

import com.sunsheen.fswp.aop.LogArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;

/**
 * 删除知识指标构件
 */
@Controller("DeleteIndicatorComponent")
public class DeleteIndicatorComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(DeleteIndicatorComponent.class);

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveDataComponent;

    @Override
    @Component(name = "DeleteIndicatorComponent", memo = "删除知识指标记录")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicator_id", comment = "指标ID")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.lang.Boolean", name = "success", comment = "操作是否成功"),
            @ReturnItem(type = "java.lang.Integer", name = "affected", comment = "影响的记录数"),
            @ReturnItem(type = "java.lang.String", name = "message", comment = "消息提示")
    })
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取指标ID
            String indicatorId = (String) this.getCallParam(param, "indicator_id");

            if (indicatorId == null || indicatorId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "指标ID不能为空");
                return result;
            }
            // 准备删除参数
            Map<String, Object> deleteParams = new HashMap<>();
            deleteParams.put("dataId", "RiskLibrary.deleteKnowledgeIndicator");

            Map<String, Object> data = new HashMap<>();
            data.put("indicator_id", indicatorId);
            deleteParams.put("data", data);

            // 执行删除操作
            Object deleteResult = saveDataComponent.run(deleteParams);
            int affected = Integer.parseInt(deleteResult.toString());

            // 封装返回结果
            result.put("success", affected > 0);
            result.put("affected", affected);

            if (affected > 0) {
                result.put("message", "删除成功");
                logger.info("成功删除指标记录，ID: {}", indicatorId);
            } else {
                result.put("message", "未找到符合条件的记录");
                logger.warn("未找到ID为{}的指标记录", indicatorId);
            }

        } catch (Exception e) {
            logger.error("删除指标记录失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
}