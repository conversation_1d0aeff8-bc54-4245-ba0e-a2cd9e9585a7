package com.sunsheen.fswp.RiskLibrary;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.sunsheen.fswp.aop.LogArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;

/**
 * 新增知识指标
 */
@Controller("AddKnowledgeIndicatorComponent")
public class AddKnowledgeIndicatorComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(AddKnowledgeIndicatorComponent.class);

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort saveDataComponent;

    @Override
    @Component(name = "AddKnowledgeIndicatorComponent", memo = "新增知识指标")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicatorName", comment = "指标名称"),
            @ParamItem(type = "java.lang.String", name = "describe", comment = "描述"),
            @ParamItem(type = "java.lang.String", name = "business", comment = "业务"),
            @ParamItem(type = "java.lang.String", name = "process", comment = "流程"),
            @ParamItem(type = "java.lang.String", name = "subProcess", comment = "子流程"),
            @ParamItem(type = "java.lang.String", name = "monitorObj", comment = "监控对象"),
            @ParamItem(type = "java.lang.String", name = "riskDescription", comment = "风险描述"),
            @ParamItem(type = "java.lang.String", name = "prevensionMeasure", comment = "防控措施"),
            @ParamItem(type = "java.lang.String", name = "referReguDescribe", comment = "制度依据描述"),
            @ParamItem(type = "java.lang.String", name = "editor", comment = "编辑人")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.lang.Boolean", name = "success", comment = "操作是否成功"),
            @ReturnItem(type = "java.lang.String", name = "message", comment = "操作结果消息"),
            @ReturnItem(type = "java.lang.String", name = "indicatorId", comment = "新增指标的ID")
    })
    @LogArgs
    public Object run(Map param) {
        logger.info("开始新增知识指标...");
        logger.info("收到参数: {}", param);

        Map<String, Object> result = new HashMap<>();
        try {
            // 从param中获取参数，处理大小写不敏感
            param = convertParamKeysToLowerCase(param);

            // 生成UUID作为指标ID
            String indicatorId = UUID.randomUUID().toString();

            // 获取参数（尝试多种可能的键名）
            String indicatorName = getParameterCaseInsensitive(param, "indicatorName", "indicatorname", "indicator_name");
            String describe = getParameterCaseInsensitive(param, "describe", "description");
            String business = getParameterCaseInsensitive(param, "business", "businessName", "businessname");
            String process = getParameterCaseInsensitive(param, "process", "processName", "processname");
            String subProcess = getParameterCaseInsensitive(param, "subProcess", "subprocess", "sub_process");
            String monitorObj = getParameterCaseInsensitive(param, "monitorObj", "monitorobj", "monitor_obj");
            String riskDescription = getParameterCaseInsensitive(param, "riskDescription", "riskdescription", "risk_description");
            String prevensionMeasure = getParameterCaseInsensitive(param, "prevensionMeasure", "prevensionmeasure", "prevension_measure");
            String referReguDescribe = getParameterCaseInsensitive(param, "referReguDescribe", "referregudescribe", "refer_regu_describe");
            String editor = getParameterCaseInsensitive(param, "editor", "createUser", "createuser");

            // 参数校验
            if (indicatorName == null || indicatorName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "新增知识指标失败: 指标名称(indicatorName)不能为空");
                logger.error("缺少必需参数: indicatorName");
                return result;
            }

            if (business == null || business.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "新增知识指标失败: 业务名称(business)不能为空");
                logger.error("缺少必需参数: business");
                return result;
            }

            // 打印关键参数用于调试
            logger.info("indicatorName = {}", indicatorName);
            logger.info("business = {}", business);

            // 准备插入数据
            Map<String, Object> saveParams = new HashMap<>();
            saveParams.put("table", "knowledge_indicators");  // 表名

            // 设置要保存的字段值
            Map<String, Object> fields = new HashMap<>();

            fields.put("indicator_id", indicatorId);
            fields.put("indicator_name", indicatorName != null ? indicatorName.trim() : "");
            fields.put("describe", describe != null ? describe.trim() : "");
            fields.put("business", business != null ? business.trim() : "");
            fields.put("process", process != null ? process.trim() : "");
            fields.put("sub_process", subProcess != null ? subProcess.trim() : "");
            fields.put("monitor_obj", monitorObj != null ? monitorObj.trim() : "");
            fields.put("risk_description", riskDescription != null ? riskDescription.trim() : "");
            fields.put("prevension_measure", prevensionMeasure != null ? prevensionMeasure.trim() : "");
            fields.put("refer_regu_describe", referReguDescribe != null ? referReguDescribe.trim() : "");
            fields.put("editor", editor != null ? editor : "system");
            fields.put("edit_date", new java.sql.Date(System.currentTimeMillis()));
            fields.put("valid_status", 1);  // 默认有效
            saveParams.put("dataId", "RiskLibrary.insertKnowledgeIndicator");
            saveParams.put("fields", fields);

            logger.info("准备保存数据: {}", saveParams);

            // 执行保存
            @SuppressWarnings("unchecked")
            Map<String, Object> saveResult = (Map<String, Object>) saveDataComponent.run(saveParams);

            logger.info("保存结果: {}", saveResult);

            // 处理结果
            boolean success = false;
            String message = "";

            if (saveResult != null && saveResult.get("success") != null) {
                success = Boolean.parseBoolean(saveResult.get("success").toString());
                message = success ? "新增知识指标成功" : "新增知识指标失败";
            } else {
                message = "新增知识指标失败: 未得到执行结果";
            }

            // 封装返回结果
            result.put("success", success);
            result.put("message", message);
            result.put("indicatorId", indicatorId);

            logger.info("{}", message);

        } catch (Exception e) {
            logger.error("新增知识指标失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "新增知识指标失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 将Map中的所有键转换为小写
     */
    private Map<String, Object> convertParamKeysToLowerCase(Map<String, Object> originalMap) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
            if (entry.getKey() != null) {
                result.put(entry.getKey().toLowerCase(), entry.getValue());
            }
        }
        return result;
    }

    /**
     * 尝试多种可能的键名获取参数
     */
    private String getParameterCaseInsensitive(Map<String, Object> map, String... possibleKeys) {
        for (String key : possibleKeys) {
            // 尝试原始键
            Object value = map.get(key);
            if (value != null) {
                return value.toString();
            }

            // 尝试小写键
            value = map.get(key.toLowerCase());
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }
}