package com.sunsheen.fswp.AIAgentClass;

import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Controller;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Controller("AssetReportGeneratorComponent")
@BixComponentPackage(dirname = "所有健康指标显示", type = "SYSTEM")
@Slf4j
public class AssetReportGeneratorComponent extends ABaseComponent {

    // 报告模板文件路径
    private static final String TEMPLATE_PATH = "classpath:report_template.txt";
    // 日期格式
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    private ResourceLoader resourceLoader = null;


    @Autowired
    private AssetIndicatorComponent assetIndicatorComponent;

    public AssetReportGeneratorComponent(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 替换模板中的占位符
     *
     * @param template 报告模板
     * @param data     JSON数据对象
     * @return 替换后的报告
     */
    @LogArgs
    private static String replacePlaceholders(String template, JsonObject data) {
        // 替换当前日期
        template = template.replace("{{current_time}}", DATE_FORMAT.format(new Date()));

        // 定义占位符正则表达式
        Pattern pattern = Pattern.compile("\\{\\{([^{}]+)\\}\\}");
        Matcher matcher = pattern.matcher(template);

        // 查找所有占位符并替换
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String placeholder = matcher.group(1).trim();
            String replacement = getValueFromData(data, placeholder);
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 从JSON数据中获取指定键的值
     *
     * @param data JSON数据对象
     * @param key  键名（支持嵌套键，用点号分隔）
     * @return 对应的值，若不存在则返回空字符串
     */
    @LogArgs
    private static String getValueFromData(JsonObject data, String key) {
        try {
            String result = data.get(key).getAsString();
            if (StrUtil.isBlank(result)) {
                return "";
            }

            return result;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 生成资产报告
     *
     * @param jsonData JSON格式的资产数据
     * @return 生成的资产报告
     * @throws IOException 读取模板文件时发生异常
     */
    @LogArgs
    public String generateReport(String jsonData) throws IOException {
        // 1. 读取报告模板
        String template = readTemplate(TEMPLATE_PATH);

        // 2. 解析JSON数据
        JsonObject data = new Gson().fromJson(jsonData, JsonObject.class);

        // 3. 替换模板中的占位符
        String report = replacePlaceholders(template, data);

        return report;
    }

    /**
     * 读取报告模板文件
     *
     * @param path 模板文件路径
     * @return 模板内容
     * @throws IOException 读取文件失败
     */
    @LogArgs
    private String readTemplate(String path) throws IOException {
        StringBuilder content = new StringBuilder();

        Resource resource = this.resourceLoader.getResource(path);

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
//        try (BufferedReader reader = new BufferedReader(new FileReader("D:\\project\\fswp\\src\\main\\resources\\report_template.txt"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    @Component(
            name = "AssetReportGeneratorComponent"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {

        String jsonData = assetIndicatorComponent.run(param).toString();
        try {
            // 生成报告
            String report = generateReport(jsonData);
            // 输出报告
            System.out.println(report);
            return report;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}