package com.sunsheen.fswp.AIAgentClass;

import com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

import static com.sunsheen.fswp.util.MapFlattener.flatten;


@Controller("AssetIndicatorComponent")
@BixComponentPackage(dirname = "所有健康指标显示", type = "SYSTEM")
@Slf4j
public class AssetIndicatorComponent extends ABaseComponent {
    // 查数据

    @Autowired
    private HealthIndicatorComponent healthIndicatorComponent;
    @Autowired
    private AssetLifecycleSelectComponent assetLifecycleSelectComponent;

    @Autowired
    private AssetTimeoutAlertSelectComponent assetTimeoutAlertSelectComponent;


    @Component(
            name = "AssetIndicatorComponent",
            memo = "所有健康指标显示"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String, Object> AllMap = new HashMap<>();
        String contentText = (String) param.get("contentText");
        if (contentText != null) {

            String[] parts = contentText.split("_", 2); // 最多切分为2部分
            String result = parts.length > 1 ? parts[1] : contentText;
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("department_name", result);

            AllMap.putAll((Map<? extends String, ?>) healthIndicatorComponent.run(queryMap));
            AllMap.putAll((Map<? extends String, ?>) assetLifecycleSelectComponent.run(queryMap));
            AllMap.putAll((Map<? extends String, ?>) assetTimeoutAlertSelectComponent.run(queryMap));
            return AllMap;
        } else {
            AllMap.putAll((Map<? extends String, ?>) healthIndicatorComponent.run(param.get("depart_name")));
            AllMap.putAll((Map<? extends String, ?>) assetLifecycleSelectComponent.run(param.get("depart_name")));
            AllMap.putAll((Map<? extends String, ?>) assetTimeoutAlertSelectComponent.run(param.get("depart_name")));
            return AllMap;
        }

    }

}
