package com.sunsheen.fswp.AIAgentClass;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

import java.util.Map;


@Controller("AgentDataSaverComponent")
@BixComponentPackage(dirname = "体检报告自动存储", type = "SYSTEM")
@Slf4j
public class AgentDataSaverComponent extends ABaseComponent {
    // 查数据

    private static final String API_URL = "http://api.agent-13.default.epai.cuit.edu.cn:30080/v1/chat/completions";
    // HTTP请求方法
    private static final String HTTP_METHOD = "POST";

    // 生成带时间戳的文件名
    public static String generateTimestampedFileName(String prefix, String extension) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        return prefix + "_" + now.format(formatter) + "." + extension;
    }

    // 发送HTTP请求并获取响应
    public static String sendRequest(String apiUrl, String requestBody) throws IOException {
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求头
        connection.setRequestMethod(HTTP_METHOD);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Authorization", "Bearer sk-4J5i54b44Rqg16W86os2gakidzde5bc9cib5R6oi16fhcX0N");
        connection.setDoOutput(true);

//        // 发送请求体
//        try (FileWriter writer = new FileWriter(connection.getOutputStream().toString(), StandardCharsets.UTF_8)) {
//            writer.write(requestBody);
//        }
        try (OutputStream os = connection.getOutputStream();
             OutputStreamWriter osw = new OutputStreamWriter(os, StandardCharsets.UTF_8);
             BufferedWriter writer = new BufferedWriter(osw)) {
            writer.write(requestBody);
        }

        // 读取响应
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 200 && responseCode < 300
                                ? connection.getInputStream()
                                : connection.getErrorStream(),
                        StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
        }

        if (responseCode >= 200 && responseCode < 300) {
            return response.toString();
        } else {
            throw new IOException("HTTP请求失败，状态码: " + responseCode + ", 响应: " + response);
        }
    }

    // 保存数据到JSON文件
    public static void saveToJsonFile(List<String> dataList, String filePath) {
        try (FileWriter writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
            writer.write("[\n");
            for (int i = 0; i < dataList.size(); i++) {
                writer.write(dataList.get(i));
                if (i < dataList.size() - 1) {
                    writer.write(",\n");
                }
            }
            writer.write("\n]");
            System.out.println("数据已保存至: " + filePath);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        }
    }

    @Component(
            name = "AssetIndicatorComponent",
            memo = "体检报告自动存储"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        try {
            // 构建请求体（根据API要求调整）
            String requestBody = "{\n" +
                    "    \"model\": \"gpt-3.5-turbo\",\n" +
                    "    \"messages\": [\n" +
                    "        {\"role\": \"user\", \"content\": \"体检报告\"}\n" +
                    "    ],\n" +
                    "    \"stream\": true\n" +
                    "}";

            // 发送请求并获取响应
            String response = sendRequest(API_URL, requestBody);

            // 处理流式响应（按data:行分割）
            List<String> dataChunks = new ArrayList<>();
            String[] lines = response.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("data: ") && !line.equals("data: [DONE]")) {
                    String dataChunk = line.substring(6); // 去掉"data: "前缀
                    dataChunks.add(dataChunk);
                }
            }

            // 保存数据到文件
            String fileName = generateTimestampedFileName("agent_response", "json");
            saveToJsonFile(dataChunks, fileName);

            System.out.println("成功获取并保存 " + dataChunks.size() + " 个数据块");
            return 1;

        } catch (Exception e) {
            System.err.println("程序执行出错: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }

    }


}
