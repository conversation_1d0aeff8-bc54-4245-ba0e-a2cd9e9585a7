package com.sunsheen.fswp.AIAgentClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sunsheen.fswp.util.MapFlattener.flatten;


@Controller("AssetTimeoutAlertSelectComponent")
@BixComponentPackage(dirname = "资产各审批流程超时告警数据", type = "SYSTEM")
@Slf4j

public class AssetTimeoutAlertSelectComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Component(
            name = "AssetTimeoutAlertSelectComponent",
            memo = "资产各审批流程超时告警数据"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        HashMap<String, Object> tempDataCountMap = new HashMap<>();
//        if (param.get("department_name")!=null&&param.get("department_name")!="全校"){
//            tempDataCountMap.put("department_name", param.get("department_name"));
//        }
        String currentDepartment = (String) param.get("department_name");
        if (!"全校".equals(currentDepartment)) {
            tempDataCountMap.put("department_name", currentDepartment);
        }
        tempDataCountMap.put("dataId", "AIAgent.getAssetAlert");
        List<Map<String, Object>> AssetAlertMap = (List<Map<String, Object>>) queryForList.run(tempDataCountMap);
        Map<String, Object> fieldMap = new HashMap<>();
        AssetAlertMap.forEach(map -> {
            String process = map.get("process").toString();
            map.forEach((key, value) -> {
                if (!"process".equals(key)) {
                    fieldMap.put(process + "." + key, value);
                }
            });
        });
        return flatten(fieldMap);
    }

}
