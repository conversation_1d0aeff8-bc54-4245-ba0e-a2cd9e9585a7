package com.sunsheen.fswp.AIAgentClass;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.sunsheen.fswp.aop.LogArgs;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ApiResponseExtractor {

    private static final String API_URL = "http://api.agent-15.default.epai.cuit.edu.cn:30080/v1/chat/completions";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) {
        try {
            String requestBody = "{\"model\": \"gpt-3.5-turbo\", \"messages\": [{\"role\": \"user\", \"content\": \"请求内容\"}]}";
            String responseBody = sendHttpRequest(API_URL, requestBody);
            List<String> contents = extractContentsFromResponse(responseBody);
            contents.forEach(System.out::println);

        } catch (IOException e) {
            System.err.println("读取流错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @LogArgs
    private static String sendHttpRequest(String url, String requestBody) throws IOException {
        // 配置超时参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(30000)     // 连接超时30秒
                .setSocketTimeout(60000)      // 读取超时60秒
                .setConnectionRequestTimeout(30000)
                .build();

        HttpClient httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .build();

        HttpPost request = new HttpPost(url);
        request.addHeader("Content-Type", "application/json");
        request.addHeader("Authorization", "Bearer sk-ee29iECq948911A7bKHA7Fe972dFu8g8Z8hSJP12m9V2d39M");

        if (requestBody != null && !requestBody.isEmpty()) {
            StringEntity entity = new StringEntity(requestBody, StandardCharsets.UTF_8);
            request.setEntity(entity);
        }

        HttpResponse response = httpClient.execute(request);
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity responseEntity = response.getEntity();

        if (statusCode == 200) {
            // 核心优化：手动管理流读取，避免EntityUtils导致的流关闭问题
            return readEntityWithStream(responseEntity);
        } else {
            String errorContent = readEntityWithStream(responseEntity);
            throw new IOException("HTTP请求失败，状态码：" + statusCode + "\n错误内容：" + errorContent);
        }
    }

    /**
     * 核心优化方法：使用try-with-resources手动读取流，确保资源正确关闭
     */
    @LogArgs
    private static String readEntityWithStream(HttpEntity entity) throws IOException {
        if (entity == null) {
            return "";
        }

        // 使用try-with-resources自动关闭InputStream
        try (InputStream inputStream = entity.getContent();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            StringBuilder result = new StringBuilder();
            char[] buffer = new char[8192];
            int len;
            while ((len = reader.read(buffer)) != -1) {
                result.append(buffer, 0, len);
            }
            return result.toString();
        }
    }

    @LogArgs
    private static List<String> extractContentsFromResponse(String jsonResponse) throws IOException {
        List<String> contents = new ArrayList<>();
        if (jsonResponse == null || jsonResponse.isEmpty()) {
            return contents;
        }

        JsonNode rootNode = objectMapper.readTree(jsonResponse);
        if (rootNode.isArray()) {
            ArrayNode arrayNode = (ArrayNode) rootNode;
            for (JsonNode item : arrayNode) {
                if (item.isObject()) {
                    JsonNode choicesNode = item.get("choices");
                    if (choicesNode != null && choicesNode.isArray()) {
                        for (JsonNode choice : choicesNode) {
                            JsonNode deltaNode = choice.get("delta");
                            if (deltaNode != null && deltaNode.isObject()) {
                                JsonNode contentNode = deltaNode.get("content");
                                if (contentNode != null && contentNode.isTextual()) {
                                    contents.add(contentNode.asText());
                                }
                            }
                        }
                    }
                }
            }
        }
        return contents;
    }
}