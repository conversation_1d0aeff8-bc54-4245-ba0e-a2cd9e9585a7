package com.sunsheen.fswp.AIAgentClass;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.MarkdownToPdf;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

public class StreamingApiClient {

    private static final String API_URL = "http://api.agent-15.default.epai.cuit.edu.cn:30080/v1/chat/completions";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 流式响应分隔符（根据API实际格式调整）
    private static final String STREAM_DELIMITER = "data: ";

    public static void main(String[] args) {
        try {
            // 构造包含stream参数的请求体
            String requestBody = "{\"model\":\"ddd\",\"messages\":[{\"role\":\"user\",\"content\":\"体检报告\"}],\"stream\":true}";
            // 发送请求并处理流式响应
            processStreamingResponse(API_URL, requestBody, "体检报告");

        } catch (IOException e) {
            System.err.println("流式处理错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @LogArgs
    public static void processStreamingResponse(String url, String requestBody, String path) throws IOException {
        // 配置超时参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(30000)     // 连接超时30秒
                .setSocketTimeout(120000)     // 读取超时120秒（适应流式响应）
                .setConnectionRequestTimeout(30000)
                .build();

        HttpClient httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .build();

        HttpPost request = new HttpPost(url);
        request.addHeader("Content-Type", "application/json");
        request.addHeader("Authorization", "Bearer sk-ee29iECq948911A7bKHA7Fe972dFu8g8Z8hSJP12m9V2d39M");
        request.addHeader("Accept", "text/event-stream"); // 声明接收流式响应

        if (requestBody != null && !requestBody.isEmpty()) {
            StringEntity entity = new StringEntity(requestBody, StandardCharsets.UTF_8);
            request.setEntity(entity);
        }

        HttpResponse response = httpClient.execute(request);
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity responseEntity = response.getEntity();

        if (statusCode == 200) {
            System.out.println("流式响应开始处理...");
            // 核心：处理流式响应
            parseStreamingContent(responseEntity.getContent(), path);
        } else {
            String errorContent = readEntity(responseEntity);
            throw new IOException("HTTP请求失败，状态码：" + statusCode + "\n错误内容：" + errorContent);
        }
    }

    /**
     * 解析流式响应内容
     */
    @LogArgs
    private static void parseStreamingContent(InputStream inputStream, String path) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            StringBuilder fullResponse = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                // 过滤空行和分隔符（如"data: "）
                if (line.startsWith(STREAM_DELIMITER)) {
                    String jsonPart = line.substring(STREAM_DELIMITER.length()).trim();
                    // 处理特殊结束标记
                    if ("[DONE]".equals(jsonPart)) {
                        System.out.println("流式响应结束");
                        break;
                    }

                    // 解析JSON并提取content
                    JsonNode node = objectMapper.readTree(jsonPart);
                    extractContentFromNode(node, fullResponse);
                }
            }
//            String result= fullResponse.toString();

            String result = filterText(fullResponse.toString());
            System.out.println(result);
            MarkdownToPdf.convertMarkdownToPdf(result, path);
        }
    }

    /**
     * 从JSON节点提取content并拼接
     */
    @LogArgs
    private static void extractContentFromNode(JsonNode node, StringBuilder fullResponse) {
        if (node.isObject()) {
            JsonNode choicesNode = node.get("choices");
            if (choicesNode != null && choicesNode.isArray()) {
                for (JsonNode choice : choicesNode) {
                    JsonNode deltaNode = choice.get("delta");
                    if (deltaNode != null && deltaNode.isObject()) {
                        JsonNode contentNode = deltaNode.get("content");
                        if (contentNode != null && contentNode.isTextual()) {
                            String content = contentNode.asText();
                            System.out.print(content); // 实时输出流式内容
                            fullResponse.append(content);
                        }
                    }
                }
            }
        }
    }

    /**
     * 辅助方法：读取普通响应内容
     */
    @LogArgs
    private static String readEntity(HttpEntity entity) throws IOException {
        if (entity == null) {
            return "";
        }
        try (InputStream inputStream = entity.getContent();
             BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder result = new StringBuilder();
            char[] buffer = new char[8192];
            int len;
            while ((len = reader.read(buffer)) != -1) {
                result.append(buffer, 0, len);
            }
            return result.toString();
        }
    }

    //过滤ai输出结果
    @LogArgs
    public static String filterText(String input) {
        int startIndex = input.indexOf("</think>");
        int endIndex = input.indexOf("【大模型——体检报告分析】节点运行结束。。。");

        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            System.out.println("未找到匹配的文本范围");
            return "";
        }

        // 调整起始索引以排除 "</think>" 标签本身
        startIndex += "</think>".length();

        // 提取从 startIndex 到 endIndex 之间的文本
        return input.substring(startIndex, endIndex).trim();
    }
}