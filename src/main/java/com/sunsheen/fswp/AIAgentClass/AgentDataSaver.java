package com.sunsheen.fswp.AIAgentClass;

import com.sunsheen.fswp.aop.LogArgs;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class AgentDataSaver {

    // 保存数据到JSON文件
    @LogArgs
    public static void saveToJsonFile(List<String> dataList, String filePath) {
        try (FileWriter writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
            // 构建JSON数组
            writer.write("[\n");
            for (int i = 0; i < dataList.size(); i++) {
                writer.write(dataList.get(i));
                if (i < dataList.size() - 1) {
                    writer.write(",\n");
                }
            }
            writer.write("\n]");
            System.out.println("数据已保存至: " + filePath);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        }
    }

    // 生成带时间戳的文件名
    @LogArgs
    public static String generateTimestampedFileName(String prefix, String extension) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        return prefix + "_" + now.format(formatter) + "." + extension;
    }

    public static void main(String[] args) {
        // 示例数据（实际使用时替换为API返回结果）
        List<String> agentResponses = new ArrayList<>();
        agentResponses.add("{\"id\":\"0\",\"choices\":[{\"delta\":{\"content\":\"【文本输入】节点运行开始。。。\\n\",\"function_call\":{\"arguments\":\"\",\"name\":\"文本输入\"},\"role\":\"assistant\"},\"finish_reason\":\"function_call\",\"index\":0}],\"created\":1750641430,\"model\":\"ddd\",\"object\":\"chat.completion.chunk\"}");
        agentResponses.add("{\"id\":\"0\",\"choices\":[{\"delta\":{\"content\":\"【文本输入】节点运行结束。。。\\n\",\"function_call\":{\"arguments\":\"\",\"name\":\"文本输入\"},\"role\":\"assistant\"},\"finish_reason\":\"function_call\",\"index\":0}],\"created\":1750641430,\"model\":\"ddd\",\"object\":\"chat.completion.chunk\"}");

        // 生成带时间戳的文件名
        String fileName = generateTimestampedFileName("agent_data", "json");

        // 保存数据到文件
        saveToJsonFile(agentResponses, fileName);

        // 可选：读取保存的文件内容（用于验证）
        try {
            List<String> lines = Files.readAllLines(Paths.get(fileName), StandardCharsets.UTF_8);
            System.out.println("\n保存的文件内容：");
            lines.forEach(System.out::println);
        } catch (IOException e) {
            System.err.println("读取文件时出错: " + e.getMessage());
        }
    }
}