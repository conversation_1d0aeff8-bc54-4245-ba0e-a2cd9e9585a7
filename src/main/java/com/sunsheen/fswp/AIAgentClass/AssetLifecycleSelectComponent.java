package com.sunsheen.fswp.AIAgentClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sunsheen.fswp.util.MapFlattener.flatten;


@Controller("AssetLifecycleSelectComponent")
@BixComponentPackage(dirname = "二、资产各生命周期健康度监测", type = "SYSTEM")
@Slf4j
public class AssetLifecycleSelectComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;
    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;
    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Component(
            name = "AssetLifecycleSelectComponent",
            memo = "资产各生命周期健康度监测"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {


        HashMap<String, Object> tempDataCountMap = new HashMap<>();
        String currentDepartment = (String) param.get("department_name");
        if (!"全校".equals(currentDepartment)) {
            tempDataCountMap.put("department_name", currentDepartment);
        }
//        if (param.get("department_name")!=null&&param.get("department_name")!="全校"){
//            tempDataCountMap.put("department_name", param.get("department_name"));
//        }
        tempDataCountMap.put("dataId", "AIAgent.getAbnormalAssetsNumber");
        Map<String, Object> AbnormalAssetsMap = (Map<String, Object>) queryForMap.run(tempDataCountMap);
//
        tempDataCountMap.put("dataId", "AIAgent.getMove");
        Map<String, Object> MoveMap = (Map<String, Object>) queryForMap.run(tempDataCountMap);

        tempDataCountMap.put("dataId", "AIAgent.getTransfer");
        Map<String, Object> TransferMap = (Map<String, Object>) queryForMap.run(tempDataCountMap);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("AbnormalAssetsMap", AbnormalAssetsMap);
        resultMap.put("MoveMap", MoveMap);
        resultMap.put("TransferMap", TransferMap);


        return flatten(resultMap);
    }

}
