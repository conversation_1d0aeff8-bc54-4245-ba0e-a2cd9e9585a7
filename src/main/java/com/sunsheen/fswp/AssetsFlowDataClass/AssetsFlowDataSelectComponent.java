package com.sunsheen.fswp.AssetsFlowDataClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;

@Controller("AssetsFlowDataSelectComponent")
@BixComponentPackage(dirname = "任务进度查询", type = "SYSTEM")
@Slf4j
public class AssetsFlowDataSelectComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "AssetsFlowDataSelectComponent",
            memo = "任务流查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {


        // 查询参数的当前部门
        String currentDepartment = (String) param.get("department_name");
        if (!"全校".equals(currentDepartment)) {
            param.put("using_department", currentDepartment);
        }

        param.put("dataId", "AssetsFlowData.countSelectWorkflowData");
        param.putAll(DBUtil.getLimitPageParams(param));
        Long count = (Long) queryForObject.run(param);

        param.put("dataId", "AssetsFlowData.selectWorkflowData");

        List result = (List) queryForList.run(param);

        result.forEach(item -> {
            Map dataMap = (Map) item;
            dataMap.put("systemName", "资产系统");
            Integer stayHours = (Integer) dataMap.get("stayHours");
            dataMap.remove("stayHours");
            dataMap.put("stayDays", String.format("%.1f", 1. * stayHours / 24));
        });

        return Map.of(
                "data", result,
                "count", count
        );
    }
}