package com.sunsheen.fswp.util;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import lombok.extern.slf4j.Slf4j;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

@Slf4j
public class MarkdownToPdfUtil {
    /**
     * 将Markdown文本转换为HTML
     *
     * @param markdown Markdown格式的文本
     * @return HTML格式的字符串
     */
    public static String markdownToHtml(String markdown) {
        Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        return renderer.render(parser.parse(markdown));
    }

    /**
     * 将HTML转换为PDF并保存到指定路径
     *
     * @param html       HTML格式的字符串
     * @param outputPath 输出PDF文件的路径
     * @throws IOException 如果文件操作失败
     */
    public static void htmlToPdf(String html, String outputPath) throws IOException {
        // 包装HTML，确保有单一根元素
        String wrappedHtml = "<html><body>" + html + "</body></html>";

        OutputStream os = new FileOutputStream(outputPath);
        PdfRendererBuilder builder = new PdfRendererBuilder();
        builder.withHtmlContent(wrappedHtml, null);
        builder.toStream(os);
        builder.run();
    }


    public static void markdownToPdf(String markdown, String outputPath) {
        try {
            String html = markdownToHtml(markdown);
            htmlToPdf(html, outputPath);
            log.info("PDF生成成功：" + outputPath);
        } catch (IOException e) {
            log.error("PDF生成失败：" + e.getMessage());
            throw new RuntimeException(e);
        }

    }

    public static void main(String[] args) {
        String markdown = "# Hello World\nThis is a *simple* example.";
        String outputPath = "output1.pdf";

        markdownToPdf(markdown, outputPath);
    }
}