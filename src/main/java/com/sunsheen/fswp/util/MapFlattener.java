package com.sunsheen.fswp.util;

import java.util.HashMap;
import java.util.Map;

public class MapFlattener {

    /**
     * 将嵌套Map转换为扁平Map
     *
     * @param nestedMap 嵌套的Map
     * @param separator 键名分隔符
     * @return 扁平化后的Map
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap, String separator) {
        Map<String, Object> result = new HashMap<>();
        flattenMap(nestedMap, result, "", separator);
        return result;
    }

    /**
     * 默认使用点号作为分隔符
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap) {
        return flatten(nestedMap, ".");
    }

    /**
     * 递归扁平化Map的辅助方法
     */
    @SuppressWarnings("unchecked")
    private static void flattenMap(Map<String, Object> nestedMap, Map<String, Object> result,
                                   String prefix, String separator) {
        for (Map.Entry<String, Object> entry : nestedMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String newKey = prefix.isEmpty() ? key : prefix + separator + key;

            if (value instanceof Map) {
                // 如果值是Map，则递归处理
                flattenMap((Map<String, Object>) value, result, newKey, separator);
            } else {
                // 否则直接添加到结果Map中
                result.put(newKey, value);
            }
        }
    }

    // 使用示例
    public static void main(String[] args) {
        Map<String, Object> nestedData = new HashMap<>();

        Map<String, Object> user = new HashMap<>();
        user.put("name", "John");
        user.put("age", 30);

        Map<String, Object> address = new HashMap<>();
        address.put("city", "New York");
        address.put("zip", "10001");
        user.put("address", address);

        Map<String, Object> preferences = new HashMap<>();
        preferences.put("darkMode", true);

        Map<String, Object> notifications = new HashMap<>();
        notifications.put("email", true);
        notifications.put("sms", false);
        preferences.put("notifications", notifications);

        nestedData.put("user", user);
        nestedData.put("preferences", preferences);

        Map<String, Object> flattenedData = flatten(nestedData);

        // 打印扁平化后的结果
        flattenedData.forEach((key, value) ->
                System.out.println(key + ": " + value)
        );
    }
}
