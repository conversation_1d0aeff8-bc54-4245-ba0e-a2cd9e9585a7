package com.sunsheen.fswp.util;

import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.font.FontProvider;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;

import java.io.*;

public class MarkdownToPdf {
    public static void main(String[] args) {
        String markdown = "### 学校资产全周期分阶段体检报告分析\n" +
                "\n" +
                "**报告标题：** 学校资产全周期分阶段体检报告  \n" +
                "**报告日期：** 2025-06-26  \n" +
                "**总体健康度：** 0.6299473429272332  \n" +
                "\n" +
                "### 总结\n" +
                "学校资产管理总体健康度处于中等水平，需重点关注资产登记和报废环节的告警问题。通过优化审批流程、加强信息化管理和完善资产信息更新机制，可以有效提升资产管理效率和健康度。";
        String outputPath = "output_with_chinese.pdf";

        try {
            convertMarkdownToPdf(markdown, outputPath);
            System.out.println("PDF生成成功，路径：" + outputPath);
        } catch (Exception e) {
            System.err.println("生成PDF时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void convertMarkdownToPdf(String markdown, String outputPath) throws IOException {
        // 解析Markdown
        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdown);

        // 渲染为HTML
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        String html = renderer.render(document);

        // 添加中文支持的CSS
        html = "<html><head><style>body { font-family: 'SimHei', sans-serif; }</style></head><body>" +
                html +
                "</body></html>";

        // 创建输出文件
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null) {
            parentDir.mkdirs();
        }

//        // 创建字体提供器并注册中文字体
//        FontProvider fontProvider = new FontProvider();
//        fontProvider.addStandardPdfFonts();
//        fontProvider.addSystemFonts();
        // 创建字体提供器并注册中文字体，指定具体字体文件路径
        FontProvider fontProvider = new FontProvider();
        fontProvider.addStandardPdfFonts();
        fontProvider.addSystemFonts();

        // 添加Linux系统中常见的中文字体路径
        try {

            fontProvider.addFont("/root/var/fonts/SIMSUN.TTC");

            fontProvider.addFont("/root/var/fonts/SIMHEI.TTF");

        } catch (Exception e) {
            System.err.println("字体加载失败: " + e.getMessage());
        }

        // 将HTML转换为PDF，使用自定义字体提供器
        try (OutputStream fileOutputStream = new FileOutputStream(outputFile);
             PdfWriter writer = new PdfWriter(fileOutputStream);
             PdfDocument pdfDoc = new PdfDocument(writer)) {

            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setFontProvider(fontProvider);

            Document doc = HtmlConverter.convertToDocument(html, pdfDoc, converterProperties);
            doc.close();
        }
    }
}    