package com.sunsheen.fswp.util;

import com.sunsheen.fswp.aop.LogArgs;
import jakarta.servlet.http.HttpServletRequest;
import org.apereo.cas.client.authentication.AttributePrincipal;
import org.apereo.cas.client.util.AbstractCasFilter;
import org.apereo.cas.client.validation.Assertion;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class UserUtil {

    @LogArgs
    public static String getLogAccount(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
            String loginName = null;

            if (assertion != null) {
                //2、取登录用户信息
                AttributePrincipal principal = assertion.getPrincipal();
                loginName = principal.getName();
            }
            return loginName;
        }
        return null;
    }
}
