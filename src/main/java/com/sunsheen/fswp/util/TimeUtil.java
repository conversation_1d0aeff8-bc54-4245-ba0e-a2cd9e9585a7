package com.sunsheen.fswp.util;

import com.sunsheen.fswp.aop.LogArgs;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

public class TimeUtil {

    // 获取上周一的 00:00:00
    @LogArgs
    public static LocalDateTime getStartOfLastMonday() {
        LocalDate today = LocalDate.now();
        // 先获取本周一，再减去7天
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        return lastMonday.atStartOfDay();
    }


    // 获取本周一的 00:00:00
    @LogArgs
    public static LocalDateTime getStartOfThisMonday() {
        LocalDate today = LocalDate.now();
        // 回到本周一
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return thisMonday.atStartOfDay();
    }


}
