package com.sunsheen.fswp.util;

import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DBUtil {

    // 分页参数处理
    public static Map<String, Object> getLimitPageParams(Map params) {
        Map<String, Object> resultMap = new HashMap<>();
        //判断是否需要分页，默认不需要
        if (ObjUtil.isNotEmpty(params.get("page")) && ObjUtil.isNotEmpty(params.get("pageSize"))) {
            // 获取分页参数
            Integer page = Integer.parseInt(params.get("page").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());

            resultMap.put("start", (page - 1) * pageSize);
            resultMap.put("limit", pageSize);
        }
        return resultMap;
    }
}
