package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 执行所有指标预警规则组件
 */
@Controller("ExecuteAllIndicatorRulesComponent")
@BixComponentPackage(dirname = "执行所有指标预警规则", type = "BUSINESS")
public class ExecuteAllIndicatorRulesComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(ExecuteAllIndicatorRulesComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Component(name = "ExecuteAllIndicatorRulesComponent", memo = "执行所有指标预警规则")
    @Params({})
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "执行结果，包含成功和失败的规则信息")
    })
    @LogArgs
    public Object run(Map param) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("dataId", "IndicatorLevel.queryMyIndicatorLevels");


//        try {
//            logger.info("开始执行所有指标预警规则");
//            System.out.println("param---------------: " + param);
//            // 获取分页参数
//            Map<String, Object> limitPageParams = DBUtil.getLimitPageParams(param);
//            // 查询所有指标预警规则
//            Map<String, Object> queryParams = new HashMap<>();
//            queryParams.put("dataId", "IndicatorLevel.queryAllIndicatorLevels");
////            queryParams.putAll(limitPageParams);
//            List<Map<String, Object>> allRules = (List<Map<String, Object>>) queryForList.run(queryParams);
//            logger.info("查询到{}条指标预警规则", allRules != null ? allRules.size() : 0);
//            if (allRules == null || allRules.isEmpty()) {
//                Map<String, Object> result = new HashMap<>();
//                result.put("message", "没有找到任何指标预警规则");
//                result.put("total_rules", 0);
//                result.put("executed_rules", 0);
//                result.put("success_count", 0);
//                result.put("error_count", 0);
//                result.put("results", new ArrayList<>());
//                return result;
//            }
//            // 统计规则总数
//            long count = allRules.stream().count();
//
//            // 执行结果统计
//            List<Map<String, Object>> executionResults = new ArrayList<>();
//            int executedCount = 0;
//            int successCount = 0;
//            int errorCount = 0;
//
//            // 遍历所有规则并执行有SQL内容的规则
//            for (Map<String, Object> rule : allRules) {
//                String sql = (String) rule.get("sql");
//
//                String ruleId = String.valueOf(rule.get("id"));
//                String indicatorName = (String) rule.get("indicator_name");
//                String type = (String) rule.get("type");
//
//                // 检查SQL字段是否有内容，没有内容直接跳过，不加入结果
//                if (sql == null || sql.trim().isEmpty()) {
//                    logger.debug("规则ID: {} ({}) 没有SQL内容，跳过执行", ruleId, indicatorName);
//                    continue; // 直接跳过，不加入结果列表
//                }
//
//                // 给sql加分页 limit page，pageSize
//                if (limitPageParams.containsKey("start") && limitPageParams.containsKey("limit")) {
//                    sql += " LIMIT " + limitPageParams.get("start") + ", " + limitPageParams.get("limit");
//                    System.out.println("拼接的sql------: " + sql);
//                }
//                logger.debug("拼接的sql: {}", sql);
//
//                logger.info("执行规则ID: {} ({}) 的SQL: {}", ruleId, indicatorName, sql);
//                executedCount++;
//
//                Map<String, Object> ruleResult = new HashMap<>();
//                ruleResult.put("rule_id", ruleId);
//                ruleResult.put("indicator_name", indicatorName);
//                ruleResult.put("type", type);
//                ruleResult.put("sql", sql);
//
//                try {
//                    // 安全检查：只允许SELECT语句
//                    String upperSql = sql.toUpperCase().trim();
//                    if (!upperSql.startsWith("SELECT")) {
//                        logger.warn("规则ID: {} 包含非SELECT语句，跳过执行", ruleId);
//                        ruleResult.put("status", "error");
//                        ruleResult.put("message", "只允许执行SELECT查询语句");
//                        ruleResult.put("result_data", null);
//                        errorCount++;
//                    } else {
//                        // 执行SQL查询
//                        List<Map<String, Object>> sqlResult = jdbcTemplate.queryForList(sql);
//                        ruleResult.put("status", "success");
//                        ruleResult.put("message", "执行成功");
//                        ruleResult.put("result_data", sqlResult);
//                        ruleResult.put("result_count", sqlResult.size());
//
//                        successCount++;
//                        logger.info("规则ID: {} 执行成功，返回{}条记录", ruleId, sqlResult.size());
//                    }
//                } catch (Exception e) {
//                    logger.error("规则ID: {} 执行失败: {}", ruleId, e.getMessage(), e);
//                    ruleResult.put("status", "error");
//                    ruleResult.put("message", "执行失败: " + e.getMessage());
//                    ruleResult.put("result_data", null);
//                    errorCount++;
//                }
//
//                executionResults.add(ruleResult);
//            }
//
//            // 构造最终结果
//            Map<String, Object> finalResult = new HashMap<>();
//            finalResult.put("message", "指标预警规则执行完成");
//            finalResult.put("total_rules", allRules.size());
//            finalResult.put("executed_rules", executedCount);
//            finalResult.put("success_count", successCount);
//            finalResult.put("error_count", errorCount);
//            finalResult.put("results", executionResults);
//
//            logger.info("指标预警规则执行完成 - 总规则数: {}, 执行数: {}, 成功数: {}, 失败数: {}, 跳过数: {}",
//                       allRules.size(), executedCount, successCount, errorCount, allRules.size() - executedCount);
//
//            return finalResult;
//
//        } catch (Exception e) {
//            logger.error("执行所有指标预警规则失败: {}", e.getMessage(), e);
//            throw new RuntimeException("执行所有指标预警规则失败: " + e.getMessage(), e);
//        }
        return null;
    }
}
