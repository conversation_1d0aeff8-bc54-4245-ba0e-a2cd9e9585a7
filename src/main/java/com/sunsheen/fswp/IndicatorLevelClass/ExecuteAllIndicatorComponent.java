package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 执行所有指标预警规则组件
 */
@Controller("ExecuteAllIndicatorComponent")
@BixComponentPackage(dirname = "自定义指标查询", type = "BUSINESS")
public class ExecuteAllIndicatorComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(ExecuteAllIndicatorComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;


    @Override
    @Component(name = "ExecuteAllIndicatorComponent", memo = "自定义指标查询")
    @Params({})
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "执行结果，包含成功和失败的规则信息")
    })
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("dataId", "IndicatorLevel.queryAllIndicator");
        return queryForList.run(queryMap);
    }
}
