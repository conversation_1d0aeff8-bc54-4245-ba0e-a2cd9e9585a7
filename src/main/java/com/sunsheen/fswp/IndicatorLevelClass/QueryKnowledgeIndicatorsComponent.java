package com.sunsheen.fswp.IndicatorLevelClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("QueryKnowledgeIndicatorsComponent")
@BixComponentPackage(dirname = "查询知识库指标", type = "BUSINESS")
public class QueryKnowledgeIndicatorsComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "QueryKnowledgeIndicatorsComponent", memo = "查询知识库中的指标名称与编号")
    @Params({
            @ParamItem(type = "java.lang.String", name = "dataId", comment = "数据查询 ID")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回知识库指标列表")})
    @LogArgs
    public Object run(Map param) {
        // 设置查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "IndicatorLevel.queryKnowledgeIndicatorsForDropdown");
        // 运行查询并返回结果
        return queryForList.run(queryParams);
    }
}

