package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;

import java.util.*;

/**
 * 执行所有指标预警规则组件
 */
@Controller("ExecuteIndicatorContentComponent")
@BixComponentPackage(dirname = "自定义指标查询", type = "BUSINESS")
public class ExecuteIndicatorContentComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(ExecuteIndicatorContentComponent.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    @Component(name = "ExecuteIndicatorContentComponent", memo = "自定义指标查询")
    @Params({})
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "执行结果，包含成功和失败的规则信息")
    })
    @LogArgs
    public Object run(Map param) {

        String sql = param.get("sql").toString();
        int page = Integer.parseInt(param.get("page").toString());
        int size = Integer.parseInt(param.get("pageSize").toString());
        long count = Optional.ofNullable(jdbcTemplate.queryForList(sql)).orElse(Collections.emptyList()).stream().count();

        String query = sql + " limit " + (page-1) * size + "," + size;
        Map<String, Object> result = new HashMap<>();

        // 执行SQL查询
        List<Map<String, Object>> sqlResult = jdbcTemplate.queryForList(query);
        result.put("data", sqlResult);
        result.put("count", count);
        result.put("code", 1);
        result.put("msg", "success");
        result.put("page", page);
        result.put("pageSize", size);

        return result;
    }
}
