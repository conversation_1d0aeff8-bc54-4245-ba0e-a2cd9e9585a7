package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预览自定义SQL执行结果组件
 */
@Controller("PreviewCustomSqlComponent")
@BixComponentPackage(dirname = "预览自定义SQL", type = "BUSINESS")
public class PreviewCustomSqlComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(PreviewCustomSqlComponent.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Component(name = "PreviewCustomSqlComponent", memo = "预览自定义SQL执行结果")
    @Params({
            @ParamItem(type = "java.lang.String", name = "sql", comment = "自定义SQL语句")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "预览结果，包含数据和统计信息")
    })
    @LogArgs
    public Object run(Map param) {
        try {
            // 获取SQL参数
            Object sqlObj = this.getCallParam(param, "sql");
            if (sqlObj == null) {
                logger.error("SQL语句不能为空");
                throw new RuntimeException("SQL语句不能为空");
            }

            String sql = sqlObj.toString().trim();
            if (sql.isEmpty()) {
                logger.error("SQL语句不能为空字符串");
                throw new RuntimeException("SQL语句不能为空");
            }

            // 安全检查：只允许SELECT语句
            String upperSql = sql.toUpperCase().trim();
            if (!upperSql.startsWith("SELECT")) {
                logger.error("只允许执行SELECT查询语句");
                throw new RuntimeException("只允许执行SELECT查询语句");
            }

            // 检查是否包含危险操作
            if (containsDangerousOperations(upperSql)) {
                logger.error("SQL语句包含不允许的操作");
                throw new RuntimeException("SQL语句包含不允许的操作，只允许SELECT查询");
            }

            logger.info("开始预览SQL执行结果: {}", sql);

            // 限制查询结果数量，避免返回过多数据
            String limitedSql = addLimitToSql(sql);

            // 直接使用JdbcTemplate执行SQL
            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(limitedSql);

            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", resultList);
            result.put("total", resultList != null ? resultList.size() : 0);
            result.put("sql", limitedSql);
            result.put("message", "预览成功");

            logger.info("SQL预览执行成功，返回{}条记录", resultList != null ? resultList.size() : 0);

            return result;

        } catch (Exception e) {
            logger.error("预览SQL执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("预览SQL执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查SQL是否包含危险操作
     */
    private boolean containsDangerousOperations(String sql) {
        String[] dangerousKeywords = {
            "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", 
            "TRUNCATE", "EXEC", "EXECUTE", "CALL", "PROCEDURE",
            "FUNCTION", "TRIGGER", "INDEX", "VIEW", "GRANT", "REVOKE"
        };
        
        for (String keyword : dangerousKeywords) {
            if (sql.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 为SQL添加LIMIT限制，避免返回过多数据
     */
    private String addLimitToSql(String sql) {
        String upperSql = sql.toUpperCase().trim();
        
        // 如果已经包含LIMIT，则不再添加
        if (upperSql.contains("LIMIT")) {
            return sql;
        }
        
        // 添加LIMIT 100限制
        return sql + " LIMIT 100";
    }
}
