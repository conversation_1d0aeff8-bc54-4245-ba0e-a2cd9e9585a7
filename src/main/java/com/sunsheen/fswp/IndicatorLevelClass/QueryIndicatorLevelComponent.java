package com.sunsheen.fswp.IndicatorLevelClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Controller("QueryIndicatorLevelComponent")
@BixComponentPackage(dirname = "查询指标预警规则", type = "BUSINESS")
public class QueryIndicatorLevelComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(name = "QueryIndicatorLevelComponent", memo = "查询指标预警规则构件")
    @Params({
            @ParamItem(type = "java.lang.String", name = "dataId", comment = "查询数据源ID"),
            @ParamItem(type = "java.util.Map", name = "queryParams", comment = "查询参数")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {
        log.info("QueryIndicatorLevelComponent run");
        String dataId = "IndicatorLevel.queryIndicatorLevels"; // 与 .dm 文件中定义的 ID 对应
        Map<String, Object> params = new HashMap<>();
        params.put("dataId", dataId);
        params.put("indicatorName", this.getCallParam(param, "indicatorName"));
        // 调用查询接口并返回结果
        return queryForList.run(params);
    }
}

