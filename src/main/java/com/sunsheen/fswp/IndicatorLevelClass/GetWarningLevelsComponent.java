package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import java.util.HashMap;
import java.util.Map;

@Controller("GetWarningLevelsComponent")
@BixComponentPackage(dirname = "获取预警级别", type = "BUSINESS")
public class GetWarningLevelsComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryForList;

    @Override
    @Component(name = "GetWarningLevelsComponent", memo = "获取预警级别构件")
    @Params({
            @ParamItem(type = "java.lang.String", name = "dataId", comment = "目标数据源ID")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回预警级别列表")})
    @LogArgs
    public Object run(Map param) {
        String dataId = "IndicatorLevel.queryWarningLevels"; // 与 .dm 文件中定义的 ID 对应
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", dataId);
        // 调用查询接口并返回结果
        return queryForList.run(queryParams);
    }
}

