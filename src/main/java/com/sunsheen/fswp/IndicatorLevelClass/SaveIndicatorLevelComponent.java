package com.sunsheen.fswp.IndicatorLevelClass;


import cn.hutool.core.date.DateTime;
import com.mysql.cj.util.StringUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Controller("SaveIndicatorLevelComponent")
@BixComponentPackage(dirname = "添加指标预警规则", type = "BUSINESS")
public class SaveIndicatorLevelComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(name = "SaveIndicatorLevelComponent", memo = "保存指标预警规则构件")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicator_id", comment = "指标ID"),
            @ParamItem(type = "java.lang.String", name = "indicator_name", comment = "指标名称"),
            @ParamItem(type = "java.lang.String", name = "warning_level", comment = "预警级别"),
            @ParamItem(type = "java.lang.String", name = "rule_description", comment = "规则描述"),
            @ParamItem(type = "java.lang.String", name = "supervisor_level", comment = "监管级别"),
            @ParamItem(type = "java.lang.Integer", name = "threshold", comment = "阈值"),
            @ParamItem(type = "java.lang.String", name = "sql", comment = "SQL语句（自定义指标必填）")
    })
    @Returns(retValue = {@ReturnItem(type = "java.lang.Integer", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        log.info("SaveIndicatorLevelComponent 接收到的参数: {}", param);

        String indicatorId = (String) this.getCallParam(param, "indicator_id");
        log.info("指标ID: {}", indicatorId);
        String logAccount = UserUtil.getLogAccount();
        if(StringUtils.isEmptyOrWhitespaceOnly(logAccount)){
            throw new RuntimeException("请登录！");
        }

        // 查询指标的type信息
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "IndicatorLevel.queryIndicatorTypeById");
        queryParams.put("indicator_id", indicatorId);

        log.info("查询指标类型的参数: {}", queryParams);

        Map<String, Object> indicatorInfo = (Map<String, Object>) queryForMap.run(queryParams);
        log.info("查询到的指标信息: {}", indicatorInfo);

        String type;
        if (indicatorInfo == null || indicatorInfo.isEmpty()) {
            log.warn("未找到指标ID为 {} 的指标信息，默认按自定义指标处理", indicatorId);
            type = "自定义指标";
        } else {
            type = (String) indicatorInfo.get("type");
            log.info("指标类型: {}", type);
        }

        Map<String, Object> saveDataMap = new HashMap<>();
        Map<String, Object> saveParams = new HashMap<>();
        saveParams.put("table", "indicator_level");
        saveParams.put("dataId", "IndicatorLevel.insertIndicatorLevel");

        // 生成适合INT范围的数字ID（使用时间戳的后6位+随机数）
        long timestamp = System.currentTimeMillis();
        int timestampSuffix = (int)(timestamp % 1000000); // 取时间戳后6位
        int random = (int)(Math.random() * 1000); // 3位随机数
        int numericId = timestampSuffix * 1000 + random; // 最大9位数字，在INT范围内
        log.info("生成的数字ID: {}, 时间戳后缀: {}, 随机数: {}", numericId, timestampSuffix, random);
        saveDataMap.put("ID", numericId);
        saveDataMap.put("indicator_id", this.getCallParam(param,"indicator_id"));
        saveDataMap.put("indicator_name", this.getCallParam(param,"indicator_name"));
        saveDataMap.put("warning_level", this.getCallParam(param,"warning_level"));
        saveDataMap.put("describe", this.getCallParam(param,"rule_description"));
        saveDataMap.put("threshold", this.getCallParam(param,"threshold"));
        saveDataMap.put("supervisor_level", this.getCallParam(param,"supervisor_level"));
        saveDataMap.put("updateTime", DateTime.now());
        saveDataMap.put("modifer", logAccount);

        // 根据type决定是否添加sql字段
        if ("自定义指标".equals(type)) {
            // 自定义指标需要sql字段
            String sql = (String) this.getCallParam(param, "sql");
            log.info("自定义指标，获取到的SQL: {}", sql);
            if (sql == null || sql.trim().isEmpty()) {
                log.error("自定义指标必须提供SQL语句");
                throw new RuntimeException("自定义指标必须提供SQL语句");
            }
            saveDataMap.put("sql", sql);
        } else if ("内置指标".equals(type)) {
            // 内置指标不需要sql字段，设置为null
            log.info("内置指标，sql字段设置为null");
            saveDataMap.put("sql", null);
        } else {
            log.warn("未知的指标类型: {}", type);
            saveDataMap.put("sql", null);
        }

        saveParams.put("data", saveDataMap);
        log.info("准备保存的数据: {}", saveParams);

        try {
            Object result = saveData.run(saveParams);
            log.info("保存操作结果: {}", result);

            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("result", "success");
            return resultMap;
        } catch (Exception e) {
            log.error("保存数据时发生错误: ", e);
            throw new RuntimeException("保存数据失败: " + e.getMessage(), e);
        }
    }
}



