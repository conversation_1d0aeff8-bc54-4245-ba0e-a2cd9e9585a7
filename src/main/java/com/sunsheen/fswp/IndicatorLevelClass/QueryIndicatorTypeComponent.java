package com.sunsheen.fswp.IndicatorLevelClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

/**
 * 根据指标ID查询指标类型组件
 */
@Controller("QueryIndicatorTypeComponent")
@BixComponentPackage(dirname = "查询指标类型", type = "BUSINESS")
public class QueryIndicatorTypeComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(QueryIndicatorTypeComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(name = "QueryIndicatorTypeComponent", memo = "根据指标ID查询指标类型")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicator_id", comment = "指标ID")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "指标信息，包含indicator_id、indicator_name、type")
    })
    @LogArgs
    public Object run(Map param) {
        try {
            // 获取指标ID参数
            Object indicatorIdObj = this.getCallParam(param, "indicator_id");
            if (indicatorIdObj == null) {
                logger.error("指标ID不能为空");
                throw new RuntimeException("指标ID不能为空");
            }

            String indicatorId = indicatorIdObj.toString().trim();
            if (indicatorId.isEmpty()) {
                logger.error("指标ID不能为空字符串");
                throw new RuntimeException("指标ID不能为空");
            }

            logger.info("开始查询指标ID: {} 的类型信息", indicatorId);

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "IndicatorLevel.queryIndicatorTypeById");
            queryParams.put("indicator_id", indicatorId);

            // 执行查询
            Map<String, Object> indicatorInfo = (Map<String, Object>) queryForMap.run(queryParams);

            if (indicatorInfo == null || indicatorInfo.isEmpty()) {
                logger.warn("未找到指标ID: {} 的信息", indicatorId);
                throw new RuntimeException("未找到指定的指标信息");
            }

            // 直接返回查询结果，框架会自动包装
            logger.info("成功查询到指标ID: {} 的类型信息，类型为: {}",
                       indicatorId, indicatorInfo.get("type"));

            return indicatorInfo;

        } catch (Exception e) {
            logger.error("查询指标类型失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询指标类型失败: " + e.getMessage(), e);
        }
    }
}
