package com.sunsheen.fswp.IndicatorLevelClass;

import cn.hutool.core.date.DateTime;
import com.mysql.cj.util.StringUtils;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("UpdateIndicatorLevelComponent")
@BixComponentPackage(dirname = "更新指标预警规则", type = "BUSINESS")
public class UpdateIndicatorLevelComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(name = "UpdateIndicatorLevelComponent", memo = "更新指标预警规则")
    @Params({
            @ParamItem(type = "java.lang.String", name = "id", comment = "指标预警规则的 ID"),
            @ParamItem(type = "java.lang.String", name = "indicatorId", comment = "指标 ID"),
            @ParamItem(type = "java.lang.String", name = "indicatorName", comment = "指标名称"),
            @ParamItem(type = "java.lang.String", name = "warningLevel", comment = "告警级别"),
            @ParamItem(type = "java.lang.String", name = "describe", comment = "预警规则描述"),
            @ParamItem(type = "java.lang.Integer", name = "supervisorLevel", comment = "监管级别"),
            @ParamItem(type = "java.lang.Integer", name = "threshold", comment = "阈值"),
            @ParamItem(type = "java.lang.String", name = "sql", comment = "SQL语句（自定义指标必填）")
    })
    @Returns(retValue = {@ReturnItem(type = "java.lang.Integer", name = "result", comment = "更新成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        Object idObj = this.getCallParam(param, "id");
        if (idObj == null) {
            throw new RuntimeException("ID 不能为空");
        }
        String logAccount = UserUtil.getLogAccount();
        if(StringUtils.isEmptyOrWhitespaceOnly(logAccount)){
            throw new RuntimeException("请登录！");
        }
        // 确保ID是正确的类型
        Integer numericId = null;
        try {
            numericId = Integer.parseInt(idObj.toString());
        } catch (NumberFormatException e) {
            throw new RuntimeException("ID格式不正确: " + idObj);
        }

        String indicatorId = (String) this.getCallParam(param, "indicatorId");
        String indicatorName = (String) this.getCallParam(param, "indicatorName");
        String warningLevel = (String) this.getCallParam(param, "warningLevel");
        String describe = (String) this.getCallParam(param, "describe");
        Integer supervisorLevel = (Integer) this.getCallParam(param, "supervisorLevel");
        Integer threshold = (Integer) this.getCallParam(param, "threshold");

        // 先查询指标的type信息
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "IndicatorLevel.queryIndicatorTypeById");
        queryParams.put("indicator_id", indicatorId);
//        Map<String, Object> queryData = new HashMap<>();
//        queryParams.put("data", queryData);

        Map<String, Object> indicatorInfo = (Map<String, Object>) queryForMap.run(queryParams);
        if (indicatorInfo == null) {
            throw new RuntimeException("未找到指定的指标信息");
        }

        String type = (String) indicatorInfo.get("type");

        Map<String, Object> updateParams = new HashMap<>();
        Map<String, Object> updateParam = new HashMap<>();
        updateParams.put("dataId", "IndicatorLevel.updateIndicatorLevel");
        updateParam.put("id", numericId);
        updateParam.put("indicator_id", indicatorId);
        updateParam.put("indicator_name", indicatorName);
        updateParam.put("warning_level", warningLevel);
        updateParam.put("describe", describe);
        updateParam.put("supervisor_level", supervisorLevel);
        updateParam.put("threshold", threshold);
        updateParam.put("updateTime", DateTime.now());
        updateParam.put("modifer", logAccount);

        // 根据type决定是否添加sql字段
        if ("自定义指标".equals(type)) {
            // 自定义指标需要sql字段
            String sql = (String) this.getCallParam(param, "sql");
            if (sql == null || sql.trim().isEmpty()) {
                throw new RuntimeException("自定义指标必须提供SQL语句");
            }
            updateParam.put("sql", sql);
        } else if ("内置指标".equals(type)) {
            // 内置指标不需要sql字段，设置为null或空字符串
            updateParam.put("sql", null);
        }

        updateParams.put("data", updateParam);
        return saveData.run(updateParams);
    }
}

