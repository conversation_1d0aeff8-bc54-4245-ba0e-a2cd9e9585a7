package com.sunsheen.fswp.IndicatorLevelClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("DeleteIndicatorLevelComponent")
@BixComponentPackage(dirname = "删除指标预警规则", type = "BUSINESS")
public class DeleteIndicatorLevelComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(name = "DeleteIndicatorLevelComponent", memo = "删除指标预警规则")
    @Params({
            @ParamItem(type = "java.lang.String", name = "id", comment = "指标预警规则的 ID")
    })
    @Returns(retValue = {@ReturnItem(type = "java.lang.Integer", name = "result", comment = "删除成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        Object idObj = this.getCallParam(param, "id");
        if (idObj == null) {
            throw new RuntimeException("ID 不能为空");
        }

        // 确保ID是正确的类型（可能是String或Integer）
        String id = idObj.toString();
        Integer numericId = null;
        try {
            numericId = Integer.parseInt(id);
        } catch (NumberFormatException e) {
            throw new RuntimeException("ID格式不正确: " + id);
        }

        // 先查询该预警规则的指标信息，获取type字段
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "IndicatorLevel.queryIndicatorLevelById");
        queryParams.put("id", numericId); // 直接将参数放在主Map中

        Map<String, Object> indicatorLevelInfo = (Map<String, Object>) queryForMap.run(queryParams);
        if (indicatorLevelInfo == null) {
            throw new RuntimeException("未找到指定的预警规则");
        }

        String type = (String) indicatorLevelInfo.get("type");

        // 权限控制已移除：内置指标和自定义指标都可以删除预警规则

        // 执行删除操作
        Map<String, Object> deleteParam = new HashMap<>();
        Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("dataId", "IndicatorLevel.deleteIndicatorLevel");
        deleteParam.put("id", numericId); // 使用数字类型的ID
        deleteParams.put("data", deleteParam);

        return saveData.run(deleteParams);
    }
}

