package com.sunsheen.fswp.ProjectWholeProgressClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Controller("ProjectWholeProgressSelectComponent")
@BixComponentPackage(dirname = "项目进度查询", type = "SYSTEM")
@Slf4j
public class ProjectWholeProgressSelectComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(
            name = "ProjectWholeProgressSelectComponent",
            memo = "项目总进度查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询内容")})
    @Override
    @LogArgs
    public Object run(Map param) {

        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            try {
                int page = Integer.parseInt(pageV);
                int pageSize = Integer.parseInt(pageSizeV);
                Integer start = (page - 1) * pageSize;
                Integer limit = pageSize;
                param.put("start", start);
                param.put("limit", limit);
            } catch (Exception e) {
                log.error("page和pageSize非法");
                throw new RuntimeException("page和pageSize非法");
            }
        }
        param.put("dataId", "ProjectWholeProgress.countSelectProjectwholeProgress");
        Long count = (Long) queryForObject.run(param);
        param.put("dataId", "ProjectWholeProgress.selectProjectwholeProgress");

        return Map.of(
                "count", count,
                "data", queryForList.run(param)
        );

    }
}
