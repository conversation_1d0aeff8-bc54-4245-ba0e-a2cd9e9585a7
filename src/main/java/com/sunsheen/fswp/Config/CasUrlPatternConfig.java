package com.sunsheen.fswp.Config;

import org.apereo.cas.client.boot.configuration.CasClientConfigurer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class CasUrlPatternConfig implements CasClientConfigurer {

	@Value("${cas.server-login-url}")
	private String casServerLoginUrl;

	@Value("${cas.client-host-url}")
	private String casClientHostUrl;

	@Value("${ignore-pattern}")
	private String casIgnorePattern;

	@Override
	public void configureAuthenticationFilter(FilterRegistrationBean authenticationFilter) {
		authenticationFilter.addUrlPatterns("/*");

		Map<String, String> initParameters = new HashMap<String, String>();
		initParameters.put("casServerLoginUrl", casServerLoginUrl);
		initParameters.put("serverName", casClientHostUrl);

		initParameters.put("ignoreUrlPatternType",
				"org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy");
		// 配置文件中设置要过滤拦截的路径
		initParameters.put("ignorePattern", casIgnorePattern);
		authenticationFilter.setInitParameters(initParameters);
		authenticationFilter.setOrder(1);
	}
}