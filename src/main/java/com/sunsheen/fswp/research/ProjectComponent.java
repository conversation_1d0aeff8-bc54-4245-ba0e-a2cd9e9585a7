package com.sunsheen.fswp.research;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("ProjectComponent")
@BixComponentPackage(dirname = "科研项目分页查询", type = "SYSTEM")
@Slf4j
public class ProjectComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Override
    @Component(
            name = "ProjectComponent",
            memo = "科研项目分页查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {
        HashMap<String,Object> tempDataCountMap = new HashMap<>();
        Integer page = Integer.valueOf(String.valueOf(param.get("page")));
        Integer pageSize = Integer.valueOf(String.valueOf(param.get("pageSize")));
        String type = String.valueOf(param.get("type"));
        String htmc = String.valueOf(param.get("htmc"));
        String dept = String.valueOf(param.get("dept"));
        tempDataCountMap.put("pageStart",(page-1)*pageSize);
        tempDataCountMap.put("pageSize",pageSize);
        tempDataCountMap.put("type",type);
        tempDataCountMap.put("htmc",htmc);
        tempDataCountMap.put("dept",dept);

        // 查询分页数据
        tempDataCountMap.put("dataId","research_showboard.select_all_project_info");
        List<Map<String, Object>> rows = (List<Map<String, Object>>) queryForList.run(tempDataCountMap);

        // 查询总数
        HashMap<String,Object> countParams = new HashMap<>();
        countParams.put("dataId","research_showboard.select_all_project_info_count");
        countParams.put("type",type);
        countParams.put("htmc",htmc);
        countParams.put("dept",dept);
        List<Map<String, Object>> countResult = (List<Map<String, Object>>) queryForList.run(countParams);
        int total = 0;
        if (countResult != null && !countResult.isEmpty()) {
            Object totalObj = countResult.get(0).get("total");
            if (totalObj != null) {
                total = Integer.parseInt(totalObj.toString());
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("rows", rows);
        return result;
    }
}
