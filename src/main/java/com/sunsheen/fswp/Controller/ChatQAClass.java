package com.sunsheen.fswp.Controller;


import com.sunsheen.fswp.chatAi.ChatBaseParam;
import com.sunsheen.fswp.chatAi.ChatMsgService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.Map;

@RestController
@RequestMapping("/chat")
public class ChatQAClass {

    @Autowired
    ChatMsgService chatMsgService;

    @PostMapping(value = "/main", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chat(@Valid @RequestBody ChatBaseParam param) {
        // ...
        return chatMsgService.baseChat(param);
    }

}
