package com.sunsheen.fswp.Controller;

import cn.hutool.core.date.DateTime;
// import com.sunsheen.fswp.util.TCPUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apereo.cas.client.authentication.AttributePrincipal;
import org.apereo.cas.client.util.AbstractCasFilter;
import org.apereo.cas.client.validation.Assertion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Controller
@Slf4j
public class LoginController {

    @Value(value = "${cas.server-url-prefix}")
    private String serverUrlPrefix = "";

    @Value(value = "${cas.server-login-url}")
    private String serverLoginUrl;

    @Value(value = "${front.url}")
    private String frontUrl = "";

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    private HttpServletRequest httpServletRequest;

    //心跳信息类
    private static class HeartbeatInfo {
        long timestamp;  // 最后心跳时间
        String ipAddress; // 用户IP地址

        HeartbeatInfo(long timestamp, String ipAddress) {
            this.timestamp = timestamp;
            this.ipAddress = ipAddress;
        }
    }

    private final ConcurrentHashMap<String, HeartbeatInfo> heartbeatMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    //初始化定时任务
    @PostConstruct
    public void initHeartbeatCheck() {
        // 每5分钟检测一次超时（
        executorService.scheduleAtFixedRate(this::checkHeartbeats,
                0, 5, TimeUnit.MINUTES);
    }

    @GetMapping("/fswp/heart")
    @ResponseBody
    public String handleHeartbeat(String nickName,HttpServletRequest request) {
        // 更新最后心跳时间为当前时间戳
        log.info("ip：{}",request.getRemoteAddr());
        heartbeatMap.put(nickName, new HeartbeatInfo(System.currentTimeMillis(), request.getRemoteAddr()));
        return "ok";
    }

    @RequestMapping("/casLogin")
    public void checkLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        String loginName = null;
        if (assertion != null) {
            AttributePrincipal principal = assertion.getPrincipal();
            loginName = principal.getName();

            recordUserLog(loginName,"login",request.getRemoteAddr());

            log.info("访问者:" + loginName);
        }
        //重定向到前端地址，并附带cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
               response.sendRedirect(frontUrl + "/main?"+cookies[0].getName() + "="+cookies[0].getValue());
            // response.sendRedirect("http://" + TCPUtil.getClientIp(request) + ":5173/main?" + cookies[0].getName() + "=" + cookies[0].getValue());
        }
    }

    @GetMapping("getUser")
    @ResponseBody
    public Map user(HttpServletRequest request, HttpSession session) {
        Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        String loginName = null;
        //1、获取sessionID
        String sessionId = session.getId();
        System.out.println("请求中的 sessionId: " + sessionId);

        if (assertion != null) {
            //2、取登录用户信息
            AttributePrincipal principal = assertion.getPrincipal();
            loginName = principal.getName();
            log.info("访问者:" + loginName);
        }
        Map<String,Object> selectMap = new HashMap<>();

//        selectMap.put("dataId","SysUser.selectByUser");
        selectMap.put("dataId","SysUser.selectUserByNickName");
        selectMap.put("nickName",loginName);
        Map<String,Object> result = (Map<String, Object>) queryForMap.run(selectMap);
        result.put("loginName",loginName);
        result.remove("nickName");
        return result;
    }

    @RequestMapping("/logout")
    public String logout(HttpServletRequest request, HttpServletResponse response) {
        log.info(UserUtil.getLogAccount() + "登出");

        recordUserLog(UserUtil.getLogAccount(),"logout",request.getRemoteAddr());

        //在心跳检测中移出该用户
        heartbeatMap.remove(Objects.requireNonNull(UserUtil.getLogAccount()));

        request.getSession().invalidate();

        // 编码内层 loginURL
        String encodedLoginUrl = URLEncoder.encode(serverLoginUrl + "?service=" + frontUrl, StandardCharsets.UTF_8);
        String serverLogoutUrl = "https://ywtb.cuit.edu.cn/authserver/logout?service=" + encodedLoginUrl;



        return "redirect:" + serverLogoutUrl;
    }


    /**
     * 新增：记录用户日志的通用方法
     */
    private void recordUserLog(String nickName, String logType,String IP) {
        try {
            //查询用户信息
            Map<String, Object> userQuery = new HashMap<>();
            userQuery.put("dataId", "SysUser.selectUserByNickName");
            userQuery.put("nickName", nickName);
            Map<String, Object> userInfo = (Map<String, Object>) queryForMap.run(userQuery);

            if (userInfo != null && !userInfo.isEmpty()) {
                //日志数据

                Map<String, Object> insertUserLogMap = new HashMap<>();

                Map<String, Object> logData = new HashMap<>();
                insertUserLogMap.put("dataId", "SysUserLog.insertUserLog");
                logData.put("nickName", nickName);
                logData.put("userName", userInfo.get("userName"));
                logData.put("dept", userInfo.get("dept"));
                logData.put("type", logType);
                logData.put("time", new DateTime());
                logData.put("ip", IP);

                //插入日志记录
                insertUserLogMap.put("data",logData);
                saveData.run(insertUserLogMap);

                log.info("成功记录{}日志: {}", logType, nickName);
            }
        } catch (Exception e) {
            log.error("记录{}日志失败: {}", logType, nickName, e);
        }
    }

    // 心跳超时检测方法
    private void checkHeartbeats() {
        final long now = System.currentTimeMillis();
        final long timeoutMillis = 10 * 60 * 1000; // 10分钟超时

        heartbeatMap.forEach((nickName, info) -> {
            if (now - info.timestamp > timeoutMillis) {
                heartbeatMap.remove(nickName);
                recordUserLog(nickName, "logout", info.ipAddress);
                log.info("用户[{}]心跳超时，已记录登出日志", nickName);
            }
        });
    }
}