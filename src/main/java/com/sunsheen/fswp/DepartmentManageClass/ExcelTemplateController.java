package com.sunsheen.fswp.DepartmentManageClass;

import com.sunsheen.fswp.DepartmentManageClass.service.ExcelTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping({"/excelTemplate"})
public class ExcelTemplateController {

    @Autowired
    public ExcelTemplateService excelTemplateService ;

    @GetMapping("/download-template")
    public ResponseEntity<Resource> downloadTemplate(){

        // 设置HTTP响应头
        return excelTemplateService.downloadTemplate();
    }
}
