package com.sunsheen.fswp.DepartmentManageClass;


import com.sunsheen.fswp.aop.LogArgs;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;


@Controller("ExcelTemplateComponent")
@BixComponentPackage(dirname = "excel模板下载", type = "SYSTEM")
@Slf4j
public class ExcelTemplateComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;
    
    @SneakyThrows
    @Override
    @Component(
            name = "ExcelTemplateComponent",
            memo = "excel模板下载"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        // 1. 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("数据模板");

        // 2. 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"姓名", "年龄", "邮箱", "部门"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            // 设置表头样式（可选）
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            cell.setCellStyle(headerStyle);
        }

        // 4. 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 5. 保存文件到临时目录
        String filePath = "template.xlsx";
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("模板已生成: " + filePath);

        // 加载文件
        Path filePaths = Paths.get("template.xlsx");
        Resource resource = new UrlResource(filePaths.toUri());
        // 设置HTTP响应头
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
    }

