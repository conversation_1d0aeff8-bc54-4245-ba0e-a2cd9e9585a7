package com.sunsheen.fswp.DepartmentManageClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.hutool.poi.excel.cell.CellUtil.getCellValue;

@Controller("ExeclUploadComponent")
@BixComponentPackage(dirname = "excel上传部门信息", type = "SYSTEM")
@Slf4j
public class ExeclUploadComponent extends ABaseComponent {
    // 改数据
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("FileUploadComponent")
    IDataPort upload;

    @SneakyThrows
    @Override
    @Component(
            name = "ExeclUploadComponent",
            memo = "excel上传部门信息"
    )
    @Params({@ParamItem(
            type = "java.lang.String",
            name = "dataId",
            comment = "执行的dataId，可包含通配符{xxx}"
    ), @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源查询参数"
    )})
    @Returns(
            retValue = {@ReturnItem(
                    type = "java.lang.Object",
                    name = "data",
                    comment = "返回保存成功的数据条数"
            )}
    )
    @LogArgs
    public Object run(Map param) {

        ArrayList<MultipartFile> fileList = (ArrayList) param.get("files");
        Map<String,Object> dataMap = (HashMap) param.get("data");
        ArrayList<Map> listData = new ArrayList<>();
        HashMap<Integer,Map> tempMap = new HashMap();
        for (MultipartFile file : fileList) {
                InputStream inputStream = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream);
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();

                // 校验标题行
                Row headerRow = sheet.getRow(0);
                if (headerRow == null) {
                    throw new RuntimeException("标题行不存在！");
                }
                // 校验标题数量
                List<String> expectedHeaders = Arrays.asList("学校ID","学校名称","二级部门ID","二级部门名称","三级部门ID","三级部门名称");
                if (headerRow.getLastCellNum() != expectedHeaders.size()) {
                    throw new RuntimeException("标题列数量不匹配！");
                }
                // 校验每个标题内容
                for (int i = 0; i < expectedHeaders.size(); i++) {
                    Cell cell = headerRow.getCell(i);
                    String actualHeader = (cell != null) ? cell.getStringCellValue().trim() : "";
                    String expectedHeader = expectedHeaders.get(i).trim();
                    if (!actualHeader.equals(expectedHeader)) {
                        throw new RuntimeException("第 " + (i+1) + " 列标题不匹配，期望 '" + expectedHeader+"',"+" 实际 '" + actualHeader + "'");
                    }
                }
                // 跳过标题行
                if (rowIterator.hasNext()) {
                    rowIterator.next();
                }
                Integer num = 0;
                while (rowIterator.hasNext()) {
                    Map<String,Object> map = new HashMap<>();
                    Row row = rowIterator.next();
                    // Excel列顺序
                    map.put("ID",String.valueOf(getCellValue(row.getCell(0))));
                    map.put("university",getCellValue(row.getCell(1)));
                    map.put("dept_id",String.valueOf(getCellValue(row.getCell(2))));
                    map.put("dept_name",getCellValue(row.getCell(3)));
                    map.put("sub_dept_id",String.valueOf(getCellValue(row.getCell(4))));
                    map.put("sub_dept_name",getCellValue(row.getCell(5)));
                    tempMap.put(num,map);
                    num++;
                }
        }

        // 重复校验
        if (tempMap.size() > 0) {
            for (Map.Entry<Integer,Map> entry : tempMap.entrySet()) {
                Map<String,Object> newMap = entry.getValue();
                String id = (String) newMap.get("ID");
                String deptId = (String) newMap.get("dept_id");
                String subDeptId = (String) newMap.get("sub_dept_id");
                HashMap<String,Object> newData = new HashMap<>();
                newData.put("ID",id);
                newData.put("dept_id",deptId);
                newData.put("sub_dept_id",subDeptId);
                newData.put("dataId","ExcelUpload.selectById");
                List<Map<String,Object>> list = (List<Map<String, Object>>) this.queryForList.run(newData);
                if (list.size() > 0) {
                    throw new RuntimeException(id+"_"+deptId+"_"+subDeptId+"部门已存在，请校验！")         ;
                }
            }
        }
        // 修改人和修改日期
        HashMap<String,Object> nameMap = new HashMap<>();
//        nameMap.put("dataId","SysUser.selectByUser");
        nameMap.put("dataId","SysUser.selectUserByNickName");
        nameMap.put("nickName",dataMap.get("userId"));
        List<HashMap> nameList = (List<HashMap>) queryForList.run(nameMap);
        Object first = ((List<?>) queryForList.run(nameMap)).getFirst();
        Object name = ((LinkedCaseInsensitiveMap) first).get("userName");
        Date currentDate = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        // 格式化日期
        String formattedDate = formatter.format(currentDate);
        // 将数据存入数据库
        if (tempMap.size() > 0) {
            for (Map.Entry<Integer,Map> entry : tempMap.entrySet()) {
                Map<String,Object> saveMap = new HashMap<>();
                Map<String,Object> newMap = entry.getValue();
                newMap.put("editor",name);
                newMap.put("edit_date",formattedDate);
                newMap.putAll(entry.getValue());
                saveMap.put("data",newMap);
                saveMap.put("dataId","ExcelUpload.insertDepart");
                saveData.run(saveMap);
            }
        }
        return null;
    }
}
