package com.sunsheen.fswp.DepartmentManageClass.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
@Slf4j
public class ExcelTemplateService {

    public ResponseEntity<Resource> downloadTemplate(){
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("数据模板");

        // 2. 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"学校ID", "学校名称", "二级部门ID", "二级部门名称", "三级部门ID","三级部门名称"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            // 设置表头样式（可选）
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            cell.setCellStyle(headerStyle);
        }

        // 4. 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 5. 保存文件到临时目录
        String filePath = "template.xlsx";
        try (
                FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
        } catch (
                IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("模板已生成: " + filePath);

        // 加载文件
        Path filePaths = Paths.get("template.xlsx");
        Resource resource = null;
        try {
            resource = new UrlResource(filePaths.toUri());
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }

        // 设置HTTP响应头
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
}
