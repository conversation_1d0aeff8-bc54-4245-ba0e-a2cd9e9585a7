package com.sunsheen.fswp.upload.service;

//import com.aspose.pdf.Document;
//import com.aspose.words.HtmlSaveOptions;
//import com.aspose.words.SaveFormat;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.protobuf.ServiceException;
import com.sunsheen.fswp.SysUserClass.SelectDeptListComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.sunsheen.fswp.AIAgentClass.StreamingApiClient.processStreamingResponse;


@Service
@Slf4j
public class ReportUploadService {
    @Autowired
    SelectDeptListComponent selectDeptListComponent;


    private static final String PATH = "/var/uploads/ReportsUploads/";

//     private static final String PATH = "D://var/uploads/LawsUploads/";


    @SneakyThrows
    public List<String> uploadFile() {
        Map<String, Object> params = new HashMap<>();
        params.put("dataId", "SysUser.selectDeptList");
        Map<String, Object> deptMap = (Map<String, Object>) selectDeptListComponent.run(params);
//        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) deptMap.get("data");
        List<String> urlList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            System.out.println(map.get("dept") + "_" + map.get("deptId"));
            String fileName = "体检报告_" + map.get("dept") + ".pdf";
            String API_URL = "http://api.agent-15.default.epai.cuit.edu.cn:30080/v1/chat/completions";
            try {
//                String requestBody = "{\"model\":\"ddd\",\"messages\":[{\"role\":\"user\",\"content\":\"体检报告\"}],\"stream\":true}";
                String requestBody = "{\"model\":\"ddd\",\"messages\":[{\"role\":\"user\",\"content\":\"体检报告_" + map.get("dept") + "\"}],\"stream\":true}";
                processStreamingResponse(API_URL, requestBody, PATH + fileName);

//                return PATH + fileName; // 返回完整路径
                urlList.add(PATH + fileName);
            } catch (IOException e) {
                System.err.println("流式处理错误：" + e.getMessage());
                e.printStackTrace();
//                return null; // 失败时返回null
            }
        }

        return urlList;
    }

    @SneakyThrows
    public Object downloadFileFromServer(String department_name) {
        String fileName = "体检报告_" + department_name + ".pdf";

        String fileAddr = PATH + fileName;
        // 获取文件对象
        File file = new File(fileAddr);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在！");
        }
        Path filePath = Paths.get(fileAddr);
        Resource resource = new UrlResource(filePath.toUri());
        if (resource == null || !resource.exists()) {
            throw new RuntimeException("PDF文件不存在！");
        }

        // 确保文件名是PDF扩展名
        String pdfFileName = ensurePdfExtension(fileName);

        // 使用UTF-8编码文件名，防止中文乱码
        String encodedFileName = URLEncoder.encode(pdfFileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");

        // 设置Content-Disposition头，强制下载并指定文件名
        String contentDisposition = "attachment; filename=\"" + encodedFileName + "\"";

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                .body(resource);
    }


    private Resource loadPdfResource(String fileName) {
        // 实际实现中，这里应该根据文件名加载PDF资源
        // 例如：return new ClassPathResource("pdfs/" + fileName);
        return null; // 占位实现，实际应返回PDF资源
    }

    private String ensurePdfExtension(String fileName) {
        if (!fileName.toLowerCase().endsWith(".pdf")) {
            return fileName + ".pdf";
        }
        return fileName;
    }

}
