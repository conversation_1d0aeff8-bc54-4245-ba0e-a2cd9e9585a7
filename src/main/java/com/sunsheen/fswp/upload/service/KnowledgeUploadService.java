package com.sunsheen.fswp.upload.service;

//import com.aspose.pdf.Document;
//import com.aspose.words.HtmlSaveOptions;
//import com.aspose.words.SaveFormat;

import com.google.protobuf.ServiceException;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * Author: chentong
 * date: 2025/3/26
 * 知识库——政策法规业务实现层
 */

@Service
@Slf4j
public class KnowledgeUploadService{

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("GridDataComponent")
    IDataPort gridQueryForList;


    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

//     private static final String PATH = System.getProperty("user.dir")+"/var/uploads/";
    private static final String PATH = "/var/uploads/LawsUploads/";

    // private static final String PATH = "D://var/uploads/LawsUploads/";

    /**
     * 导入政策法规
     */
    public Object importKonwlege(MultipartFile file,String topic,String lawName,String publisher,long time,String fileNo,String userName) {

        HashMap<String,Object> resultMap = new HashMap<>();
        Map<String,Object> tempMap = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();

        // 文件判空
        if (file.isEmpty()) {
            try{
                throw new RuntimeException("文件为空，请重新上传！");
            }catch (RuntimeException e) {
                resultMap.put("code", 0);
                resultMap.put("msg", e.getMessage());
            }
        }

        // 法规政策名判重
        HashMap<String,Object> ifTitle = new HashMap<>();
        ifTitle.put("dataId","KnowledgeUpload.selectLawsByTitle");
        ifTitle.put("title",lawName);
        List<HashMap> lawList = (List<HashMap>) this.queryForList.run(ifTitle);
        if (lawList == null || lawList.size() == 0) {
            try{
                throw new RuntimeException("法规政策名已存在，请修改！");
            }catch (RuntimeException e) {
                resultMap.put("code", 0);
                resultMap.put("msg", e.getMessage());
            }
        }

        // 文件上传
        File upLoadFile = new File(PATH);
        if (!upLoadFile.exists()) {
            upLoadFile.mkdirs();
        }
        File tempFile = new File(PATH + file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
            resultMap.put("code", 1);
            resultMap.put("msg", "文件上传成功！");
        } catch (IOException e) {
            log.error(e.getMessage());
            resultMap.put("code", 0);
            resultMap.put("msg", "文件上传失败！"+e.getMessage());
        }

        // 法规政策名
        dataMap.put("title",lawName);
        // 文件编号
        dataMap.put("file_no",fileNo);
        // 主题
        dataMap.put("subject",topic);
        // 文件地址
        dataMap.put("file_addr",PATH+file.getOriginalFilename());
        // 发布单位
        dataMap.put("publisher",publisher);
        // 发布日期格式化
        Instant instant = Instant.ofEpochMilli(time);
        LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dataMap.put("pub_date",utcDateTime.format(formatter));
        // 编辑日期
        dataMap.put("edit_date",utcDateTime.format(formatter));
        // 有效状态
        dataMap.put("valid_status",1);
        // 根据用户名获取姓名
        HashMap<String,Object> nameMap = new HashMap<>();
//        nameMap.put("dataId","SysUser.selectByUser");
        nameMap.put("dataId","SysUser.selectUserByNickName");
        nameMap.put("nickName",userName);
        List<HashMap> nameList = (List<HashMap>) queryForList.run(nameMap);
        Object first = ((List<?>) queryForList.run(nameMap)).getFirst();
        Object name = ((LinkedCaseInsensitiveMap) first).get("userName");
        tempMap.put("editor",name);
        // 存入数据库
        tempMap.put("dataId","KnowledgeUpload.insertKonwle");
        tempMap.put("data",dataMap);
        Integer saveDataMap = (Integer) saveData.run(tempMap);

        return resultMap;
    }



    /**
     * 查询法律法规政策
     * param: page,pageSize
     */

    public Object selectLaws(Integer page, Integer pageSize){
        HashMap<String,Object> resultMap = new HashMap<>();
        HashMap<String,Object> tempDataMap = new HashMap<>();

        // 分页查询
        List<Map> tempDataList = new ArrayList<>();
        tempDataMap.put("dataId","KnowledgeUpload.selectLawsInfo");
        tempDataMap.put("start", (page - 1) * pageSize);
        tempDataMap.put("limit", pageSize);
        tempDataList = (List) this.queryForList.run(tempDataMap);

        // 映射文件地址
        tempDataList.forEach(law -> {
            log.info("{}");
            File file = new File(law.get("file_addr").toString());
            // law.put("url", "var/uploads/LawsUploads/" + file.getName());
            law.put("url", "/fswp/download/laws/" + file.getName()); // 调下载接口

            law.remove("file_addr");
        });

        // 查询数据总条数
        HashMap<String,Object> countMap = new HashMap<>();
        countMap.put("dataId","KnowledgeUpload.selectLawsCount");
        List<Map> countList = (List<Map>) this.queryForList.run(countMap);
        Map<String,Object> counts = countList.get(0);
        resultMap.put("data",tempDataList);
        resultMap.put("code",2);
        resultMap.putAll(counts);
        return resultMap;
    }

    /**
     * 删除政策法规
     */
        public Object deleteLaws(String lawName){
            HashMap<String,Object> resultMap = new HashMap<>();
            HashMap<String,Object> tempDataMap = new HashMap<>();
            HashMap<String,Object> deleteMap = new HashMap<>();
            // 判断删除的政策法规是否存在
            HashMap<String,Object> dataMap = new HashMap<>();
            dataMap.put("dataId","KnowledgeUpload.selectLaws");
            List<Map> countList = (List<Map>) this.queryForList.run(dataMap);
            if (countList == null || countList.size() == 0) {
                try{
                    throw new RuntimeException("删除的政策法规不存在");
                }catch (RuntimeException e) {
                    resultMap.put("code", 0);
                    resultMap.put("msg", e.getMessage());
                }
            }
            // 编辑时间
            Instant instant = Instant.ofEpochMilli(new Date().getTime());
            LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            deleteMap.put("expire_date",utcDateTime.format(formatter));
            deleteMap.put("title",lawName);
            tempDataMap.put("dataId","KnowledgeUpload.deleteLaws");
            tempDataMap.put("data",deleteMap);
            Integer isDelete = (Integer) saveData.run(tempDataMap);
            if (!isDelete.equals(0)){
                resultMap.put("code", 1);
                resultMap.put("result", "政策法规删除成功！");
            }else {
                resultMap.put("code", 0);
                resultMap.put("result", "政策法规删除失败，请检查!");
            }
            return resultMap;
            }


    /**
     *更新政策法规
     */

    public Object updateLaws(String oldTitle,String newTitle, String publisher, Long pub_date, MultipartFile file){
        HashMap<String,Object> resultMap = new HashMap<>();
        HashMap<String,Object> dataMap = new HashMap<>();
        // 文件判空
        if (file.isEmpty()) {
            try{
                throw new RuntimeException("文件为空，请重新上传！");
            }catch (RuntimeException e) {
                resultMap.put("code", 0);
                resultMap.put("msg", e.getMessage());
            }
        }
        // 文件上传
        File upLoadFile = new File(PATH);
        if (!upLoadFile.exists()) {
            upLoadFile.mkdirs();
        }
        File tempFile = new File(PATH + file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
        } catch (IOException e) {
            try {
                throw new ServiceException("文件上传失败，原因：" + e.getMessage(), e);
            } catch (ServiceException ex) {
                resultMap.put("code", 0);
                resultMap.put("msg", "文件上传失败！"+e.getMessage());
            }
        }
        // 发布日期
        Instant instant = Instant.ofEpochMilli(pub_date);
        LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dataMap.put("pub_date",utcDateTime.format(formatter));
        // 编辑日期
        Instant editTime = Instant.ofEpochMilli(new Date().getTime());
        LocalDateTime utcEditTime = LocalDateTime.ofInstant(editTime, ZoneId.of("UTC"));
        DateTimeFormatter formar = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dataMap.put("expire_date",utcEditTime.format(formar));
        // 文件地址
        dataMap.put("file_addr",PATH+file.getOriginalFilename());
        if (newTitle.length()>0){
            dataMap.put("title",newTitle);
        }else {
            dataMap.put("title",oldTitle);
        }
        dataMap.put("oldTitle",oldTitle);
        dataMap.put("publisher",publisher);
        // 更新
        Map<String,Object> tempMap = new HashMap<>();
        tempMap.put("dataId","KnowledgeUpload.updateLaws");
        tempMap.put("data",dataMap);
        Integer saveDataMap = (Integer) saveData.run(tempMap);
        if (saveDataMap.equals(0)){
            resultMap.put("code", 0);
            resultMap.put("msg","更新失败！");
        }else {
            resultMap.put("code", 1);
            resultMap.put("msg","更新成功！");
        }
        return resultMap;
    }


    /**
     * 条件查询
     */
    public Object selectLawsBy(String title,String publisher, String pub_date, String subject,int limit,int start){
        Map<String,Object> selectMap = new HashMap<>();
        selectMap.put("dataId","KnowledgeUpload.selectLawsBy");
        selectMap.put("title",title);
        selectMap.put("publisher",publisher);
        selectMap.put("pub_date",pub_date);
        selectMap.put("subject",subject);
        selectMap.put("limit",limit);
        selectMap.put("start",start);

        return gridQueryForList.run(selectMap);

    }

    /**
     * 文件下载
     */
    @SneakyThrows
    public Object downloadLawFile(String fileName) {

        String fileAddr = PATH + fileName;
        // 获取文件对象
        File file = new File(fileAddr);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在！");
        }
        Path filePath = Paths.get(fileAddr);
        Resource resource = new UrlResource(filePath.toUri());

        if (!resource.exists()) {
            throw new RuntimeException("文件不存在！");
        }

        // 使用 Spring 的工具类编码
        String encodedFileName = UriUtils.encode(resource.getFilename(), StandardCharsets.UTF_8);

        String contentDisposition = "attachment; filename*=UTF-8''" + encodedFileName;

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
                .body(resource);
    }


    /**
     * 文档在线预览
     */
//    public Object onlinePreview(String fileName){
//        try {
//
//            Path filePath = Paths.get(PATH, fileName);
//            File file = filePath.toFile();
//
//            if (!file.exists() || !file.isFile()) {
//                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("File not found");
//            }
//
//            String contentType = Files.probeContentType(filePath);
//            if (contentType == null) {
//                return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body("Unsupported file type");
//            }
//
//            if (contentType.equals("application/pdf")) {
//                return convertPdfToHtml(file);
//            } else if (contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
//                    contentType.equals("application/msword")) {
//                return convertWordToHtml(file);
//            } else {
//                return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body("Unsupported file type");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal server error");
//        }
//    }

//    private ResponseEntity<String> convertPdfToHtml(File file) throws Exception {
//        Document pdfDocument = new Document(file.getAbsolutePath());
//        ByteArrayOutputStream htmlStream = new ByteArrayOutputStream();
//        HtmlSaveOptions saveOptions = new HtmlSaveOptions();
//        pdfDocument.save(htmlStream);
//        return ResponseEntity.ok().body(htmlStream.toString());
//    }
//
//    private ResponseEntity<String> convertWordToHtml(File file) throws Exception {
//        com.aspose.words.Document doc = new com.aspose.words.Document(file.getAbsolutePath());
//        ByteArrayOutputStream htmlStream = new ByteArrayOutputStream();
//        doc.save(htmlStream, SaveFormat.HTML);
//        return ResponseEntity.ok().body(htmlStream.toString());
//    }

}


