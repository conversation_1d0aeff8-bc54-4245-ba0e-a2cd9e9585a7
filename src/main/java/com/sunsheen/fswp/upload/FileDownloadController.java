package com.sunsheen.fswp.upload;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.upload.service.KnowledgeUploadService;
import com.sunsheen.fswp.upload.service.ReportUploadService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import java.text.MessageFormat;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/download")
public class FileDownloadController {
    @Autowired
    private KnowledgeUploadService knowledgeUploadService;
    @Autowired
    private ReportUploadService reportUploadService;

    /**
     * 下载文件
     * @param fileName
     * @return
     */
    @SneakyThrows
    @GetMapping("/laws/{fileName}.{extension}")
    @LogArgs
    public Object downloadFile(@PathVariable(value = "fileName") String fileName,
                               @PathVariable(value = "extension") String extension) {
        return knowledgeUploadService.downloadLawFile(MessageFormat.format("{0}.{1}", fileName, extension));
    }

    @SneakyThrows
    @GetMapping("/downloadReports")
    @LogArgs
    public Object downloadReport(@RequestParam(value = "department_name") String department_name) {
        return reportUploadService.downloadFileFromServer(department_name);
    }

    @SneakyThrows
    @PostMapping("/reports")
    @LogArgs
    public Object report(@RequestBody Map<String, Object> body) {
    Map<String, Object> departmentMap = (Map<String, Object>) body.get("departmentMap");
    if (departmentMap == null || departmentMap.get("dept") == null) {
        throw new IllegalArgumentException("参数 'dept' 不能为空");
    }
       return reportUploadService.downloadFileFromServer(departmentMap.get("dept").toString());
    }
}
