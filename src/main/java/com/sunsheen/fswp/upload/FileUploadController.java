package com.sunsheen.fswp.upload;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.upload.service.KnowledgeUploadService;
import com.sunsheen.fswp.upload.service.ReportUploadService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * Author: chentong
 * date: 2025/3/25
 * 知识库——政策法规接口层
 */

@RestController
@RequestMapping({"/upload"})
public class FileUploadController {

    @Autowired
    private KnowledgeUploadService konwledgeUploadService;
    @Autowired
    private ReportUploadService reportUploadService;


    /**
     * 导入政策法规
     *
     * @param file
     * @return
     */
    @LogArgs
    @PostMapping("/importLaws")
    public Object importLaws(@RequestParam("file") MultipartFile file,@RequestParam("topic") String topic,@RequestParam("lawName") String lawName
    ,@RequestParam("publisher") String publisher, @RequestParam("time") long time,@RequestParam("fileNo") String fileNo,
                             @RequestParam("userName") String userName) {
        return konwledgeUploadService.importKonwlege(file,topic,lawName,publisher,time,fileNo,userName);
    }



    /**
     * 查询法律法规政策
     * @param: page,pageSize
     *
     */
    @LogArgs
    @GetMapping("/selectLaws")
    public Object selectLaws(@RequestParam("page") Integer page,@RequestParam("pageSize") Integer pageSize) {
        return konwledgeUploadService.selectLaws(page,pageSize);
    }


    /**
     * 删除法律法规政策
     * @param: lawName
     */
    @LogArgs
    @PostMapping("/deleteLaws")
    public Object deleteLaws(@RequestParam("lawName") String lawName) {
        return konwledgeUploadService.deleteLaws(lawName);
    }


    /**
     * 删除法律法规政策
     * @param: lawName
     */
    @LogArgs
    @PostMapping("/updateLaws")
    public Object updateLaws(@RequestParam("oldTitle") String oldTitle,@RequestParam("newTitle") String newTitle,@RequestParam("publisher") String publisher,
                             @RequestParam("pub_date") Long pub_date, @RequestParam("file") MultipartFile file) {
        return konwledgeUploadService.updateLaws(oldTitle,newTitle,publisher,pub_date,file);
    }


    /**
     * 条件查询法律法规政策
     * @param: lawName
     */
    @LogArgs
    @GetMapping("/selectLawsBy")
    public Object selectLawsBy(@RequestParam(required = false,name = "title") String title,
                               @RequestParam(required = false,name = "publisher") String publisher,
                               @RequestParam(required = false,name = "pub_date") String pub_date,
                               @RequestParam(required = false,name = "subject") String subject,
                               @RequestParam(required = false,name = "limit",defaultValue = "10") int limit,
                               @RequestParam(required = false,name = "start",defaultValue = "0") int start
                               ) {
        return konwledgeUploadService.selectLawsBy(title,publisher,pub_date,subject,limit,start);
    }

    @SneakyThrows
    @GetMapping("/uploadTest")
    @LogArgs
    @Scheduled(cron = "0 0 1 * * ?")
    public void uploadTest() {
        try {
            reportUploadService.uploadFile();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @SneakyThrows
    @GetMapping("/uploadReport")
    @LogArgs
    @Scheduled(cron = "0 00 1 * * ?")
    public String uploadReport() {
        String string = "111111111";
        try {
            String sucess = "定时任务已启动！！";
            return string + sucess;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return string;

    }

}