package com.sunsheen.fswp.DepartmenDutiesClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;

@Controller("DepartmentPersonTaskSelectComponent")
@BixComponentPackage(dirname = "部门人员岗位职责信息展示", type = "SYSTEM")
@Slf4j
public class DepartmentPersonTaskSelectComponent extends ABaseComponent {

    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Component(name = "DepartmentSelectComponent", memo = "部门人员岗位职责信息展示")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @LogArgs
    @Override
    public Object run(Map param) {
        Map<String, Object> pageParamsMap = DBUtil.getLimitPageParams(param);
        Map<String, Object> queryParamMap = new HashMap<String, Object>(param);
        queryParamMap.put("dataId", "DepartmentDuties.selectDepartmentPersonTask");
        queryParamMap.put("participantId", param.get("participantId"));
        // 先执行计数查询获取总记录数
        Map<String, Object> countParamMap = new HashMap<>(queryParamMap);
        countParamMap.put("dataId", "DepartmentDuties.countDepartmentPersonTask");
        Long count = (Long) queryForObject.run(countParamMap);
        queryParamMap.putAll(pageParamsMap);
        // 执行分页查询
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) queryForList.run(queryParamMap);
        // 构建返回结果，包含数据列表和总记录数
        Map<String, Object> result = new HashMap<>();
        result.put("dataList", Optional.ofNullable(dataList).orElse(Collections.emptyList()));
        result.put("count", count);
        return result;
    }
}
