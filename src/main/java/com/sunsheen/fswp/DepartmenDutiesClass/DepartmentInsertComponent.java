package com.sunsheen.fswp.DepartmenDutiesClass;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("DepartmentInsertComponent")
@BixComponentPackage(dirname = "部门职责信息存入", type = "SYSTEM")
@Slf4j
public class DepartmentInsertComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;
    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Override
    @Component(
            name = "BusinessInsertComponent",
            memo = "部门职责信息存入"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;
        String account = UserUtil.getLogAccount();
        Map<String, Object> runParam = new HashMap<>();
        runParam.put("nickName", account);
        runParam.put("dataId", "SysUser.selectDeptIdByUser");
        Map<String, Object> tempDataMap = (Map<String, Object>) this.queryForMap.run(runParam);
        try {
            Object dataObj = param.get("data");
            if (dataObj == null) {
                result.put("success", false);
                result.put("message", "参数 data 不能为空");
                return result;
            }

            if (!(dataObj instanceof List)) {
                result.put("success", false);
                result.put("message", "参数 data 必须是 List 类型");
                return result;
            }

            List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataObj;
            if (dataList.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有数据需要插入");
                return result;
            }

            for (Map<String, Object> dataMap : dataList) {
                try {
                    HashMap<String, Object> tempData = new HashMap<>();
                    tempData.put("task_name", dataMap.get("task_name"));
                    tempData.put("description", dataMap.get("description"));
                    tempData.put("department_id", tempDataMap.get("deptId"));
                    tempData.put("department_name", tempDataMap.get("dept"));

                    // 使用 Timestamp 类型
                    Date now = new Date();
                    tempData.put("created_at", new java.sql.Timestamp(now.getTime()));
                    log.info("插入数据前的时间: {}", now);
                    // 准备插入参数
                    HashMap<String, Object> insertMap = new HashMap<>();
                    insertMap.put("dataId", "DepartmentDuties.insertDepartmentDuties");
                    insertMap.put("data", tempData);

                    // 执行插入
                    saveData.run(insertMap);
                    successCount++;
                } catch (Exception e) {
                    log.error("插入单条部门职责数据失败", e);
                    failureCount++;
                }
            }

            result.put("success", true);
            result.put("message", "插入完成");
            result.put("total", dataList.size());
            result.put("successCount", successCount);
            result.put("failureCount", failureCount);

        } catch (Exception e) {
            log.error("处理部门职责插入请求失败", e);
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }

        return result;
    }
}
