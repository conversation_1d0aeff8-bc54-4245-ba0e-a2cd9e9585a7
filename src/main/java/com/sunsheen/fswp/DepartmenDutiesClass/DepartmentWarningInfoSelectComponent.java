package com.sunsheen.fswp.DepartmenDutiesClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller("DepartmentWarningInfoSelectComponent")
@BixComponentPackage(dirname = "部门告警信息展示", type = "SYSTEM")
@Slf4j
public class DepartmentWarningInfoSelectComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;
    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;


    @Component(
            name = "DepartmentWarningInfoSelectComponent",
            memo = "部门告警信息展示"
    )
    @Params({@ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        // 参数校验
        if (param == null) {
            log.warn("run方法接收到null参数");
            return Collections.emptyList();
        }

        // 获取当前登录账号
        String account = UserUtil.getLogAccount();
        if (account == null) {
            log.warn("无法获取当前登录账号");
            return Collections.emptyList();
        }

        // 查询用户部门信息
        Map<String, Object> deptParam = Map.of(
                "nickName", account,
                "dataId", "SysUser.selectDeptIdByUser"
        );

        Map<String, Object> deptResult;
        try {
            deptResult = (Map<String, Object>) queryForMap.run(deptParam);
        } catch (Exception e) {
            log.error("查询用户部门信息失败，account={}", account, e);
            return Collections.emptyList();
        }

        // 检查部门信息是否存在
        if (deptResult == null || deptResult.get("dept") == null) {
            log.warn("未找到用户的部门信息，account={}", account);
            return Collections.emptyList();
        }


        // 查询部门警告信息
        String deptName = deptResult.get("dept").toString();


        Map<String, Object> warningParam = new HashMap<>();
        warningParam.put("dataId", "WarningList.selectWarningInfoByDepartment");
        // 查询参数的当前部门
        String currentDepartment = (String) param.get("department_name");
        if (!"全校".equals(currentDepartment)) {
            warningParam.put("dept_name", currentDepartment);
        }

        try {
            return queryForList.run(warningParam);
        } catch (Exception e) {
            log.error("查询部门警告信息失败，deptName={}", deptName, e);
            return Collections.emptyList();
        }
    }

}
