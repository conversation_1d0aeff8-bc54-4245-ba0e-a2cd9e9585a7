package com.sunsheen.fswp.DepartmenDutiesClass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("DepartmentUpdateComponent")
@BixComponentPackage(dirname = "部门职责信息更新", type = "SYSTEM")
@Slf4j
public class DepartmentUpdateComponent extends ABaseComponent {
    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @SneakyThrows
    @Override
    @Component(
            name = "DepartmentInsertComponent",
            memo = "部门职责信息更新信息更新"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        List<String> args = Arrays.asList("id", "task_name", "description", "department_id", "department_name");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });
        String id = this.getCallParam(param, "id").toString();

        String departmentName = queryForList.run(Map.of(
                "dataId", "DepartmentDuties.selectDepartmentDutiesById",
                "id", id
        )).toString();

        if (departmentName == null || departmentName.isEmpty()) {
            log.error("部门不存在!");
            throw new RuntimeException("部门不存在!");
        }

        param.put("departmentName", departmentName);
        return saveData.run(Map.of(
                "dataId", "DepartmentDuties.updateDepartmentDuties",
                "data", param
        ));
    }
}
