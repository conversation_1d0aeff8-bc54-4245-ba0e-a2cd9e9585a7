package com.sunsheen.fswp.DepartmenDutiesClass;

import com.sunsheen.fswp.aop.LogArgs;

import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.*;


@Controller("DepartmentSelectComponent")
@BixComponentPackage(dirname = "部门职责信息展示", type = "SYSTEM")
@Slf4j
public class DepartmentSelectComponent extends ABaseComponent {
    // 查数据
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryForMap;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;


    @Component(
            name = "DepartmentSelectComponent",
            memo = "部门职责信息展示"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    public Object run(Map param) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> pageParamsMap = DBUtil.getLimitPageParams(param);
        String account = UserUtil.getLogAccount();
        Map<String, Object> runParam = new HashMap<>();
        runParam.put("nickName", account);
        runParam.put("dataId", "SysUser.selectDeptIdByUser");
        Map<String, Object> tempDataMap = (Map<String, Object>) this.queryForMap.run(runParam);
        // 初始化结果Map，使用泛型明确键值类型
        try {
            // 准备查询参数
            Map<String, Object> queryParam = new HashMap<>(param);
//            if(roleName == ""){
//                queryParam.put("dataId", "DepartmentDuties.selectDepartmentDuties");
//            }
            queryParam.put("dataId", "DepartmentDuties.selectCountDepartmentDutiesByDepartmentId");
            queryParam.put("department_id", tempDataMap.get("deptId").toString());
            Long count = (Long) queryForObject.run(queryParam);
            queryParam.put("dataId", "DepartmentDuties.selectDepartmentDutiesByDepartmentId");
            queryParam.putAll(pageParamsMap);
            // 执行分页查询
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) queryForList.run(queryParam);
            // 组装返回结果
            resultMap.put("count", count);
            resultMap.put("data", dataList);
            resultMap.put("code", 2);
            resultMap.put("success", true);
        } catch (Exception e) {
            // 统一异常处理
            log.error("执行部门职责分页查询失败", e);
            resultMap.put("code", 500);
            resultMap.put("success", false);
            resultMap.put("message", "系统错误：" + e.getMessage());
        }
        return resultMap;
    }

}
