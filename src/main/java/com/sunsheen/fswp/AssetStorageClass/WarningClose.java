package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import java.util.Date;

import java.util.HashMap;
import java.util.Map;

@Controller("WarningCloseComponent")
@BixComponentPackage(dirname = "预警关闭", type = "SYSTEM")
@Slf4j
public class WarningClose extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    private IDataPort saveData;

    @Override
    @Component(name = "WarningCloseComponent", memo = "预警关闭")
    @Params({
    @ParamItem(type = "java.lang.String", name = "user_code", comment = "要删除的预警用户编码"),
    @ParamItem(type = "java.lang.String", name = "warning_type", comment = "预警类型"),
    @ParamItem(type = "java.util.Map", name = "closed_desc", comment = "关闭原因"),
    @ParamItem(type = "java.util.Map", name = "colsed_by", comment = "关闭人编码")
    })
    @Returns(retValue = {@ReturnItem(type = "java.lang.Integer", name = "result", comment = "删除成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        // 获取参数
        String userCode = (String) param.get("user_code");
        String warningType = (String) param.get("warning_type");
        String closedDesc = (String) param.get("closed_desc");
        String closedBy = (String) param.get("colsed_by");

        if (userCode == null || warningType == null) {
            throw new RuntimeException("用户编码和预警类型不能为空");
        }
        // 准备删除数据的参数
        HashMap <String, Object> deleteParams = new HashMap<>();
        deleteParams.put("dataId", "AssetStorage.warningClose");

        HashMap<String, Object> delMap = new HashMap<>();
        delMap.put("update_time", new Date());
        delMap.put("end_time", new Date());
        delMap.put("closed_by", closedBy);
        delMap.put("closed_desc", closedDesc);
        delMap.put("warning_type", warningType);
        delMap.put("user_code", userCode);

        deleteParams.put("data", delMap);
        // 执行删除操作
        Integer result = (Integer) saveData.run(deleteParams);
        Map<String, Object> response = new HashMap<>();
        if (result == null || result <= 0) {
            response.put("code", 0);
            response.put("message", "预警关闭失败，可能是用户编码或预警类型不正确");
            log.error("预警关闭失败，用户编码: {}, 预警类型: {}, 影响记录数: {}", userCode, warningType, result);
        }else {
            // 返回删除结果
            response.put("result", result);
            response.put("message", "预警关闭成功");
            response.put("code", 2);
            log.info("预警关闭成功，用户编码: {}, 预警类型: {}, 影响记录数: {}", userCode, warningType, result);

        }
        return response;
    }















}
