package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("GetTransferComponent")
@BixComponentPackage(dirname = "部门调动", type = "SYSTEM")
@Slf4j
public class GetTransferComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    QueryDataForObjectComponent queryDataForObjComp;

    @Override
    @Component(name = "GetTransferComponent", memo = "部门调动")
    @Params({@ParamItem(type = "java.util.Map", name = "data", comment = "数据源参数")})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {

        int page = Integer.parseInt(param.getOrDefault("page", "1").toString());
        int pageSize = Integer.parseInt(param.getOrDefault("pageSize", "10").toString());
        int offset = (page - 1) * pageSize; // 手动计算偏移量

        Map<String, Object> pageParamsMap = new HashMap<>();
        pageParamsMap.put("page", page);
        pageParamsMap.put("pageSize", pageSize);
        pageParamsMap.put("offset", offset);

        String department = (String) param.get("department_name");
        Map<String, Object> resMap = new HashMap<>();
        Long count = 0L;

        if ("全校".equals(department)) {
            pageParamsMap.put("dataId", "AssetStorage.getTransfer");
            resMap.put("data", queryForList.run(pageParamsMap));


            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "AssetStorage.countTransfer");
            count = (Long) queryDataForObjComp.run(countParams);
        } else {
            pageParamsMap.put("dataId", "AssetStorage.getTransferByDept");
            pageParamsMap.put("department_name", department);
            resMap.put("data", queryForList.run(pageParamsMap));


            Map<String, Object> countParams = new HashMap<>();
            countParams.put("dataId", "AssetStorage.countTransferByDept");
            countParams.put("department_name", department);
            count = (Long) queryDataForObjComp.run(countParams);
        }

        resMap.put("count", count);
        return resMap;
    }

//    public Object run(Map param) {
//        String department = (String)param.get("department_name");
//        HashMap<String,Object> tempDataCountMap = new HashMap<>();
//
//
//        tempDataCountMap.put("dataId","AssetStorage.getTransferByNewDept");
//        if (!"全校".equals(department)) {
//            tempDataCountMap.put("department_name", department);
//        }
//
//
//        return queryForList.run(tempDataCountMap);
//    }
}
