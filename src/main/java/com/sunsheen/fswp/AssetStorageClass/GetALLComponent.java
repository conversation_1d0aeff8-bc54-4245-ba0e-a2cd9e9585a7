package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.CustomDataPrivilege;
import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("GetALLComponent")
@BixComponentPackage(dirname = "人员资产总计异常预警", type = "SYSTEM")
@Slf4j
public class GetALLComponent extends ABaseComponent{
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    QueryDataForObjectComponent queryDataForObjComp;

    @Override
    @Component(
            name = "GetALLComponent",
            memo = "人员资产异总计常预警"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @LogArgs
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @CustomDataPrivilege
    public Object run(Map param) {
        String department = (String)param.get("user_department_code");
        HashMap<String,Object> tempDataCountMap = new HashMap<>(DBUtil.getLimitPageParams(param));

        tempDataCountMap.put("department",department);
        tempDataCountMap.put("userDept",param.get("userDept"));
        tempDataCountMap.put("lookDept",param.get("lookDept"));
//        tempDataCountMap.put("dataId","AssetStorage.getALL");
        tempDataCountMap.put("dataId","AssetStorage.getALLPri");
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("code",1);
        resMap.put("data",queryForList.run(tempDataCountMap));

        tempDataCountMap.put("dataId","AssetStorage.countALLPri");
        resMap.put("count",queryDataForObjComp.run(tempDataCountMap));
        return resMap;
    }
}
