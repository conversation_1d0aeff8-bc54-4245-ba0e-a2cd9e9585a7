package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("GetUserByCodeComponent")
@BixComponentPackage(dirname = "人员资产详情异常预警", type = "SYSTEM")
@Slf4j
public class GetUserByCodeComponent extends ABaseComponent{
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Override
    @Component(
            name = "GetUserByCodeComponent",
            memo = "人员资产详情异常预警"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {
        String userCode = (String)param.get("userCode");
        Integer current = (Integer)param.get("current");
        Integer pageSize = (Integer)param.get("pageSize");
        HashMap<String,Object> tempDataCountMap = new HashMap<>();
        tempDataCountMap.put("userCode",userCode);
        tempDataCountMap.put("dataId","AssetStorage.getTotal");
        Map total = (Map)queryForMap.run(tempDataCountMap);
        tempDataCountMap.put("current",(current -1)*pageSize);
        tempDataCountMap.put("pageSize",pageSize);
        tempDataCountMap.put("dataId","AssetStorage.getUserbyCode");
        Object data = queryForList.run(tempDataCountMap);
        Map resultMap = new HashMap<>();
        resultMap.put("data",data);
        resultMap.put("total",total.get("count(*)"));
        resultMap.put("current",current);
        resultMap.put("pageSize",pageSize);
        System.out.println(tempDataCountMap);
        return resultMap;
    }
}
