package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.DBUtil;
import com.sunsheen.hearken.dev.service.datasource.QueryDataForObjectComponent;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("GetMoveComponent")
@BixComponentPackage(dirname = "人员离职的异常", type = "SYSTEM")
@Slf4j
public class GetMoveComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    QueryDataForObjectComponent queryDataForObjComp;

    @Override
    @Component(
            name = "GetMoveComponent",
            memo = "人员离职的异常"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {

        HashMap<String, Object> tempDataCountMap = new HashMap<>(param);
        String department = (String) param.get("department_name");
        Map<String,Object> resMap = new HashMap<>();
        Long count = 0L;

        if ("全校".equals(department)) {
            tempDataCountMap.put("dataId","AssetStorage.getMove");
            resMap.put("data",queryForList.run(tempDataCountMap));
            tempDataCountMap.put("dataId","AssetStorage.countMove");
            count = (Long)queryDataForObjComp.run(tempDataCountMap);
        }else {
            tempDataCountMap.put("dataId","AssetStorage.getMoveByDept");
            tempDataCountMap.put("department_name",department);
            resMap.put("data",queryForList.run(tempDataCountMap));
            tempDataCountMap.put("dataId","AssetStorage.countMoveByDept");
            count = (Long)queryDataForObjComp.run(tempDataCountMap);
        }
        resMap.put("count",count);
        return resMap;
    }
}
