package com.sunsheen.fswp.AssetStorageClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("GetDetailedTransferComponent")
@BixComponentPackage(dirname = "部门调动的异常详情", type = "SYSTEM")
@Slf4j
public class GetDetailedTransferComponent extends ABaseComponent {
    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;



    @Override
    @Component(
            name = "GetDetailedTransferComponent",
            memo = "部门调动的异常详情"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.List", name = "data", comment = "返回查询的列表数据")})
    @LogArgs
    public Object run(Map param) {
        Integer current = (Integer)param.get("current");
        Integer pageSize = (Integer)param.get("pageSize");
        String userCode = (String)param.get("user_code");
        String department = (String)param.get("user_department_name");
        HashMap<String,Object> tempDataCountMap = new HashMap<>();
        tempDataCountMap.put("department",department);
        tempDataCountMap.put("userCode",userCode);
        tempDataCountMap.put("dataId","AssetStorage.getTransferTotal");
        Map total = (Map)queryForMap.run(tempDataCountMap);
        System.out.println(total);
        tempDataCountMap.put("dataId","AssetStorage.getDetailedTransfer");
        tempDataCountMap.put("current",(current -1)*pageSize);
        tempDataCountMap.put("pageSize",pageSize);
        Map resultMap = new HashMap<>();
        resultMap.put("data",queryForList.run(tempDataCountMap));
        resultMap.put("total",total.get("count(*)"));
        resultMap.put("current",current);
        resultMap.put("pageSize",pageSize);
        return resultMap;
    }
}
