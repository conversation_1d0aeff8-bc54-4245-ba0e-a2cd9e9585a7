package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资产预警阈值大屏展示组件
 * 直接展示所有达到预警阈值的资产信息
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Controller("AssetWarningThresholdDashboardComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class AssetWarningThresholdDashboardComponent extends ABaseComponent {

    private static final Logger logger = LoggerFactory.getLogger(AssetWarningThresholdDashboardComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "AssetWarningThresholdDashboardComponent", memo = "资产预警阈值大屏展示")
    @Params({
            @ParamItem(type = "java.lang.Integer", name = "statisticsYear", comment = "统计年份（可选，默认当年）"),
            @ParamItem(type = "java.lang.String", name = "userDepartmentName", comment = "部门名称筛选（可选）"),
            @ParamItem(type = "java.lang.Integer", name = "limit", comment = "返回记录数限制（可选，默认100）")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "data", comment = "返回所有预警资产信息")})
    public Object run(Map param) {
        try {
            logger.info("开始查询所有达到预警阈值的资产信息，参数: {}", param);

            // 准备查询参数 - 查询所有使用率超过阈值的资产
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getAssetsExceedUsageThreshold");
            
            // 设置默认查询条件：查询所有预警级别的资产
            // 不设置具体的indicatorId，让SQL使用默认的'asset_usage_rate'
            
            // 传递用户参数
            if (param.get("statisticsYear") != null) {
                queryParams.put("statisticsYear", param.get("statisticsYear"));
            }
            if (param.get("userDepartmentName") != null) {
                queryParams.put("userDepartmentName", param.get("userDepartmentName"));
            }

            // 设置返回记录数限制
            Integer limit = (Integer) param.get("limit");
            if (limit == null || limit <= 0) {
                limit = 100; // 默认返回100条记录
            }
            queryParams.put("dm_limit", limit);
            queryParams.put("dm_offset", 0);

            // 执行查询
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            if (resultList == null || resultList.isEmpty()) {
                logger.info("未找到达到预警阈值的资产");
                return createEmptyResult("未找到达到预警阈值的资产");
            }

            // 转换字段名为驼峰命名并处理数据
            for (Map<String, Object> asset : resultList) {
                convertToCamelCase(asset);
                // 添加预警等级描述
                addWarningLevelDescription(asset);
            }

            logger.info("成功查询到{}条达到预警阈值的资产记录", resultList.size());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("totalCount", resultList.size());
            result.put("warningAssets", resultList);
            result.put("message", "查询成功");
            result.put("queryTime", new java.util.Date());

            return result;

        } catch (Exception e) {
            logger.error("查询达到预警阈值的资产失败: {}", e.getMessage(), e);
            return createErrorResult("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建空结果
     */
    private Map<String, Object> createEmptyResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("totalCount", 0);
        result.put("warningAssets", new java.util.ArrayList<>());
        result.put("message", message);
        result.put("queryTime", new java.util.Date());
        return result;
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("totalCount", 0);
        result.put("warningAssets", new java.util.ArrayList<>());
        result.put("message", message);
        result.put("queryTime", new java.util.Date());
        return result;
    }

    /**
     * 添加预警等级描述
     */
    private void addWarningLevelDescription(Map<String, Object> asset) {
        Object thresholdWarningLevel = asset.get("thresholdWarningLevel");
        if (thresholdWarningLevel != null) {
            String levelDesc = "";
            String levelColor = "";
            
            if ("1".equals(thresholdWarningLevel.toString())) {
                levelDesc = "红色预警";
                levelColor = "red";  // 预警级别1：阈值40%，红色
            } else if ("2".equals(thresholdWarningLevel.toString())) {
                levelDesc = "黄色预警";
                levelColor = "yellow";  // 预警级别2：阈值60%，黄色
            } else {
                levelDesc = "正常";
                levelColor = "green";
            }
            
            asset.put("warningLevelDescription", levelDesc);
            asset.put("warningLevelColor", levelColor);
        }

        // 计算低于阈值的百分比
        Object usageRate = asset.get("usageRate");
        Object configuredThreshold = asset.get("configuredThreshold");
        if (usageRate != null && configuredThreshold != null) {
            try {
                double usage = Double.parseDouble(usageRate.toString());
                double threshold = Double.parseDouble(configuredThreshold.toString());
                double belowPercentage = ((threshold - usage) / threshold) * 100;
                asset.put("belowPercentage", Math.round(belowPercentage * 100.0) / 100.0);
                asset.put("shortfallAmount", Math.round((threshold - usage) * 100.0) / 100.0);
            } catch (NumberFormatException e) {
                asset.put("belowPercentage", 0.0);
                asset.put("shortfallAmount", 0.0);
            }
        }
    }

    /**
     * 将数据库字段名转换为驼峰命名
     */
    private void convertToCamelCase(Map<String, Object> data) {
        // 基础资产信息
        if (data.containsKey("asset_code")) data.put("assetCode", data.remove("asset_code"));
        if (data.containsKey("asset_name")) data.put("assetName", data.remove("asset_name"));
        if (data.containsKey("asset_category_name")) data.put("assetCategoryName", data.remove("asset_category_name"));
        if (data.containsKey("user_department_name")) data.put("userDepartmentName", data.remove("user_department_name"));
        if (data.containsKey("user_name")) data.put("userName", data.remove("user_name"));
        if (data.containsKey("equipment_value")) data.put("equipmentValue", data.remove("equipment_value"));
        if (data.containsKey("asset_entry_date")) data.put("assetEntryDate", data.remove("asset_entry_date"));
        if (data.containsKey("status_name")) data.put("statusName", data.remove("status_name"));
        if (data.containsKey("storage_location")) data.put("storageLocation", data.remove("storage_location"));
        if (data.containsKey("model_brand")) data.put("modelBrand", data.remove("model_brand"));
        
        // 使用率相关信息
        if (data.containsKey("statistics_year")) data.put("statisticsYear", data.remove("statistics_year"));
        if (data.containsKey("annual_rated_hours")) data.put("annualRatedHours", data.remove("annual_rated_hours"));
        if (data.containsKey("annual_usage_hours")) data.put("annualUsageHours", data.remove("annual_usage_hours"));
        if (data.containsKey("usage_rate")) data.put("usageRate", data.remove("usage_rate"));
        if (data.containsKey("effective_usage_rate")) data.put("effectiveUsageRate", data.remove("effective_usage_rate"));
        if (data.containsKey("shared_rate")) data.put("sharedRate", data.remove("shared_rate"));
        if (data.containsKey("usage_level")) data.put("usageLevel", data.remove("usage_level"));
        if (data.containsKey("warning_status")) data.put("warningStatus", data.remove("warning_status"));
        if (data.containsKey("warning_level")) data.put("warningLevel", data.remove("warning_level"));
        
        // 阈值相关信息
        if (data.containsKey("configured_threshold")) data.put("configuredThreshold", data.remove("configured_threshold"));
        if (data.containsKey("threshold_warning_level")) data.put("thresholdWarningLevel", data.remove("threshold_warning_level"));
        if (data.containsKey("threshold_description")) data.put("thresholdDescription", data.remove("threshold_description"));
        if (data.containsKey("shortfall_amount")) data.put("shortfallAmount", data.remove("shortfall_amount"));

        // 指标ID信息
        if (data.containsKey("indicator_id")) data.put("indicatorId", data.remove("indicator_id"));
    }
}
