package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("LargeEquipmentUsageRateComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class LargeEquipmentUsageRateComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(LargeEquipmentUsageRateComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "LargeEquipmentUsageRateComponent", memo = "查询大型设备列表及使用率信息")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "assetName", comment = "资产名称，可选参数，支持模糊查询", type = "java.lang.String"),
        @ParamItem(name = "usageLevel", comment = "使用水平，可选参数", type = "java.lang.String"),
        @ParamItem(name = "warningStatus", comment = "预警状态，可选参数", type = "java.lang.String"),
        @ParamItem(name = "statisticsYear", comment = "统计年份，可选参数，默认当年", type = "java.lang.String"),
        @ParamItem(name = "start", comment = "分页起始位置", type = "java.lang.Integer"),
        @ParamItem(name = "limit", comment = "分页大小", type = "java.lang.Integer")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "data", comment = "返回大型设备列表及使用率信息")
    })
    public Object run(Map param) {
        logger.info("开始查询大型设备列表及使用率信息...");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getLargeEquipmentWithUsageRate");

            // 传递查询参数
            if (param.get("userDepartmentName") != null) {
                queryParams.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.get("assetName") != null) {
                queryParams.put("assetName", param.get("assetName"));
            }
            if (param.get("usageLevel") != null) {
                queryParams.put("usageLevel", param.get("usageLevel"));
            }
            if (param.get("warningStatus") != null) {
                queryParams.put("warningStatus", param.get("warningStatus"));
            }
            if (param.get("statisticsYear") != null) {
                queryParams.put("statisticsYear", param.get("statisticsYear"));
            }

            // 分页参数
            Integer start = (Integer) param.getOrDefault("start", 0);
            Integer limit = (Integer) param.getOrDefault("limit", 20);
            queryParams.put("start", start);
            queryParams.put("limit", limit);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> result = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            logger.info("查询大型设备列表及使用率信息成功，返回 {} 条记录", result != null ? result.size() : 0);
            return result;

        } catch (Exception e) {
            logger.error("查询大型设备列表及使用率信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询大型设备列表及使用率信息失败: " + e.getMessage());
        }
    }
}