package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("LargeEquipmentUsageStatisticsComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class LargeEquipmentUsageStatisticsComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(LargeEquipmentUsageStatisticsComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "LargeEquipmentUsageStatisticsComponent", memo = "大型设备使用率统计概览")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "statisticsYear", comment = "统计年份，可选参数，默认当年", type = "java.lang.String")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回大型设备使用率统计概览")
    })
    public Object run(Map param) {
        logger.info("开始获取大型设备使用率统计概览...");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getLargeEquipmentUsageStatistics");

            // 传递查询参数
            if (param.get("userDepartmentName") != null) {
                queryParams.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.get("statisticsYear") != null) {
                queryParams.put("statisticsYear", param.get("statisticsYear"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) queryDataForMap.run(queryParams);

            if (result == null) {
                logger.warn("获取大型设备使用率统计概览查询结果为空，返回默认值");
                result = new HashMap<>();
                result.put("total_equipment_count", 0);
                result.put("has_usage_data_count", 0);
                result.put("avg_usage_rate", 0.0);
                result.put("avg_effective_usage_rate", 0.0);
                result.put("avg_shared_rate", 0.0);
                result.put("high_efficiency_count", 0);
                result.put("good_efficiency_count", 0);
                result.put("normal_efficiency_count", 0);
                result.put("low_efficiency_count", 0);
                result.put("warning_count", 0);
                result.put("total_equipment_value", 0.0);
                result.put("total_fee_income", 0.0);
                result.put("total_maintenance_cost", 0.0);
            }

            // 转换为驼峰命名
            Map<String, Object> finalResult = new HashMap<>();
            finalResult.put("totalEquipmentCount", result.getOrDefault("total_equipment_count", 0));
            finalResult.put("hasUsageDataCount", result.getOrDefault("has_usage_data_count", 0));
            finalResult.put("avgUsageRate", result.getOrDefault("avg_usage_rate", 0.0));
            finalResult.put("avgEffectiveUsageRate", result.getOrDefault("avg_effective_usage_rate", 0.0));
            finalResult.put("avgSharedRate", result.getOrDefault("avg_shared_rate", 0.0));
            finalResult.put("highEfficiencyCount", result.getOrDefault("high_efficiency_count", 0));
            finalResult.put("goodEfficiencyCount", result.getOrDefault("good_efficiency_count", 0));
            finalResult.put("normalEfficiencyCount", result.getOrDefault("normal_efficiency_count", 0));
            finalResult.put("lowEfficiencyCount", result.getOrDefault("low_efficiency_count", 0));
            finalResult.put("warningCount", result.getOrDefault("warning_count", 0));
            finalResult.put("totalEquipmentValue", result.getOrDefault("total_equipment_value", 0.0));
            finalResult.put("totalFeeIncome", result.getOrDefault("total_fee_income", 0.0));
            finalResult.put("totalMaintenanceCost", result.getOrDefault("total_maintenance_cost", 0.0));

            logger.info("获取大型设备使用率统计概览成功");
            return finalResult;

        } catch (Exception e) {
            logger.error("获取大型设备使用率统计概览失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取大型设备使用率统计概览失败: " + e.getMessage());
        }
    }
}