package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("LargeEquipmentUsageByDepartmentComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class LargeEquipmentUsageByDepartmentComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(LargeEquipmentUsageByDepartmentComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "LargeEquipmentUsageByDepartmentComponent", memo = "按部门统计大型设备使用率")
    @Params({
        @ParamItem(name = "statisticsYear", comment = "统计年份，可选参数，默认当年", type = "java.lang.String")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "data", comment = "返回按部门统计的大型设备使用率")
    })
    public Object run(Map param) {
        logger.info("开始按部门统计大型设备使用率...");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getLargeEquipmentUsageByDepartment");

            // 传递查询参数
            if (param.get("statisticsYear") != null) {
                queryParams.put("statisticsYear", param.get("statisticsYear"));
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> result = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            logger.info("按部门统计大型设备使用率成功，返回 {} 个部门的统计数据", result != null ? result.size() : 0);
            return result;

        } catch (Exception e) {
            logger.error("按部门统计大型设备使用率失败: {}", e.getMessage(), e);
            throw new RuntimeException("按部门统计大型设备使用率失败: " + e.getMessage());
        }
    }
}