package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询一级监管指标配置组件
 * 根据indicatorId查询supervisor_level=1的指标配置信息
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Controller("IndicatorLevelSupervisorLevel1Component")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class IndicatorLevelSupervisorLevel1Component extends ABaseComponent {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorLevelSupervisorLevel1Component.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "IndicatorLevelSupervisorLevel1Component", memo = "查询一级监管指标配置信息")
    @Params({
            @ParamItem(type = "java.lang.String", name = "indicatorId", comment = "指标ID（可选）"),
            @ParamItem(type = "java.lang.String", name = "indicatorName", comment = "指标名称（可选）"),
            @ParamItem(type = "java.lang.String", name = "warningLevel", comment = "预警级别（可选）")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "data", comment = "返回一级监管指标配置信息")})
    public Object run(Map param) {
        try {
            logger.info("开始查询一级监管指标配置信息，参数: {}", param);

            // 准备查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getIndicatorLevelBySupervisorLevel1");
            
            // 传递参数
            if (param.get("indicatorId") != null) {
                queryParams.put("indicatorId", param.get("indicatorId"));
            }
            if (param.get("indicatorName") != null) {
                queryParams.put("indicatorName", param.get("indicatorName"));
            }
            if (param.get("warningLevel") != null) {
                queryParams.put("warningLevel", param.get("warningLevel"));
            }

            // 执行查询
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            if (resultList == null || resultList.isEmpty()) {
                logger.info("未找到一级监管指标配置信息");
                return createEmptyResult("未找到一级监管指标配置信息");
            }

            // 转换字段名为驼峰命名
            for (Map<String, Object> indicator : resultList) {
                convertToCamelCase(indicator);
            }

            logger.info("成功查询到{}条一级监管指标配置记录", resultList.size());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("totalCount", resultList.size());
            result.put("indicatorLevels", resultList);
            result.put("message", "查询成功");
            result.put("queryTime", new java.util.Date());

            return result;

        } catch (Exception e) {
            logger.error("查询一级监管指标配置信息失败: {}", e.getMessage(), e);
            return createErrorResult("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建空结果
     */
    private Map<String, Object> createEmptyResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("totalCount", 0);
        result.put("indicatorLevels", new java.util.ArrayList<>());
        result.put("message", message);
        result.put("queryTime", new java.util.Date());
        return result;
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("totalCount", 0);
        result.put("indicatorLevels", new java.util.ArrayList<>());
        result.put("message", message);
        result.put("queryTime", new java.util.Date());
        return result;
    }

    /**
     * 将数据库字段名转换为驼峰命名
     */
    private void convertToCamelCase(Map<String, Object> data) {
        // 基础字段
        if (data.containsKey("indicator_id")) data.put("indicatorId", data.remove("indicator_id"));
        if (data.containsKey("indicator_name")) data.put("indicatorName", data.remove("indicator_name"));
        if (data.containsKey("warning_level")) data.put("warningLevel", data.remove("warning_level"));
        if (data.containsKey("rule_description")) data.put("ruleDescription", data.remove("rule_description"));
        if (data.containsKey("supervisor_level")) data.put("supervisorLevel", data.remove("supervisor_level"));
        if (data.containsKey("update_time")) data.put("updateTime", data.remove("update_time"));
        
        // 知识库相关字段
        if (data.containsKey("monitor_obj")) data.put("monitorObj", data.remove("monitor_obj"));
        if (data.containsKey("risk_description")) data.put("riskDescription", data.remove("risk_description"));
        if (data.containsKey("prevension_measure")) data.put("prevensionMeasure", data.remove("prevension_measure"));
        if (data.containsKey("refer_regu_describe")) data.put("referReguDescribe", data.remove("refer_regu_describe"));
        if (data.containsKey("sub_process")) data.put("subProcess", data.remove("sub_process"));
    }
}
