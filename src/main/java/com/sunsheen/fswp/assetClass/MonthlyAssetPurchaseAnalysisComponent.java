package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Calendar;

@Service("MonthlyAssetPurchaseAnalysisComponent")
public class MonthlyAssetPurchaseAnalysisComponent extends ABaseComponent {

    private static final Logger logger = LoggerFactory.getLogger(MonthlyAssetPurchaseAnalysisComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryDataForListComponent;

    @Override
    @Component(name = "MonthlyAssetPurchaseAnalysisComponent", memo = "年度仪器设备购置分析，按月统计当年新增及累计总额")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "用户部门名称 (可选)", type = "java.lang.String")
    })
    @Returns(retValue = {
        @ReturnItem(type = "java.util.List<java.util.Map<java.lang.String, java.lang.Object>>", name = "monthlyAnalysis", comment = "包含各月新增和累计资产额的列表")
    })
    public Object run(Map inParams) {
        Map<String, Object> queryParams = new HashMap<>();
        String userDepartmentName = null;
        if (inParams.containsKey("userDepartmentName")) {
            userDepartmentName = (String) inParams.get("userDepartmentName");
        }

        if (userDepartmentName != null && !userDepartmentName.isEmpty()) {
            queryParams.put("userDepartmentName", userDepartmentName);
        }
        queryParams.put("dataId", "asset.getMonthlyAssetPurchasesCurrentYear");

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> queryResult = (List<Map<String, Object>>) queryDataForListComponent.run(queryParams);

        long[] monthlyNewAssets = new long[12];

        for (Map<String, Object> row : queryResult) {
            Object monthObj = row.get("registration_month");
            Object valueObj = row.get("monthly_new_assets_value");

            if (monthObj != null && valueObj != null) {
                int month = 0;
                 if (monthObj instanceof Number) {
                    month = ((Number) monthObj).intValue();
                } else {
                    try {
                        month = Integer.parseInt(monthObj.toString());
                    } catch (NumberFormatException e) {
                        logger.error("无法解析月份: {}，已跳过此条记录。", monthObj, e);
                        continue; 
                    }
                }

                long value = 0;
                if (valueObj instanceof BigDecimal) {
                    value = ((BigDecimal) valueObj).longValue();
                } else if (valueObj instanceof Number) {
                    value = ((Number) valueObj).longValue();
                } else {
                     try {
                        value = new BigDecimal(valueObj.toString()).longValue();
                    } catch (NumberFormatException e) {
                         logger.error("无法解析月份 {} 的值: {}，已跳过此条记录。", month, valueObj, e);
                        continue;
                    }
                }
                
                if (month >= 1 && month <= 12) {
                    monthlyNewAssets[month - 1] = value;
                }
            }
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        long cumulativeTotal = 0;
        int currentActualMonth = Calendar.getInstance().get(Calendar.MONTH) + 1;

        for (int i = 0; i < 12; i++) {
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", (i + 1) + "月");
            
            long newForThisMonth = monthlyNewAssets[i];
            monthData.put("newAssets", newForThisMonth);
            
            if ((i + 1) <= currentActualMonth) {
                 cumulativeTotal += newForThisMonth;
                 monthData.put("totalAssets", cumulativeTotal);
            } else {
                 monthData.put("totalAssets", cumulativeTotal); 
            }
            resultList.add(monthData);
        }
        
        return resultList;
    }

}