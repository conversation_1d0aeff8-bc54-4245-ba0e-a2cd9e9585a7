package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("AssetUsedYearsAnalysisComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class AssetUsedYearsAnalysisComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(AssetUsedYearsAnalysisComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent") // 使用查询列表的构件
    private IDataPort queryDataForList;

    @Override
    @Component(name = "AssetUsedYearsAnalysisComponent", memo = "按已使用年限分析资产")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List", name = "data", comment = "返回各年限分段的资产数量和百分比")
    })
    public Object run(Map param) {
        String userDepartmentName = (String) param.get("userDepartmentName");
        logger.info("开始按已使用年限分析资产...userDepartmentName: {}", userDepartmentName);

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.analyzeUsedYears");

            // 添加部门名称参数支持
            if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
                queryParams.put("userDepartmentName", userDepartmentName.trim());
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) queryDataForList.run(queryParams);

            if (resultList == null) {
                logger.warn("按已使用年限分析资产未查询到数据或查询失败");
                return new ArrayList<>(); // 返回空列表
            }

            long totalAssets = 0;
            for (Map<String, Object> item : resultList) {
                Object countObj = item.get("asset_count");
                if (countObj instanceof Number) {
                    totalAssets += ((Number) countObj).longValue();
                }
            }

            List<Map<String, Object>> finalResult = new ArrayList<>();
            for (Map<String, Object> item : resultList) {
                Map<String, Object> newItem = new HashMap<>(item);
                Object countObj = item.get("asset_count");
                long currentCount = 0;
                if (countObj instanceof Number) {
                    currentCount = ((Number) countObj).longValue();
                }

                newItem.put("count", currentCount); // 使用更通用的 "count" 键
                newItem.put("name", item.get("year_range")); // 使用 "name" 作为图表标签的键

                if (totalAssets > 0) {
                    double percentage = (double) currentCount / totalAssets * 100;
                    // 保留两位小数
                    BigDecimal bd = new BigDecimal(percentage).setScale(2, RoundingMode.HALF_UP);
                    newItem.put("percentage", bd.doubleValue());
                } else {
                    newItem.put("percentage", 0.0);
                }
                finalResult.add(newItem);
            }

            logger.info("按已使用年限分析资产成功，处理了 {} 个分段", finalResult.size());
            return finalResult;

        } catch (Exception e) {
            logger.error("按已使用年限分析资产失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "按已使用年限分析资产失败: " + e.getMessage());
            // 根据实际框架要求，这里可能需要返回一个空的List或特定的错误对象
            return new ArrayList<>(List.of(errorResult));
        }
    }
}