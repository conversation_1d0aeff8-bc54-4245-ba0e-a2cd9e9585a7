package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("AssetTotalSummaryComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class AssetTotalSummaryComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(AssetTotalSummaryComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent") // 使用查询单条记录的构件
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "AssetTotalSummaryComponent", memo = "获取资产总额及当年新增")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回资产总额和当年新增额")
    })
    public Object run(Map param) {
        String userDepartmentName = (String) param.get("userDepartmentName");
        logger.info("开始获取资产总额及当年新增...userDepartmentName: {}", userDepartmentName);

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.getAssetTotalSummary");

            // 添加部门名称参数支持
            if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
                queryParams.put("userDepartmentName", userDepartmentName.trim());
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) queryDataForMap.run(queryParams);

            if (result == null) {
                logger.warn("获取资产总额及当年新增查询结果为空，返回默认值");
                result = new HashMap<>();
                result.put("total_asset_amount", 0.0);
                result.put("current_year_new_amount", 0.0);
            }

            // 将key从下划线转为驼峰，或者前端直接使用下划线的key
            Map<String, Object> finalResult = new HashMap<>();
            finalResult.put("totalAssetAmount", result.getOrDefault("total_asset_amount", 0.0));
            finalResult.put("currentYearNewAmount", result.getOrDefault("current_year_new_amount", 0.0));

            logger.info("获取资产总额及当年新增成功.");
            return finalResult;

        } catch (Exception e) {
            logger.error("获取资产总额及当年新增失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取资产总额及当年新增失败: " + e.getMessage());
            errorResult.put("totalAssetAmount", 0.0);
            errorResult.put("currentYearNewAmount", 0.0);
            return errorResult;
        }
    }
}
