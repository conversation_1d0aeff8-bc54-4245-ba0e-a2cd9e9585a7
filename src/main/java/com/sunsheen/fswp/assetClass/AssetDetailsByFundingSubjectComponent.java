package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("AssetDetailsByFundingSubjectComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class AssetDetailsByFundingSubjectComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(AssetDetailsByFundingSubjectComponent.class);
    private static final int DEFAULT_PAGE_NUMBER = 1;
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 1000;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForListComponent;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMapComponent;

    @Override
    @Component(name = "AssetDetailsByFundingSubjectComponent", memo = "根据资金主体查询资产详情，完整分页支持")
    @Params({
        @ParamItem(name = "fundingSubjectName", comment = "资金主体名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "assetCategoryName", comment = "资产类别名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "page", comment = "页码，从1开始，默认1", type = "java.lang.Integer"),
        @ParamItem(name = "pageSize", comment = "每页记录数，默认20，最大1000", type = "java.lang.Integer")
    })
    @Returns(retValue = {
        @ReturnItem(type = "java.util.Map", name = "result", comment = "包含data、total、currentPage、pageSize、totalPages的分页结果")
    })
    public Object run(Map param) {
        String fundingSubjectName = (String) param.get("fundingSubjectName");
        String userDepartmentName = (String) param.get("userDepartmentName");
        String assetCategoryName = (String) param.get("assetCategoryName");
        Integer page = parseInteger(param.get("page"));
        Integer pageSize = parseInteger(param.get("pageSize"));

        logger.info("查询资金主体资产详情 - 资金主体: {}, 部门: {}, 资产类别: {}, 页码: {}, 页大小: {}", 
                   fundingSubjectName, userDepartmentName, assetCategoryName, page, pageSize);

        try {
            return getAssetDetailsByFundingSubject(fundingSubjectName, userDepartmentName, 
                                                 assetCategoryName, page, pageSize);
        } catch (Exception e) {
            logger.error("查询资金主体资产详情失败: {}", e.getMessage(), e);
            return createErrorResult("查询资金主体资产详情失败: " + e.getMessage());
        }
    }

    private Map<String, Object> getAssetDetailsByFundingSubject(String fundingSubjectName, 
                                                               String userDepartmentName,
                                                               String assetCategoryName,
                                                               Integer page, Integer pageSize) {
        // 参数验证和默认值处理
        int currentPage = validateAndGetPage(page);
        int currentSize = validateAndGetPageSize(pageSize);
        int offset = (currentPage - 1) * currentSize;

        logger.info("分页参数 - 页码: {}, 页大小: {}, 偏移量: {}", currentPage, currentSize, offset);

        // 构建查询参数
        Map<String, Object> queryParams = buildQueryParams(fundingSubjectName, userDepartmentName, assetCategoryName);

        // 查询总记录数
        long totalRecords = getTotalRecords(queryParams);
        logger.info("总记录数: {}", totalRecords);

        // 如果没有数据，直接返回空结果
        if (totalRecords == 0) {
            return buildEmptyResult(currentPage, currentSize);
        }

        // 查询分页数据
        List<Map<String, Object>> assetList = getPagedData(queryParams, currentSize, offset);
        logger.info("查询到 {} 条记录", assetList != null ? assetList.size() : 0);

        // 计算总页数
        long totalPages = (totalRecords + currentSize - 1) / currentSize;

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("data", assetList != null ? assetList : Collections.emptyList());
        result.put("total", totalRecords);
        result.put("currentPage", currentPage);
        result.put("pageSize", currentSize);
        result.put("totalPages", totalPages);
        result.put("hasNextPage", currentPage < totalPages);
        result.put("hasPreviousPage", currentPage > 1);

        logger.info("分页查询完成 - 总记录: {}, 总页数: {}, 当前页: {}", totalRecords, totalPages, currentPage);
        return result;
    }

    private int validateAndGetPage(Integer page) {
        if (page == null || page < 1) {
            logger.debug("页码无效或为空，使用默认值: {}", DEFAULT_PAGE_NUMBER);
            return DEFAULT_PAGE_NUMBER;
        }
        return page;
    }

    private int validateAndGetPageSize(Integer pageSize) {
        if (pageSize == null || pageSize < 1) {
            logger.debug("页大小无效或为空，使用默认值: {}", DEFAULT_PAGE_SIZE);
            return DEFAULT_PAGE_SIZE;
        }
        if (pageSize > MAX_PAGE_SIZE) {
            logger.warn("页大小超过最大限制 {}, 使用最大值", MAX_PAGE_SIZE);
            return MAX_PAGE_SIZE;
        }
        return pageSize;
    }

    private Map<String, Object> buildQueryParams(String fundingSubjectName, String userDepartmentName, String assetCategoryName) {
        Map<String, Object> params = new HashMap<>();
        
        if (fundingSubjectName != null && !fundingSubjectName.trim().isEmpty()) {
            params.put("fundingSubjectName", fundingSubjectName.trim());
        }
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            params.put("userDepartmentName", userDepartmentName.trim());
        }
        if (assetCategoryName != null && !assetCategoryName.trim().isEmpty()) {
            params.put("assetCategoryName", assetCategoryName.trim());
        }
        
        return params;
    }

    private long getTotalRecords(Map<String, Object> queryParams) {
        Map<String, Object> countParams = new HashMap<>(queryParams);
        countParams.put("dataId", "asset.countAssetDetailsByFundingSubject");

        logger.debug("查询总记录数，参数: {}", countParams);
        @SuppressWarnings("unchecked")
        Map<String, Object> countResult = (Map<String, Object>) queryDataForMapComponent.run(countParams);

        if (countResult != null) {
            Object totalAssetsObj = countResult.get("total_assets");
            if (totalAssetsObj instanceof Number) {
                return ((Number) totalAssetsObj).longValue();
            } else if (totalAssetsObj != null) {
                try {
                    return Long.parseLong(totalAssetsObj.toString());
                } catch (NumberFormatException e) {
                    logger.error("解析总记录数失败: {}", totalAssetsObj, e);
                }
            }
        }
        return 0;
    }

    private List<Map<String, Object>> getPagedData(Map<String, Object> queryParams, int pageSize, int offset) {
        Map<String, Object> listParams = new HashMap<>(queryParams);
        listParams.put("dataId", "asset.getAssetDetailsByFundingSubject");
        listParams.put("dm_limit", pageSize);
        listParams.put("dm_offset", offset);

        logger.debug("查询分页数据，参数: {}", listParams);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> result = (List<Map<String, Object>>) queryDataForListComponent.run(listParams);
        
        return result;
    }

    private Map<String, Object> buildEmptyResult(int currentPage, int currentSize) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", Collections.emptyList());
        result.put("total", 0L);
        result.put("currentPage", currentPage);
        result.put("pageSize", currentSize);
        result.put("totalPages", 0L);
        result.put("hasNextPage", false);
        result.put("hasPreviousPage", false);
        return result;
    }

    private Integer parseInteger(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private Map<String, Object> createErrorResult(String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put("error", errorMessage);
        result.put("data", Collections.emptyList());
        result.put("total", 0L);
        result.put("currentPage", 1);
        result.put("pageSize", DEFAULT_PAGE_SIZE);
        result.put("totalPages", 0L);
        result.put("hasNextPage", false);
        result.put("hasPreviousPage", false);
        return result;
    }
}
