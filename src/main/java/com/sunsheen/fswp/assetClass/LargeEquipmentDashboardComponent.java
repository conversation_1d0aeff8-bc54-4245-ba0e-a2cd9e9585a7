package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("LargeEquipmentDashboardComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class LargeEquipmentDashboardComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(LargeEquipmentDashboardComponent.class);

    @Autowired
    @Qualifier("LargeEquipmentUsageStatisticsComponent")
    private IDataPort usageStatisticsComponent;

    @Autowired
    @Qualifier("LargeEquipmentUsageByDepartmentComponent")
    private IDataPort usageByDepartmentComponent;

    @Autowired
    @Qualifier("LargeEquipmentUsageRateComponent")
    private IDataPort usageRateComponent;

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForList;

    @Override
    @Component(name = "LargeEquipmentDashboardComponent", memo = "大型设备大屏数据综合查询")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "statisticsYear", comment = "统计年份，可选参数，默认当年", type = "java.lang.String"),
        @ParamItem(name = "topCount", comment = "返回前N条设备记录，默认10", type = "java.lang.Integer")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回大型设备大屏综合数据")
    })
    public Object run(Map param) {
        logger.info("开始获取大型设备大屏综合数据...");

        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 获取使用率统计概览
            Map<String, Object> statisticsParams = new HashMap<>();
            if (param.get("userDepartmentName") != null) {
                statisticsParams.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.get("statisticsYear") != null) {
                statisticsParams.put("statisticsYear", param.get("statisticsYear"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> statistics = (Map<String, Object>) usageStatisticsComponent.run(statisticsParams);
            result.put("statistics", statistics);

            // 2. 获取按部门统计数据
            Map<String, Object> departmentParams = new HashMap<>();
            if (param.get("statisticsYear") != null) {
                departmentParams.put("statisticsYear", param.get("statisticsYear"));
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> departmentStats = (List<Map<String, Object>>) usageByDepartmentComponent.run(departmentParams);
            result.put("departmentStatistics", departmentStats);

            // 3. 获取设备详情列表（前N条）
            Map<String, Object> equipmentParams = new HashMap<>();
            if (param.get("userDepartmentName") != null) {
                equipmentParams.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.get("statisticsYear") != null) {
                equipmentParams.put("statisticsYear", param.get("statisticsYear"));
            }
            
            Integer topCount = (Integer) param.getOrDefault("topCount", 10);
            equipmentParams.put("start", 0);
            equipmentParams.put("limit", topCount);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> equipmentList = (List<Map<String, Object>>) usageRateComponent.run(equipmentParams);
            result.put("topEquipmentList", equipmentList);

            // 4. 获取使用率等级分布数据（用于饼图）
            Map<String, Object> usageLevelParams = new HashMap<>();
            usageLevelParams.put("dataId", "asset.getUsageLevelDistribution");
            if (param.get("statisticsYear") != null) {
                usageLevelParams.put("statisticsYear", param.get("statisticsYear"));
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> usageLevelDistribution = (List<Map<String, Object>>) queryDataForList.run(usageLevelParams);
            result.put("usageLevelDistribution", usageLevelDistribution);

            // 5. 获取预警状态分布数据
            Map<String, Object> warningParams = new HashMap<>();
            warningParams.put("dataId", "asset.getWarningStatusDistribution");
            if (param.get("statisticsYear") != null) {
                warningParams.put("statisticsYear", param.get("statisticsYear"));
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> warningDistribution = (List<Map<String, Object>>) queryDataForList.run(warningParams);
            result.put("warningDistribution", warningDistribution);

            // 6. 获取月度使用率趋势数据
            Map<String, Object> trendParams = new HashMap<>();
            trendParams.put("dataId", "asset.getMonthlyUsageTrend");
            if (param.get("statisticsYear") != null) {
                trendParams.put("statisticsYear", param.get("statisticsYear"));
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> monthlyTrend = (List<Map<String, Object>>) queryDataForList.run(trendParams);
            result.put("monthlyUsageTrend", monthlyTrend);

            // 7. 获取设备价值分布数据
            Map<String, Object> valueParams = new HashMap<>();
            valueParams.put("dataId", "asset.getEquipmentValueDistribution");
            if (param.get("statisticsYear") != null) {
                valueParams.put("statisticsYear", param.get("statisticsYear"));
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> valueDistribution = (List<Map<String, Object>>) queryDataForList.run(valueParams);
            result.put("equipmentValueDistribution", valueDistribution);

            // 8. 计算一些额外的统计指标
            if (statistics != null) {
                Map<String, Object> additionalStats = new HashMap<>();
                
                // 计算使用率分布百分比
                Integer totalCount = (Integer) statistics.get("totalEquipmentCount");
                Integer highCount = (Integer) statistics.get("highEfficiencyCount");
                Integer goodCount = (Integer) statistics.get("goodEfficiencyCount");
                Integer normalCount = (Integer) statistics.get("normalEfficiencyCount");
                Integer lowCount = (Integer) statistics.get("lowEfficiencyCount");
                
                if (totalCount != null && totalCount > 0) {
                    additionalStats.put("excellentRate", Math.round((double) highCount / totalCount * 100 * 100.0) / 100.0);
                    additionalStats.put("goodRate", Math.round((double) goodCount / totalCount * 100 * 100.0) / 100.0);
                    additionalStats.put("qualifiedRate", Math.round((double) normalCount / totalCount * 100 * 100.0) / 100.0);
                    additionalStats.put("unqualifiedRate", Math.round((double) lowCount / totalCount * 100 * 100.0) / 100.0);
                } else {
                    additionalStats.put("excellentRate", 0.0);
                    additionalStats.put("goodRate", 0.0);
                    additionalStats.put("qualifiedRate", 0.0);
                    additionalStats.put("unqualifiedRate", 0.0);
                }

                // 计算投入产出比
                Double totalValue = (Double) statistics.get("totalEquipmentValue");
                Double totalIncome = (Double) statistics.get("totalFeeIncome");
                Double totalCost = (Double) statistics.get("totalMaintenanceCost");
                
                if (totalValue != null && totalValue > 0) {
                    additionalStats.put("incomeToValueRatio", Math.round(totalIncome / totalValue * 100 * 100.0) / 100.0);
                } else {
                    additionalStats.put("incomeToValueRatio", 0.0);
                }

                if (totalCost != null && totalCost > 0) {
                    additionalStats.put("incomeToMaintenanceRatio", Math.round(totalIncome / totalCost * 100.0) / 100.0);
                } else {
                    additionalStats.put("incomeToMaintenanceRatio", 0.0);
                }

                // 计算平均设备价值
                if (totalCount != null && totalCount > 0 && totalValue != null) {
                    additionalStats.put("avgEquipmentValue", Math.round(totalValue / totalCount * 100.0) / 100.0);
                } else {
                    additionalStats.put("avgEquipmentValue", 0.0);
                }

                result.put("additionalStatistics", additionalStats);
            }

            logger.info("获取大型设备大屏综合数据成功");
            return result;

        } catch (Exception e) {
            logger.error("获取大型设备大屏综合数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取大型设备大屏综合数据失败: " + e.getMessage());
        }
    }
}