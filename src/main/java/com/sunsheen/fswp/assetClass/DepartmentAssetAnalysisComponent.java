package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("DepartmentAssetAnalysisComponent")
public class DepartmentAssetAnalysisComponent extends ABaseComponent {

    private static final Logger logger = LoggerFactory.getLogger(DepartmentAssetAnalysisComponent.class);

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForListComponent;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    private IDataPort queryDataForObjectComponent;

    private static final String OPERATION_SUMMARY = "summary";
    private static final String OPERATION_DETAILS = "details";
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static final int DEFAULT_PAGE_NUMBER = 1;

    @Override
    @Component(name = "DepartmentAssetAnalysisComponent", memo = "仪器设备使用管理部门分析，提供汇总及明细查询")
    @Params({
            @ParamItem(name = "operationType", comment = "操作类型: 'summary' (汇总统计) 或 'details' (明细查询). 此参数为必填项.", type = "java.lang.String"),
            @ParamItem(name = "userDepartmentCode", comment = "用户部门代码 (汇总时可选，明细时必选)", type = "java.lang.String"),
            @ParamItem(name = "userDepartmentName", comment = "用户部门名称 (汇总时可选，明细时可选)", type = "java.lang.String"),
            @ParamItem(name = "page", comment = "页码 (明细查询时使用，从1开始)", type = "java.lang.Integer"),
            @ParamItem(name = "pageSize", comment = "每页记录数 (明细查询时使用)", type = "java.lang.Integer")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List<java.util.Map<java.lang.String, java.lang.Object>>", name = "summaryData", comment = "操作类型为 'summary' 时返回，部门资产统计列表"),
            @ReturnItem(type = "java.util.Map<java.lang.String, java.lang.Object>", name = "detailsData", comment = "操作类型为 'details' 时返回，包含分页的资产明细数据和分页信息")
    })
    public Object run(Map inParams) {
        String operationType = (String) inParams.get("operationType");
        String userDepartmentCode = (String) inParams.get("userDepartmentCode");
        String userDepartmentName = (String) inParams.get("userDepartmentName");
        logger.info("接收到的原始参数 inParams: {}", inParams);
        logger.info("接收到的 operationType: '{}'", operationType);
        logger.info("接收到的 userDepartmentCode: '{}', 长度: {}", userDepartmentCode, (userDepartmentCode != null ? userDepartmentCode.length() : "null"));
        logger.info("接收到的 userDepartmentName: '{}', 长度: {}", userDepartmentName, (userDepartmentName != null ? userDepartmentName.length() : "null"));

        if (operationType == null || operationType.trim().isEmpty()) {
            logger.error("参数 'operationType' 是必需的。");
            return createErrorResult("参数 'operationType' 是必需的。");
        }

        try {
            if (OPERATION_SUMMARY.equalsIgnoreCase(operationType)) {
                return getDepartmentUsageSummary(userDepartmentCode, userDepartmentName);
            } else if (OPERATION_DETAILS.equalsIgnoreCase(operationType)) {
                if ((userDepartmentCode == null || userDepartmentCode.trim().isEmpty()) &&
                    (userDepartmentName == null || userDepartmentName.trim().isEmpty())) {
                    logger.error("参数 'userDepartmentCode' 或 'userDepartmentName' 在操作类型为 'details' 时至少需要提供一个。");
                    return createErrorResult("参数 'userDepartmentCode' 或 'userDepartmentName' 在操作类型为 'details' 时至少需要提供一个。");
                }

                Object pageObj = inParams.get("page");
                Object pageSizeObj = inParams.get("pageSize");

                Integer page = null;
                Integer pageSize = null;

                if (pageObj instanceof Number) {
                    page = ((Number) pageObj).intValue();
                } else if (pageObj != null) {
                    try {
                        page = Integer.parseInt(String.valueOf(pageObj));
                    } catch (NumberFormatException e) {
                        logger.warn("无法将 'page' 参数值 '{}' 解析为整数。将使用默认值或null继续。", pageObj, e);
                    }
                }

                if (pageSizeObj instanceof Number) {
                    pageSize = ((Number) pageSizeObj).intValue();
                } else if (pageSizeObj != null) {
                    try {
                        pageSize = Integer.parseInt(String.valueOf(pageSizeObj));
                    } catch (NumberFormatException e) {
                        logger.warn("无法将 'pageSize' 参数值 '{}' 解析为整数。将使用默认值或null继续。", pageSizeObj, e);
                    }
                }

                return getAssetDetails(userDepartmentCode, userDepartmentName, page, pageSize);
            } else {
                logger.warn("无效的 'operationType': {}", operationType);
                return createErrorResult("无效的 'operationType': " + operationType + ". 有效值为 'summary' 或 'details'.");
            }
        } catch (Exception e) {
            logger.error("处理 DepartmentAssetAnalysisComponent 时发生错误，操作类型 {}: {}", operationType, e.getMessage(), e);
            return createErrorResult("发生内部错误: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> getDepartmentUsageSummary(String userDepartmentCode, String userDepartmentName) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "asset.getDepartmentAssetUsageSummary");

        boolean hasFilter = false;
        if (userDepartmentCode != null && !userDepartmentCode.trim().isEmpty()) {
            queryParams.put("userDepartmentCode", userDepartmentCode.trim());
            logger.info("为汇总查询添加筛选条件 userDepartmentCode: '{}'", userDepartmentCode.trim());
            hasFilter = true;
        }
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            queryParams.put("userDepartmentName", userDepartmentName.trim());
            logger.info("为汇总查询添加筛选条件 userDepartmentName: '{}'", userDepartmentName.trim());
            hasFilter = true;
        }
        if (!hasFilter) {
            logger.info("未提供 userDepartmentCode 或 userDepartmentName，执行汇总查询时不按部门筛选。");
        }
        logger.info("开始获取部门使用情况汇总，参数: {}", queryParams);
        List<Map<String, Object>> result = (List<Map<String, Object>>) queryDataForListComponent.run(queryParams);
        logger.info("部门使用情况汇总查询完成，返回 {} 条记录", result != null ? result.size() : 0);
        return result != null ? result : Collections.emptyList();
    }

    private Map<String, Object> getAssetDetails(String userDepartmentCode, String userDepartmentName, Integer page, Integer pageSize) {
        int currentPage = (page == null || page < 1) ? DEFAULT_PAGE_NUMBER : page;
        int currentSize = (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
        int offset = (currentPage - 1) * currentSize;

        // Fetch asset list
        Map<String, Object> listParams = new HashMap<>();
        listParams.put("dataId", "asset.getAssetDetailsByDepartment");
        if (userDepartmentCode != null && !userDepartmentCode.trim().isEmpty()) {
            listParams.put("userDepartmentCode", userDepartmentCode.trim());
        }
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            listParams.put("userDepartmentName", userDepartmentName.trim());
        }
        listParams.put("dm_limit", currentSize);
        listParams.put("dm_offset", offset);
        logger.info("开始获取资产明细，参数: {}", listParams);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> assetList = (List<Map<String, Object>>) queryDataForListComponent.run(listParams);
        logger.info("资产明细查询完成，返回 {} 条记录", assetList != null ? assetList.size() : 0);

        // Fetch total count
        Map<String, Object> countParams = new HashMap<>();
        countParams.put("dataId", "asset.countAssetDetailsByDepartment");
        if (userDepartmentCode != null && !userDepartmentCode.trim().isEmpty()) {
            countParams.put("userDepartmentCode", userDepartmentCode.trim());
        }
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            countParams.put("userDepartmentName", userDepartmentName.trim());
        }
        logger.info("开始获取资产总数，参数: {}", countParams);

        Object rawCountResult = queryDataForObjectComponent.run(countParams);

        long totalRecords = 0;
        if (rawCountResult instanceof Number) {
            totalRecords = ((Number) rawCountResult).longValue();
        } else if (rawCountResult instanceof Map) {
            Map<String, Object> countResultMap = (Map<String, Object>) rawCountResult;
            if (countResultMap.containsKey("total_assets")) {
                Object countObj = countResultMap.get("total_assets");
                if (countObj instanceof Number) {
                    totalRecords = ((Number) countObj).longValue();
                } else if (countObj != null) {
                    try {
                        totalRecords = Long.parseLong(countObj.toString());
                    } catch (NumberFormatException e) {
                        logger.error("从Map中解析 total_assets 值失败: {}", countObj, e);
                    }
                }
            } else {
                 logger.warn("资产总数查询返回的Map中未找到键 'total_assets'。Map内容: {}", countResultMap);
            }
        } else if (rawCountResult != null) {
            logger.warn("资产总数查询返回了预料之外的类型: {}。尝试进行toString后解析。", rawCountResult.getClass().getName());
            try {
                totalRecords = Long.parseLong(rawCountResult.toString());
            } catch (NumberFormatException e) {
                logger.error("尝试将未知类型结果 ({}) 转换为Long失败: {}", rawCountResult, e.getMessage());
            }
        }

        long totalPages = (totalRecords == 0) ? 0 : (totalRecords + currentSize - 1) / currentSize;


        Map<String, Object> result = new HashMap<>();
        result.put("data", assetList != null ? assetList : Collections.emptyList());
        result.put("currentPage", currentPage);
        result.put("pageSize", currentSize);
        result.put("totalRecords", totalRecords);
        result.put("totalPages", totalPages);

        return result;
    }

    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", true);
        errorResult.put("message", message);
        return errorResult;
    }
}