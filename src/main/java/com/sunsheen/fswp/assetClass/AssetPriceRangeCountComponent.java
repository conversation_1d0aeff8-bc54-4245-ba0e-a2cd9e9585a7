package com.sunsheen.fswp.assetClass;

import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;

@Controller("AssetPriceRangeCountComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class AssetPriceRangeCountComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(AssetPriceRangeCountComponent.class);

    private static final String OPERATION_SUMMARY = "summary";
    private static final String OPERATION_DETAILS = "details";
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static final int DEFAULT_PAGE_NUMBER = 1;
    private static final List<String> VALID_PRICE_RANGE_IDENTIFIERS = Arrays.asList("R1", "R2", "R3", "R4");

    @Autowired
    @Qualifier("QueryDataForListComponent")
    private IDataPort queryDataForListComponent;

    @Autowired
    @Qualifier("QueryDataForObjectComponent") // Needed for count in details
    private IDataPort queryDataForObjectComponent;

    @Override
    @Component(name = "AssetPriceRangeCountComponent", memo = "按资产单价分档统计数量（1000-10万、10万-50万、50万-100万、100万以上），并提供各分档详情查询")
    @Params({
        @ParamItem(name = "operationType", comment = "操作类型: 'summary' (汇总统计) 或 'details' (明细查询). 默认为 'summary'. 此参数为可选.", type = "java.lang.String"),
        @ParamItem(name = "userDepartmentName", comment = "使用单位名称 (汇总和明细查询时均可选). 此参数为可选.", type = "java.lang.String"),
        @ParamItem(name = "priceRangeIdentifier", comment = "价格区间标识 (操作类型为 'details' 时必需，有效值为 R1(1000-10万), R2(10万-50万), R3(50万-100万), R4(≥100万)).", type = "java.lang.String"),
        @ParamItem(name = "page", comment = "页码 (明细查询时使用，从1开始). 此参数为可选.", type = "java.lang.Integer"),
        @ParamItem(name = "pageSize", comment = "每页记录数 (明细查询时使用). 此参数为可选.", type = "java.lang.Integer")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.List<java.util.Map<java.lang.String, java.lang.Object>>", name = "summaryData", comment = "操作类型为 'summary' 时返回，各单价档次的资产数量统计结果（1000-10万、10万-50万、50万-100万、100万以上）"),
            @ReturnItem(type = "java.util.Map<java.lang.String, java.lang.Object>", name = "detailsData", comment = "操作类型为 'details' 时返回，包含分页的资产明细数据和分页信息（1000-10万、10万-50万、50万-100万、100万以上）")
    })
    public Object run(Map param) { // Changed param type to non-generic Map
        String operationType = param.containsKey("operationType") ? (String) param.get("operationType") : OPERATION_SUMMARY;
        String userDepartmentName = (String) param.get("userDepartmentName");

        logger.info("AssetPriceRangeCountComponent - operationType: {}, userDepartmentName: {}", operationType, userDepartmentName);

        try {
            if (OPERATION_SUMMARY.equalsIgnoreCase(operationType)) {
                return getPriceRangeSummary(userDepartmentName);
            } else if (OPERATION_DETAILS.equalsIgnoreCase(operationType)) {
                String priceRangeIdentifier = (String) param.get("priceRangeIdentifier");

                Object pageObj = param.get("page");
                Object pageSizeObj = param.get("pageSize");
                Integer page = null;
                Integer pageSize = null;

                if (pageObj instanceof Number) {
                    page = ((Number) pageObj).intValue();
                } else if (pageObj != null) {
                    try { page = Integer.parseInt(String.valueOf(pageObj)); } catch (NumberFormatException e) { logger.warn("无法解析 'page' 参数: {}", pageObj); }
                }

                if (pageSizeObj instanceof Number) {
                    pageSize = ((Number) pageSizeObj).intValue();
                } else if (pageSizeObj != null) {
                    try { pageSize = Integer.parseInt(String.valueOf(pageSizeObj)); } catch (NumberFormatException e) { logger.warn("无法解析 'pageSize' 参数: {}", pageSizeObj); }
                }

                return getAssetDetailsByPriceRange(userDepartmentName, priceRangeIdentifier, page, pageSize);
            } else {
                logger.warn("无效的操作类型: {}", operationType);
                return createErrorResult("无效的操作类型: " + operationType);
            }
        } catch (Exception e) {
            logger.error("处理AssetPriceRangeCountComponent失败: {}", e.getMessage(), e);
            return createErrorResult("处理失败: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> getPriceRangeSummary(String userDepartmentName) {
        logger.info("开始按资产单价分档统计数量 (汇总)...userDepartmentName: {}", userDepartmentName);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("dataId", "asset.countAssetsByPriceRange");
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            queryParams.put("userDepartmentName", userDepartmentName.trim());
        }
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> resultList = (List<Map<String, Object>>) queryDataForListComponent.run(queryParams);
        logger.info("按资产单价分档统计数量 (汇总) 完成，查询到 {} 条档次数据", resultList != null ? resultList.size() : 0);
        return resultList != null ? resultList : Collections.emptyList();
    }

    private Map<String, Object> getAssetDetailsByPriceRange(String userDepartmentName, String priceRangeIdentifier, Integer page, Integer pageSize) {
        logger.info("getAssetDetailsByPriceRange - Input: userDepartmentName={}, priceRangeIdentifier={}, page={}, pageSize={}",
            userDepartmentName, priceRangeIdentifier, page, pageSize);

        if (priceRangeIdentifier == null || !VALID_PRICE_RANGE_IDENTIFIERS.contains(priceRangeIdentifier)) {
            logger.error("明细查询必需提供有效的 priceRangeIdentifier (R1(1000-10万), R2(10万-50万), R3(50万-100万), R4(≥100万))。收到: {}", priceRangeIdentifier);
            return createErrorResult("无效或缺失 priceRangeIdentifier 参数。有效值为: R1(1000-10万), R2(10万-50万), R3(50万-100万), R4(≥100万)");
        }

        int currentPage = (page == null || page < 1) ? DEFAULT_PAGE_NUMBER : page;
        int currentSize = (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
        int offset = (currentPage - 1) * currentSize;

        logger.info("getAssetDetailsByPriceRange - Calculated: currentPage={}, currentSize={}, offset={}",
            currentPage, currentSize, offset);

        Map<String, Object> listParams = new HashMap<>();
        listParams.put("dataId", "asset.getAssetDetailsByPriceRange");
        listParams.put("priceRangeIdentifier", priceRangeIdentifier);
        listParams.put("dm_limit", currentSize);
        listParams.put("dm_offset", offset);
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            listParams.put("userDepartmentName", userDepartmentName.trim());
        }
        logger.info("价格区间资产明细查询 - listParams to be executed: {}", listParams);

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> assetList = (List<Map<String, Object>>) queryDataForListComponent.run(listParams);
        logger.info("价格区间资产明细查询完成，返回 {} 条记录", assetList != null ? assetList.size() : 0);

        Map<String, Object> countParams = new HashMap<>();
        countParams.put("dataId", "asset.countAssetDetailsByPriceRange");
        countParams.put("priceRangeIdentifier", priceRangeIdentifier);
        if (userDepartmentName != null && !userDepartmentName.trim().isEmpty()) {
            countParams.put("userDepartmentName", userDepartmentName.trim());
        }
        Object rawCountResult = queryDataForObjectComponent.run(countParams);
        long totalRecords = 0;
        if (rawCountResult instanceof Number) {
            totalRecords = ((Number) rawCountResult).longValue();
        } else if (rawCountResult instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> countResultMap = (Map<String, Object>) rawCountResult;
            if (countResultMap.containsKey("total_assets")) {
                Object countObj = countResultMap.get("total_assets");
                if (countObj instanceof Number) {
                    totalRecords = ((Number) countObj).longValue();
                } else if (countObj != null) {
                    try { totalRecords = Long.parseLong(countObj.toString()); } catch (NumberFormatException e) { logger.error("从Map解析total_assets失败: {}", countObj, e); }
                }
            } else {
                logger.warn("价格区间资产总数查询返回Map中无 'total_assets' 键。 Map: {}", countResultMap);
            }
        } else if (rawCountResult != null) {
            logger.warn("价格区间资产总数查询返回未知类型: {}。尝试toString解析。", rawCountResult.getClass().getName());
            try { totalRecords = Long.parseLong(rawCountResult.toString()); } catch (NumberFormatException e) { logger.error("转换未知类型到Long失败: {}", rawCountResult, e); }
        }
        logger.info("价格区间资产总数: {}", totalRecords);

        Map<String, Object> result = new HashMap<>();
        result.put("data", assetList != null ? assetList : Collections.emptyList());
        result.put("currentPage", currentPage);
        result.put("pageSize", currentSize);
        result.put("totalRecords", totalRecords);
        result.put("totalPages", (totalRecords == 0) ? 0 : (totalRecords + currentSize - 1) / currentSize);

        return result;
    }

    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", true);
        errorResult.put("message", message);
        return errorResult;
    }
}
