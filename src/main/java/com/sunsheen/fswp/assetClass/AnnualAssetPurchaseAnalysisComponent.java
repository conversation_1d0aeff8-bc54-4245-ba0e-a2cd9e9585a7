package com.sunsheen.fswp.assetClass;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;

/**
 * 年度资产购置分析组件
 * 用于分析每年资产的购置情况，包括新增总值和累计总值
 */
@Service("AnnualAssetPurchaseAnalysisComponent")
public class AnnualAssetPurchaseAnalysisComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryDataForListComponent;

    @Override
    @Component(name = "AnnualAssetPurchaseAnalysisComponent", memo = "年度资产购置分析")
    @Params({
        @ParamItem(type = "java.lang.String", name = "userDepartmentName", comment = "使用部门名称，可选参数"),
        @ParamItem(type = "java.lang.String", name = "startYear", comment = "开始年份，可选参数"),
        @ParamItem(type = "java.lang.String", name = "endYear", comment = "结束年份，可选参数")
    })
    @Returns(retValue = {@ReturnItem(type = "java.util.Map", name = "result", comment = "返回格式化的年度购置分析结果")})
    public Object run(Map param) {
        try {
            // 准备查询参数
            Map<String, Object> queryParam = new HashMap<>();
            queryParam.put("dataId", "asset.getAnnualAssetPurchaseAnalysis");
            
            // 传递可选参数
            if (param.containsKey("userDepartmentName") && param.get("userDepartmentName") != null) {
                queryParam.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.containsKey("startYear") && param.get("startYear") != null) {
                queryParam.put("startYear", param.get("startYear"));
            }
            if (param.containsKey("endYear") && param.get("endYear") != null) {
                queryParam.put("endYear", param.get("endYear"));
            }

            // 执行查询
            List<Map<String, Object>> rawData = (List<Map<String, Object>>) queryDataForListComponent.run(queryParam);

            // 格式化返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1); // 成功状态码
            
            List<Map<String, Object>> formattedData = new ArrayList<>();
            
            if (rawData != null && !rawData.isEmpty()) {
                for (Map<String, Object> row : rawData) {
                    Map<String, Object> yearData = new HashMap<>();
                    
                    // 处理年份，确保是字符串格式
                    Object yearObj = row.get("year");
                    String yearStr = yearObj != null ? yearObj.toString() : "";
                    yearData.put("year", yearStr);
                    
                    // 处理新增资产总值
                    Object newAssetValueObj = row.get("newAssetValue");
                    double newAssetValue = 0.0;
                    if (newAssetValueObj != null) {
                        if (newAssetValueObj instanceof Number) {
                            newAssetValue = ((Number) newAssetValueObj).doubleValue();
                        } else {
                            try {
                                newAssetValue = Double.parseDouble(newAssetValueObj.toString());
                            } catch (NumberFormatException e) {
                                newAssetValue = 0.0;
                            }
                        }
                    }
                    yearData.put("newAssetValue", newAssetValue);

                    // 处理累计资产总值
                    Object totalAssetValueObj = row.get("totalAssetValue");
                    double totalAssetValue = 0.0;
                    if (totalAssetValueObj != null) {
                        if (totalAssetValueObj instanceof Number) {
                            totalAssetValue = ((Number) totalAssetValueObj).doubleValue();
                        } else {
                            try {
                                totalAssetValue = Double.parseDouble(totalAssetValueObj.toString());
                            } catch (NumberFormatException e) {
                                totalAssetValue = 0.0;
                            }
                        }
                    }
                    yearData.put("totalAssetValue", totalAssetValue);
                    
                    formattedData.add(yearData);
                }
            }
            
            result.put("data", formattedData);
            return result;
            
        } catch (Exception e) {
            // 错误处理
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 0); // 失败状态码
            errorResult.put("message", "查询年度资产购置分析失败: " + e.getMessage());
            errorResult.put("data", new ArrayList<>());
            return errorResult;
        }
    }
} 