package com.sunsheen.fswp.assetClass;

import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("LargeEquipmentCountComponent")
@BixComponentPackage(dirname = "资产管理组件", type = "BUSINESS")
public class LargeEquipmentCountComponent extends ABaseComponent {
    private static final Logger logger = LoggerFactory.getLogger(LargeEquipmentCountComponent.class);

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    private IDataPort queryDataForMap;

    @Override
    @Component(name = "LargeEquipmentCountComponent", memo = "统计大型设备总数")
    @Params({
        @ParamItem(name = "userDepartmentName", comment = "使用部门名称，可选参数", type = "java.lang.String"),
        @ParamItem(name = "assetName", comment = "资产名称，可选参数，支持模糊查询", type = "java.lang.String"),
        @ParamItem(name = "usageLevel", comment = "使用水平，可选参数", type = "java.lang.String"),
        @ParamItem(name = "warningStatus", comment = "预警状态，可选参数", type = "java.lang.String"),
        @ParamItem(name = "statisticsYear", comment = "统计年份，可选参数，默认当年", type = "java.lang.String")
    })
    @Returns(retValue = {
            @ReturnItem(type = "java.util.Map", name = "data", comment = "返回大型设备总数")
    })
    public Object run(Map param) {
        logger.info("开始统计大型设备总数...");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("dataId", "asset.countLargeEquipmentWithUsageRate");

            // 传递查询参数
            if (param.get("userDepartmentName") != null) {
                queryParams.put("userDepartmentName", param.get("userDepartmentName"));
            }
            if (param.get("assetName") != null) {
                queryParams.put("assetName", param.get("assetName"));
            }
            if (param.get("usageLevel") != null) {
                queryParams.put("usageLevel", param.get("usageLevel"));
            }
            if (param.get("warningStatus") != null) {
                queryParams.put("warningStatus", param.get("warningStatus"));
            }
            if (param.get("statisticsYear") != null) {
                queryParams.put("statisticsYear", param.get("statisticsYear"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) queryDataForMap.run(queryParams);

            if (result == null) {
                logger.warn("统计大型设备总数查询结果为空，返回默认值");
                result = new HashMap<>();
                result.put("total_count", 0);
            }

            // 转换为驼峰命名
            Map<String, Object> finalResult = new HashMap<>();
            finalResult.put("totalCount", result.getOrDefault("total_count", 0));

            logger.info("统计大型设备总数成功");
            return finalResult;

        } catch (Exception e) {
            logger.error("统计大型设备总数失败: {}", e.getMessage(), e);
            throw new RuntimeException("统计大型设备总数失败: " + e.getMessage());
        }
    }
}