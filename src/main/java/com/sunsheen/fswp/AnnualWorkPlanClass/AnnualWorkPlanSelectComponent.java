package com.sunsheen.fswp.AnnualWorkPlanClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

@Controller("AnnualWorkPlanSelectComponent")
@BixComponentPackage(dirname = "年度工作计划查询", type = "SYSTEM")
@Slf4j
public class AnnualWorkPlanSelectComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;
    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Override
    @Component(
            name = "AnnualWorkPlanSelectComponent",
            memo = "年度工作计划查询"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "查询结果")})
    @LogArgs
    public Object run(Map param) {

        String pageV = (String) this.getCallParam(param, "page");
        String pageSizeV = (String) this.getCallParam(param, "pageSize");
        if (pageV != null && pageSizeV != null) {
            try {
                int page = Integer.parseInt(pageV);
                int pageSize = Integer.parseInt(pageSizeV);
                Integer start = (page - 1) * pageSize;
                Integer limit = pageSize;
                param.put("start", start);
                param.put("limit", limit);
            } catch (Exception e) {
                log.error("page和pageSize非法");
                throw new RuntimeException("page和pageSize非法");
            }
        }

        HashMap<String, Object> resultMap = new HashMap<>();
        param.put("dataId", "AnnualWorkPlan.countSelectAnnualWorkPlan");
        resultMap.put("count", queryForObject.run(param));

        param.put("dataId", "AnnualWorkPlan.selectAnnualWorkPlan");
        resultMap.put("data", queryForList.run(param));
        return resultMap;
    }
}
