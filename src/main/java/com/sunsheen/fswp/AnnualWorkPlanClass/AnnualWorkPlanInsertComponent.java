package com.sunsheen.fswp.AnnualWorkPlanClass;


import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.fswp.util.UserUtil;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Controller("AnnualWorkPlanInsertComponent")
@BixComponentPackage(dirname = "年度工作计划存入", type = "SYSTEM")
@Slf4j
public class AnnualWorkPlanInsertComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForMapComponent")
    IDataPort queryForMap;

    @Override
    @Component(
            name = "AnnualWorkPlanInsertComponent",
            memo = "年度工作计划存入"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        // 获取当前用户
        String loginAccount = UserUtil.getLogAccount();

        Map department = (Map) queryForMap.run(Map.of(
                "dataId", "AnnualWorkPlan.selectDepartmentIdByNickName",
                "loginAccount", loginAccount
        ));

        if (department == null || department.isEmpty()) {
            log.error("部门不存在!");
            throw new RuntimeException("部门不存在!");
        }

        String departmentId = (String) department.get("deptId");
        String departmentName = (String) department.get("dept");

        param.put("departmentId", departmentId);
        param.put("departmentName", departmentName);

        return saveData.run(Map.of(
                "dataId", "AnnualWorkPlan.insertAnnualWorkPlan",
                "data", param
        ));
    }
}