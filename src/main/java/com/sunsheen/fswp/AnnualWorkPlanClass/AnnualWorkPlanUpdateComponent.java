package com.sunsheen.fswp.AnnualWorkPlanClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("AnnualWorkPlanUpdateComponent")
@BixComponentPackage(dirname = "年度工作计划查询", type = "SYSTEM")
@Slf4j
public class AnnualWorkPlanUpdateComponent extends ABaseComponent {

    @Autowired
    @Qualifier("QueryDataForListComponent")
    IDataPort queryForList;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Override
    @Component(
            name = "AnnualWorkPlanUpdateComponent",
            memo = "年度工作计划修改"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "修改成功的数量")})
    @LogArgs
    public Object run(Map param) {

        // 参数校验
        List<String> args = Arrays.asList("id", "title", "content", "progress", "progressPercent", "deviationPercent", "departmentId");
        args.forEach(arg -> {
            if (!param.containsKey(arg)) {
                log.error("缺少参数{}", arg);
                throw new RuntimeException("缺少参数" + arg);
            }
        });
        String departmentId = (String) this.getCallParam(param, "departmentId");

        String departmentName = (String) queryForObject.run(Map.of(
                "dataId", "AnnualWorkPlan.selectDepartmentName",
                "departmentId", departmentId
        ));

        if (departmentName == null || departmentName.isEmpty()) {
            log.error("部门不存在!");
            throw new RuntimeException("部门不存在!");
        }

        param.put("departmentName", departmentName);
        return saveData.run(Map.of(
                "dataId", "AnnualWorkPlan.updateAnnualWorkPlan",
                "data", param
        ));
    }
}
