package com.sunsheen.fswp.AnnualWorkPlanClass;

import com.sunsheen.fswp.aop.LogArgs;
import com.sunsheen.jfids.system.bizass.annotation.*;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Controller("AnnualWorkPlanDeleteComponent")
@BixComponentPackage(dirname = "年度工作计划删除", type = "SYSTEM")
@Slf4j
public class AnnualWorkPlanDeleteComponent extends ABaseComponent {

    @Autowired
    @Qualifier("SaveDataComponent")
    IDataPort saveData;

    @Autowired
    @Qualifier("QueryDataForObjectComponent")
    IDataPort queryForObject;

    @Override
    @Component(
            name = "AnnualWorkPlanDeleteComponent",
            memo = "年度工作计划删除"
    )
    @Params({ @ParamItem(
            type = "java.util.Map",
            name = "data",
            comment = "数据源参数"
    )})
    @Returns(retValue = {@ReturnItem(type = "java.util.HashMap", name = "result", comment = "保存成功的记录数")})
    @LogArgs
    public Object run(Map param) {
        if (!param.containsKey("id")) {
            log.error("缺少id参数");
            throw new IllegalArgumentException("缺少id参数");
        }
        return saveData.run(Map.of(
                "dataId", "AnnualWorkPlan.deleteAnnualWorkPlanById",
                "data", param
        ));
    }
}