server:
  port: 19385
#  front_ip: **************
#  back_ip: **************
  front_ip:  ***********
  back_ip:  ***********
  servlet:
    context-path: /fswp
    session:
      timeout: 18000s   #回话超时时间，单位秒
      notintercept:    #配置不用登录就可以访问的接口，正则表达式
        - /SecurityController/updatePwd1.*
        - /app-manager/.*

front:
  #    url: http://**************:5173
#  url: http://localhost:5173
#  url: http://${server.front_ip}:8080
  url: http://${server.front_ip}:5173

spring:
  # Redis配置
  #data:
  #redis:
  #check: false #是否开启redis可用性的检查，开启之后，将每3s发送一次ping命令，检查redis是否可用
  #enable: true #设置redis是否可用，如果设置为false，那么将不从redis取数据
  #host: localhost
  #port: 6379
  #password:
  #timeout: 2000ms         # 连接超时时间
  #lettuce:
  #pool:
  #max-active: 32      # 最大连接数，负值表示没有限制，默认8
  #max-wait: 2000      # 最大阻塞等待时间，负值表示没限制，默认-1
  #max-idle: 16        # 最大空闲连接，默认8
  #min-idle: 8         # 最小空闲连接，默认0
  datasource:
    url: *********************************************************************************************
    username: application
    password: AubR@0472Zx
    driver-class-name: com.mysql.cj.jdbc.Driver

  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 700MB
      enabled: true
      file-size-threshold: 100MB
      location: /tmp/uploads

  application:
    name: ${application.profile.name}
  web:
    resources:
      static-locations: classpath:/static/
#HKDAS相关配置,springboot同类项未配置相关信息使用HKDAS中的配置
application:
  registry:
    address: **************:8999
    client:
      enabled: false
  profile:
    group: das
    code: fswp
    name: fswp项目
#添加核格作用域下面的配置，优先级高于spring下的datasource配置
hearken:
  #数据库相关配置
  datasource:
    #默认数据源
    #    default:
    #      driver-class-name: dm.jdbc.driver.DmDriver
    #      password: sftt12345678
    #      username: SFTT
    #      url: jdbc:dm://**************:5236/SFTT
    default:
      dialect: MySQLDialect
      url: *******************************************************************************************************
      username: application
      password: AubR@0472Zx
      driver-class-name: com.mysql.cj.jdbc.Driver
    research:
      url: ***********************************************************************************************************
      username: research
      password: research
      driver-class-name: com.mysql.cj.jdbc.Driver
    pms:
      url: ********************************************************************************************************
      username: pms_user
      password: pms_123
      driver-class-name: com.mysql.cj.jdbc.Driver
    #其他数据源
    #db_orcl:
    #dialect: Oracle10gDialect
    #driver-class-name: oracle.jdbc.driver.OracleDriver
    #password: pwd_mop_apps
    #username: mop_apps
    #url: **********************************************
    # 连接池配置
    druid:
      # 初始化连接数
      initialSize: 5
      #连接池中的最小空闲连接数
      minIdle: 5
      #控制连接池中的最大活动连接数
      maxActive: 15
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存时间
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      # 打开 PSCache，并且指定每个连接上 PSCache 的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的 Filter，去掉后监控界面 SQL 无法统计，wall 用于防火墙
      filters: stat,wall,slf4j
      # 通过 connectProperties 属性打开 mergeSql 功能；慢 SQL 记录
      connectProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

logging:
  config: classpath:logging-config-dev.xml
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

# 配置 cas server 信息123
cas:
  # cas服务端的地址
  server-url-prefix: https://ywtb.cuit.edu.cn/authserver
  # cas服务端的登录地址
  server-login-url: https://ywtb.cuit.edu.cn/authserver/login
  # liying本机调试地址当前服务器的地址(客户端)
  #    client-host-url: http://**************:19385
  # 应用服务器地址 当前服务器的地址(客户端)
  client-host-url: http://${server.back_ip}:19385
  # Ticket校验器使用Cas30ProxyReceivingTicketValidationFilter
  validation-type: cas

# 排除接口配置
ignore-pattern: (data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)