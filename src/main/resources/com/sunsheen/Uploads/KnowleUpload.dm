<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="KnowledgeUpload" description="法规政策知识库">

   <database id="insertKonwle" resultMap="java.lang.Integer" description="新增学校信息管理表数据">
    	INSERT INTO knowledge_Regulations
        (title, file_no, subject, file_addr, publisher, pub_date, valid_status, expire_date, editor, edit_date)
        VALUES(:title, :file_no, :subject, :file_addr, :publisher, :pub_date, :valid_status, :expire_date, :editor, :edit_date);
    </database>


     <database id="selectLaws" resultMap="java.util.Map" description="分页查询政策法规">
               SELECT title, subject, publisher, pub_date, id
               FROM knowledge_Regulations
               where valid_status = 1
            </database>

     <database id="selectLawsInfo" resultMap="java.util.Map" description="分页查询政策法规">
                    SELECT title, subject, publisher, pub_date, file_addr
                    FROM knowledge_Regulations
                    where valid_status = 1
                 </database>

     <database id="selectLawById" resultMap="java.util.Map" description="通过ID查询法规">
                    SELECT *
                    FROM knowledge_Regulations
                    where id = :id
     </database>

     <database id="selectLawsCount" resultMap="java.util.Map" description="查询政策法规总条数">
               SELECT count(*) as count
               FROM knowledge_Regulations
               where valid_status = 1;
            </database>

     <database id="selectLawsByTitle" resultMap="java.util.Map" description="分页查询政策法规">
               SELECT *
               FROM knowledge_Regulations
               where title = :title;
            </database>

<database id = "deleteLaws" resultMap="java.util.Map" description="逻辑删除法规政策">
    UPDATE knowledge_Regulations
    SET valid_status=0,expire_date = :expire_date
    WHERE title = :title;
    </database>


    <database id = "updateLaws" resultMap="java.util.Map" description="逻辑删除法规政策">
        UPDATE knowledge_Regulations
        SET title=:title, publisher=:publisher,pub_date=:pub_date,
        file_addr = :file_addr,expire_date = :expire_date
        WHERE title = :oldTitle;
        </database>

     <database id = "selectLawsBy" resultMap="java.util.Map" description="条件查询法律法规">
        select * from knowledge_Regulations
            where 1=1
            #if ($P.title and $P.title != '')
            AND title LIKE CONCAT('%', :title, '%')
            #end
            #if ($P.publisher and $P.publisher != '')
            AND publisher LIKE CONCAT('%', :publisher, '%')
            #end
            #if ($P.pub_date and $P.pub_date != '')
            AND pub_date >= :pub_date
            #end
            #if ($P.subject and $P.subject != '')
            AND subject LIKE CONCAT('%', :subject, '%')
            #end
     </database>
</sqlMap>