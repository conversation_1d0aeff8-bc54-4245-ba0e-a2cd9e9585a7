<?xml version="1.0" encoding="UTF-8"?>

<sqlMap namespace="PMS" description="采购管理系统">

    <database id="selectProjectInfo" resultMap="java.lang.Integer" description="查询项目信息">
        SELECT
        pi.pid,
        pi.pname,
        su.dept AS dept_name,
        pi.purcharse_type,
        pi.budget,
        CASE pi.pstatus
        WHEN 1 THEN '待提交'
        WHEN 2 THEN '审批中'
        WHEN 3 THEN '已通过'
        WHEN 4 THEN '执行中'
        WHEN 5 THEN '已归档'
        END AS status_desc
        FROM pms_project_info pi
        LEFT JOIN (SELECT DISTINCT deptId, dept FROM sys_user) su
        ON su.deptId = pi.rp_deptId
        WHERE rp_deptId = :deptId

        ORDER BY pi.create_time DESC
    </database>
    <database id="deleteProjectInfo" resultMap="java.lang.Integer" description="删除项目信息">
        UPDATE pms_project_info
        SET is_delete = 1
        WHERE pid = :id
    </database>
    <database id="SelectCountByDeptAndStatus" resultMap="java.lang.Integer" description="按部门和状态统计项目数量">
        SELECT
        su.dept AS 部门,
        po.pstatus AS 状态,
        COUNT(po.pid) AS 项目数量
        FROM pms_project_info po
        JOIN (SELECT DISTINCT deptId, dept FROM sys_user) su ON po.rp_deptId = su.deptId
        where is_delete = 0
        GROUP BY su.dept, po.pstatus
        ORDER BY 部门, 状态;
    </database>

    <database id="InsertProjectInfo" resultMap="java.lang.Integer" description="插入项目信息">
        INSERT INTO pms_project_info (
        pid, pname, purcharse_type, Is_center, rp_deptId,
        contact, contact_phone, content, fund_source,
        fund_name, budget, ptype, winner_amount,
        ticket_amount, is_imports, is_argumentation,
        is_sample, applyTime
        ) VALUES (
        :pid, :pname, :purcharse_type, :Is_center, :rp_deptId,
        :contact, :contact_phone, :content, :fund_source,
        :fund_name, :budget, :ptype, :winner_amount,
        :ticket_amount, :is_imports, :is_argumentation,
        :is_sample, :applyTime
        );
    </database>
    <database id="InsertProjectAttachment" resultMap="java.lang.Integer" description="插入项目对应附件信息">
        INSERT INTO pms_project_attachment (pid, aname, create_time)
        VALUES (:pid, :aname, :create_time);
    </database>
    <database id="InsertProjectGoods" resultMap="java.lang.Integer" description="插入项目对应品目信息">
        INSERT INTO pms_project_goods (
        pid, pa_name, reference, amount, unit, unit_price, total_price
        ) VALUES (
        :pid, :pa_name, :reference, :amount, :unit, :unit_price, :total_price
        );
    </database>


</sqlMap>