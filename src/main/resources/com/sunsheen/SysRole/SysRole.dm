<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="SysRole" description="角色管理">

 <database id="selectRoleList" resultMap="java.util.HashMap" description="查询学校信息">
            SELECT
            roleId ,
            roleName,
            status,
            roleKey,
            roleSort,
            createTime
            FROM
            sys_role
            where 1=1
             #if ($P.roleName and $P.roleName!='')
                          and roleName = :roleName
                        #end
             #if ($P.roleKey and $P.roleKey!='')
                         and roleKey = :roleKey
                        #end
             #if ($P.status and $P.status!='')
                         and status = :status
                        #end
            order by roleSort
        </database>

<database id = "insertRole" resultMap="java.lang.Integer" description="新增角色数据到角色表">
    INSERT INTO sys_role
    (roleId,roleName, roleKey, roleSort, status, delFlag,createTime)
    VALUES(uuid(),:roleName, :roleKey,:roleSort, :status,
    :delFlag, :createTime);
    </database>


<database id = "insertRoleMeun" resultMap="java.util.Map" description="新增角色数据到角色表">
    insert into sys_role_menu(roleId, menuId)
     values(:roleId,:menuId)
    </database>



<database id = "updateRole" resultMap="java.util.Map" description="修改角色数据到角色表">
    UPDATE sys_role
    SET
    createTime=:createTime
    #if ($P.roleName and $P.roleName!='')
          , roleName = :roleName
    #end
    #if ($P.roleKey and $P.roleKey!='')
    , roleKey=:roleKey
    #end
    #if ($P.roleSort and $P.roleSort!='')
        , roleSort=:roleSort
   #end
   #if ($P.status and $P.status!='')
           , status=:status
      #end
   #if ($P.delFlag and $P.delFlag!='')
              , delFlag=:delFlag
         #end
    WHERE roleId=:roleId;
    </database>

    <database id = "updateRoleMenu" resultMap="java.lang.Integer" description="修改角色菜单绑定信息">
        UPDATE sys_role_menu
        SET
           menuId = :menuList
        WHERE roleId=:roleId;
        </database>

 <database id="selectMenuList" resultMap="java.lang.Integer" description="查询菜单信息">
             SELECT
             *
             from
             sys_menu
             where 1=1
              #if ($P.menuId and $P.menuId!='')
                   and menuId=:menuId
              #end
             #if ($P.menuName and $P.menuName!='')
                 and menuName=:menuName
             #end
        </database>

 <database id="selectMenuByRole" resultMap="java.lang.Integer" description="查询菜单信息">
            SELECT menuId
            FROM  sys_role_menu
            WHERE roleId = :roleId
        </database>

 <database id="selectMenuByRoles" resultMap="java.lang.Integer" description="查询菜单信息">
            SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
        </database>


<database id="deleteRole" resultMap="java.lang.Integer" description="删除角色表数据">
            delete from
                sys_role
            where roleId = :roleId
    </database>

<database id="deleteRoleMenu" resultMap="java.lang.Integer" description="删除角色表数据">
            delete from
                sys_role_menu
            where roleId = :roleId
    </database>

<database id="deleteRoleUser" resultMap="java.lang.Integer" description="删除角色表数据">
            delete from
                sys_role_user
            where roleId = :roleId
    </database>

<database id="selectRoleByUser" resultMap="java.util.Map" description="查询用户角色信息">
         SELECT
         roleId
         from
         sys_role_user
         where 1=1
          #if ($P.userId and $P.userId!='')
              and userId=:userId
          #end
    </database>

<database id="updateRoleByUserId" resultMap="java.lang.Integer" description="修改用户角色信息">
    UPDATE sys_role_user
    SET
        roleId = :roleId
    WHERE userId=:userId;
</database>

<database id="insertRoleByUserId" resultMap="java.lang.Integer" description="新增用户角色信息">
    INSERT INTO sys_role_user
    (userId,roleId)
    VALUES(:nickName,:roleId);
</database>

<database id="updateRoleByRoleName" resultMap="java.lang.Integer" description="修改用户角色信息">
    UPDATE sys_user
    SET
        roleName = :roleName
    WHERE nickName=:nickName;
</database>

<database id="selectUserIdByRoleId" resultMap="java.util.Map" description="根据角色ID查询用户ID">
    SELECT
        userId
    FROM
        sys_role_user
    WHERE roleId = :roleId;
</database>

<database id="deleteUserRole" resultMap="java.lang.Integer" description="删除用户角色信息">
    delete from
        sys_role_user
    where userId = :nickName
</database>



</sqlMap>