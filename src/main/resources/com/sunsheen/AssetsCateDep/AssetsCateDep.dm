<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="AssetsCateDep" description="资产管理">

  <database id="selectAssetsCz" resultMap="java.lang.Integer" description="查询资产">
    SELECT cz_category, sum(quantity) quantity, sum(amount) amount
    FROM assets_statistic et
      group by cz_category
    </database>

      <database id="selectAssetsJy" resultMap="java.lang.Integer" description="查询资产">
        SELECT jy_category, sum(quantity) quantity, sum(amount) amount
        FROM assets_statistic et
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName!='')
          and et.department = :userDepartmentName
        #end
          group by jy_category
        </database>

      <database id="selectDepSum" resultMap="java.lang.Integer" description="查询资产">
        SELECT department, sum(quantity) quantity, sum(amount) amount
        FROM assets_statistic et
          group by department
        </database>

      <database id="selectjyCate" resultMap="java.lang.Integer" description="查询资产">
        SELECT distinct et.jy_category
        FROM assets_statistic et
        WHERE 1=1
        </database>

      <database id="selectczCate" resultMap="java.lang.Integer" description="查询资产">
        SELECT distinct et.cz_category
        FROM assets_statistic et
        WHERE 1=1
        </database>


            <database id="selectDep" resultMap="java.lang.Integer" description="查询资产">
              SELECT distinct et.department
              FROM assets_statistic et
              WHERE 1=1
              </database>

</sqlMap>