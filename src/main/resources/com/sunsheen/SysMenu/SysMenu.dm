<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="SysMenu" description="菜单管理">
  <database id = "insertMenu" resultMap="java.lang.Integer" description="新增菜单">
      INSERT INTO sys_menu
      (menuName, parentName, parentId, orderNum, path, routeName,menuType,visible,icon)
      VALUES(:menuName, :parentName, :parentId, :orderNum, :path,
      :routeName,:menuType,:visible,:icon)
  </database>

  <database id = "updateMenu" resultMap="java.lang.Integer" description="编辑菜单">
        update  sys_menu set updateTime=:updateTime
        #if ($P.menuName and $P.menuName!='')
               ,menuName = :menuName
            #end
         #if ($P.orderNum and $P.orderNum!='')
                ,orderNum = :orderNum
            #end
         #if ($P.path and $P.path!='')
               ,path = :path
         #end
        #if ($P.routeName and $P.routeName!='')
               ,routeName = :routeName
         #end
        #if ($P.menuType and $P.menuType!='')
            ,menuType = :menuType
         #end
        #if ($P.visible and $P.visible!='')
            ,visible = :visible
        #end
       #if ($P.icon and $P.icon!='')
           ,icon = :icon
       #end
       where menuId=:menuId
     </database>

     <database id = "delMenu" resultMap="java.lang.Integer" description="删除菜单">
            delete from  sys_menu  where menuId in (:menuIds)
     </database>

      <database id = "selectMenu" resultMap="java.util.Map" description="选择菜单列表">
             select * from  sys_menu
             where 1=1
             #if ($P.menuId and $P.menuId!='')
                 and menuId=:menuId
             #end
             #if ($p.menuName and $p.menuName != '')
                and menuName like CONCAT('%',:menuName, '%')
             #end
      </database>

      <database id = "selectMenuLock" resultMap="java.lang.Integer" description="选择菜单">

          #if($P.menuName and $p.menuName != '')
              select * from  sys_menu  where menuName=:menuName
               #if($P.menuId and $p.menuId != '')
                     and menuId !=:menuId
               #end
              for update
          #end
      </database>

      <database id = "selectMenuInList" resultMap="java.util.Map" description="查询多个菜单">
           select * from  sys_menu  where visible =1 and menuId in (:menuIds)
      </database>

      <database id = "SysMenuTree" resultMap="java.util.Map" description="查询菜单树">
          select
              menuId,
              menuName,
              parentId,
              parentName,
              orderNum,
              visible
          from
              sys_menu
          where 1=1
          #if ($P.menuId and $P.menuId!='')
              and menuId=:menuId
          #end
          #if ($P.menuName and $p.menuName != '')
             and menuName like %:menuName%
          #end
        </database>



</sqlMap>