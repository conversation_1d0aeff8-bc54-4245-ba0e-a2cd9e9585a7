<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="PeopleChange" description="资产变动">

<database id="selectDepartChange" resultMap="java.lang.Integer" description="查询资产">
    SELECT
    a.department as department,
    count(distinct et.user_name) as userNum,
    sum(et.quantity) as quantity,
    sum(et.amount) as amount
    FROM asset_Registration et left join teacher_infomation a
    on et.user_code = a.log_account
    WHERE
    et.user_department_name != a.department
    group by a.department
    </database>

    <database id="selectPeopleChange" resultMap="java.lang.Integer" description="查询资产">
       SELECT
       et.user_department_name as department,
       COUNT(distinct et.user_name) as userNum,
       sum(et.quantity) as quantity,
       sum(et.amount) as amount
        FROM asset_Registration et left join teacher_infomation a
                            on et.user_code = a.log_account
                            where et.user_code
        NOT in (
        SELECT et.user_code FROM asset_Registration et
                            inner join teacher_infomation a
                            on et.user_code = a.log_account
         )
         group by et.user_department_name
        </database>


        <database id="selectDepChangeDetail" resultMap="java.lang.Integer" description="查询资产">
            SELECT
            et.user_name,
            a.department as newDepart,
            et.user_department_name as oldDepart,
            et.category_name as jy_category,
            et.national_standard_name as cz_category,
            et.asset_name,
            et.quantity,
            et.unit_price
            FROM asset_Registration et left join teacher_infomation a
            on et.user_code = a.log_account
            WHERE
            et.user_department_name != a.department
             limit :start1,:limit1
            </database>



                <database id="selectPeoChangeDetail" resultMap="java.lang.Integer" description="查询资产">
                    SELECT
                    et.user_name,
                    et.user_department_name as department,
                    et.category_name as jy_category,
                    et.national_standard_name as cz_category,
                    et.asset_name,
                    et.quantity,
                    et.unit_price
                    FROM asset_Registration et left join teacher_infomation a
                    on et.user_code = a.log_account
                    where et.user_code
                    NOT in (
                    SELECT et.user_code FROM asset_Registration et
                    inner join teacher_infomation a
                    on et.user_code = a.log_account
                    )
                    limit :start1,:limit1
                    </database>

        <database id="selectDepCounts" resultMap="java.lang.Integer" description="查询资产">
            SELECT
            count(*) as count
            FROM asset_Registration et left join teacher_infomation a
            on et.user_code = a.log_account
            WHERE
            et.user_department_name != a.department
            </database>

 <database id="selectPeoCounts" resultMap="java.lang.Integer" description="查询资产">
                    SELECT
                    count(*) as count
                    FROM asset_Registration et left join teacher_infomation a
                    on et.user_code = a.log_account
                    where et.user_code
                    NOT in (
                    SELECT et.user_code FROM asset_Registration et
                    inner join teacher_infomation a
                    on et.user_code = a.log_account
                    )
                    </database>

</sqlMap>