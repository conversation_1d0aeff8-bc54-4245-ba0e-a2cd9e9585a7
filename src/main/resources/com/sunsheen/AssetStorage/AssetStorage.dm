<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="AssetStorage" description="资产预警">
    <database id="getALL" resultMap="java.util.HashMap" description="个人资产总和预警">
        select
            distinct
            department_name,
            department_code,
            user_code ,
            user_name,
            quantity,
            -- wl.warning_level,
            amount,
            ranking
        from assets_over_regis ar
        -- join warning_list wl on ar.user_code = wl.bussiness_id
        WHERE wl.indicator_id = 'zc003'
          #if ($P.department and $P.department != '')
          and department_code = :department
        #end
        ORDER BY quantity DESC;
    </database>


    <database id="getALLPri" resultMap="java.util.HashMap" description="个人资产总和预警">
            select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
                 #if ($P.userDept and $P.userDept != 'all')
                   and department_name = :userDept
                 #end
                 #if ($P.userDept and $P.userDept == 'all' and $P.lookDept and $P.lookDept != '' and $P.lookDept != '全校')
                   and department_name = :lookDept
                 #end
            ORDER BY quantity DESC
        </database>

        <database id="countALLPri" resultMap="java.util.HashMap" description="个人资产总和预警">
                    select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
                         #if ($P.userDept and $P.userDept != 'all')
                           and department_name = :userDept
                         #end
                         #if ($P.userDept and $P.userDept == 'all' and $P.lookDept and $P.lookDept != '' and $P.lookDept != '全校')
                           and department_name = :lookDept
                         #end
                </database>

    <database id="getUserbyCode" resultMap="java.util.HashMap" description="个人资产详情预警">
        SELECT
            user_name,
            user_code,
            user_department_name,
            user_department_code,
            asset_name,
            asset_code,
            category_name,
            category_code,
            national_standard_name,
            national_standard_code,
            quantity,
            amount
        FROM
            asset_Registration
        WHERE 1=1
            #if($P.userCode and $P.userCode != '')
                and user_code = :userCode
            #end
        limit :pageSize offset  :current ;
    </database>

    <database id="getTotal" resultMap="java.util.Integer" description="个人资产详情预警">
            SELECT
                count(*)
            FROM
                asset_Registration
            WHERE 1=1
                #if($P.userCode and $P.userCode != '')
                    and user_code = :userCode
                #end
    </database>

    <database id="getMove" resultMap="java.util.Integer" description="人员离职的异常">
        SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
    </database>

     <database id="countMove" resultMap="java.util.Integer" description="人员离职的异常">
            SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
        </database>

    <database id="getMoveByDept" resultMap="java.util.Integer" description="部门的人员离职的异常">
        SELECT  department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        where
            department_name = :department_name
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
    </database>


<database id="countMoveByDept" resultMap="java.util.Integer" description="部门的人员离职的异常">
        SELECT count(*)
        FROM  (SELECT department_name,department_code,user_code,
            user_name, state
            FROM
                assets_teacher_leave
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                    and wl.is_closed = '0'
            WHERE
                department_name = :department_name
            GROUP BY
                department_name,
                department_code,
                user_code,
                user_name,
                state) as a
    </database>

    <database id="getDetailedMove" resultMap="java.util.Integer" description="人员离职的异常详情">
                    SELECT
                        atl.user_name,
                        atl.user_code,
                        atl.department_name,
                        atl.department_code,
                        atl.state,
                        ar.asset_name,
                        ar.asset_code,
                        ar.category_name,
                        ar.category_code,
                        ar.national_standard_name,
                        ar.national_standard_code,
                        ar.quantity,
                        ar.amount
                    from
                        assets_teacher_leave atl
                    join asset_Registration ar on ar.user_code = atl.user_code
                    where 1=1
                    #if($P.department  and $P.department != '')
                         and atl.department_code = :department
                    #end
                    #if($P.userCode and $P.userCode != '')
                        and atl.user_code = :userCode
                    #end
                    limit :pageSize offset  :current ;
    </database>

    <database id="getMoveTotal" resultMap="java.util.Integer" description="人员离职的异常详情总条数">
                SELECT
                    count(*)
                FROM
                  assets_teacher_leave atl
                  join asset_Registration ar on ar.user_code = atl.user_code
                where 1=1
                #if($P.department  and $P.department != '')
                     and atl.department_code = :department
                #end
                #if($P.userCode and $P.userCode != '')
                    and atl.user_code = :userCode
                #end
        </database>

    <database id="getTransfer" resultMap="java.util.Integer" description="全校部门调动分页数据">
            SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
        </database>

    <database id="countTransfer" resultMap="java.lang.Integer" description="全校部门调动总数">
            SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
        </database>

    <database id="getTransferByDept" resultMap="java.util.Integer" description="按部门调动分页数据">
            SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            WHERE new_department = :department_name
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
        </database>

    <database id="countTransferByDept" resultMap="java.lang.Integer" description="按部门调动总数">
            SELECT COUNT(*)
            FROM (SELECT
                     old_department,
                     old_department_code,
                     new_department,
                     new_department_code,
                     user_code,
                     user_name,
                     SUM(quantity) AS quantity,
                     SUM(amount) AS amount
                 FROM assets_dept_change
                 WHERE new_department = :department_name
                 GROUP BY
                     old_department,
                     old_department_code,
                     new_department,
                     new_department_code,
                     user_code,
                     user_name) As a
        </database>



    <database id="getDetailedTransfer" resultMap="java.util.Integer" description="人员调动的异常详情">
               SELECT
                   ar.user_department_name as old_department,
                   ar.user_department_code as old_department_code,
                   ac.new_department,
                   ac.new_department_code,
                   ac.user_name,
                   ac.user_code,
                   ar.asset_name,
                   ar.asset_code,
                   ar.category_name,
                   ar.category_code,
                   ar.national_standard_name,
                   ar.national_standard_code,
                   ar.quantity,
                   ar.amount
               from
                   assets_dept_change ac
               join asset_Registration ar on ar.user_code = ac.user_code
               where 1=1
               #if($P.department  and $P.department != '')
                    and ac.new_department = :department
               #end
               #if($P.userCode and $P.userCode != '')
                   and ac.user_code = :userCode
               #end
               limit :pageSize offset  :current ;
        </database>
        <database id="getTransferTotal" resultMap="java.util.Integer" description="人员调动的异常详情总条数">
               SELECT
                   count(*)
               FROM
                  assets_dept_change ac
                  join asset_Registration ar on ar.user_code = ac.user_code
               where 1=1
               #if($P.department  and $P.department != '')
                    and ac.new_department = :department
               #end
               #if($P.userCode and $P.userCode != '')
                   and ac.user_code = :userCode
               #end
        </database>

        <database id="warningClose" resultMap="java.util.Integer" description="关闭预警">
            UPDATE warning_list
            SET update_time = :update_time,
                end_time = :end_time,
                is_closed = '1',
                closed_by = :closed_by,
                closed_desc = :closed_desc
            WHERE 1=1
            -- 个人资产数量异常
            #if($P.warning_type and $P.warning_type == "QUANT")
                and warning_id = CONCAT('QUANT_', :user_code)
            #end
            -- 资产登记人员状态异常
            #if($P.warning_type and $P.warning_type == "MOVE")
                and warning_id = CONCAT('MOVE_', :user_code)
            #end
            -- 资产使用单位异常
            #if($P.warning_type and $P.warning_type == "DEPARTMENT")
                and warning_id = CONCAT('DEPARTMENT_', :user_code)
            #end
            -- 资产流程异常（未定）
            #if($P.warning_type and $P.warning_type == "ZC_FLOW")
                and warning_id = CONCAT('ZC_FLOW_', :user_code)
            #end
        </database>
</sqlMap>