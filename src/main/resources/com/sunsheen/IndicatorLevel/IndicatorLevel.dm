<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="IndicatorLevel" description="预警级别规则">
    <database id="queryIndicatorLevels" resultMap="java.util.HashMap" description="查询指标预警规则">
        SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type,
            su.userName,
            il.modifer,
            il.update_time
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        LEFT JOIN sys_user as su on su.nickName = il.modifer
        WHERE
            1=1
            #if ($P.indicatorName and $P.indicatorName != '')
                AND il.indicator_name LIKE CONCAT('%', :indicatorName, '%')
            #end
        ORDER BY il.indicator_id ASC, il.warning_level ASC, il.supervisor_level ASC;
    </database>

    <database id="queryKnowledgeIndicatorsForDropdown" resultMap="java.util.HashMap" description="获取知识库中的指标名称与编号">
        SELECT
            indicator_id,
            indicator_name
        FROM
            knowledge_indicators
        WHERE
            valid_status = 1
        ORDER BY indicator_name ASC;
    </database>

    <database id="queryWarningLevels" resultMap="java.util.HashMap" description="获取预警级别字典数据">
        SELECT
            dict_label,
            dict_value
        FROM
            sys_dict_data
        WHERE
            dict_type = 'warning_level'
            AND status = '0'
        ORDER BY dict_sort ASC;
    </database>

    <database id="insertIndicatorLevel" resultMap="java.lang.Integer" description="新增指标预警规则">
        INSERT INTO indicator_level (
            ID,
            indicator_id,
            indicator_name,
            warning_level,
            `describe`,
            `sql`,
            supervisor_level,
            threshold,
            modifer,
            update_time
        ) VALUES (
            :ID,
            :indicator_id,
            :indicator_name,
            :warning_level,
            :describe,
            :sql,
            :supervisor_level,
            :threshold,
            :modifer,
            :updateTime
        );
    </database>

    <database id="deleteIndicatorLevel" resultMap="java.lang.Integer" description="删除指标预警规则">
        DELETE FROM indicator_level
        WHERE ID = :id;
    </database>

    <database id="updateIndicatorLevel" resultMap="java.lang.Integer" description="更新指标预警规则">
        UPDATE indicator_level
        SET
            indicator_name = :indicator_name,
            indicator_id = :indicator_id,
            warning_level = :warning_level,
            `describe` = :describe,
            `sql` = :sql,
            supervisor_level = :supervisor_level,
            threshold = :threshold,
            modifer = :modifer,
            update_time = :updateTime
        WHERE ID = :id;
    </database>

    <database id="queryIndicatorTypeById" resultMap="java.util.HashMap" description="根据指标ID查询指标类型">
        SELECT
            indicator_id,
            indicator_name,
            type
        FROM
            knowledge_indicators
        WHERE
            indicator_id = :indicator_id
    </database>

    <database id="queryIndicatorLevelById" resultMap="java.util.HashMap" description="根据ID查询单条指标预警规则">
        SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        WHERE
            il.ID = :id;
    </database>

    <database id="queryAllIndicatorLevels" resultMap="java.util.HashMap" description="查询所有指标预警规则">
        SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        ORDER BY il.ID;
    </database>
    <database id="queryAllIndicator">
        SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
    </database>
    <!--    <database id="queryAllIndicator">-->
    <!--        SELECT * from knowledge_indicators-->
    <!--    </database>-->


</sqlMap>
