<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="RiskLibrary" description="风险知识库管理表">
    <database id="queryBusiness" resultMap="java.util.HashMap" description="业务查询">
    		SELECT DISTINCT businessName business, businessId buss_id
            FROM business_manage
            ORDER BY buss_id;
    </database>
   <!-- 根据业务名称查询流程 -->
   <database id="queryFlowsByBusinessName" resultMap="java.util.HashMap" description="根据业务名称查询流程信息">
       SELECT
           ID as id,
           businessId,
           businessName,
           flows
       FROM
           business_manage
       WHERE
           1=1
           #if ($P.businessName and $P.businessName!='')
               AND businessName like CONCAT('%', :businessName, '%')
           #end
       ORDER BY orders ASC
    </database>
   <!-- 根据业务名称和流程名称查询子流程 -->
   <database id="querySubFlowsByBusinessAndFlow" resultMap="java.util.HashMap" description="根据业务名称和流程名称查询子流程">
       SELECT
           bm.businessId,
           bm.businessName,
           JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.flowId')) as flowId,
           JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.name')) as flowName,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.flowId')) as subFlowId,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.name')) as subFlowName,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.order')) as subFlowOrder
       FROM
           business_manage bm,
           JSON_TABLE(
               bm.flows,
               '$[*]' COLUMNS (
                   flow JSON PATH '$'
               )
           ) AS f,
           JSON_TABLE(
               JSON_EXTRACT(f.flow, '$.children'),
               '$[*]' COLUMNS (
                   subflow JSON PATH '$'
               )
           ) AS sf
       WHERE
           bm.businessName = :businessName
           AND JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.name')) = :flowName
           AND JSON_LENGTH(JSON_EXTRACT(f.flow, '$.children')) > 0
       ORDER BY
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.order'))
   </database>

    <!-- 更新知识库记录 -->
    <database id="updateLibrary" resultMap="java.lang.Integer" description="更新知识库记录">
            UPDATE knowledge_indicators
            SET indicator_name = :indicator_name,
                describe = :describe,
                business = :business,
                process = :process,
                sub_process = :sub_process,
                monitor_obj = :monitor_obj,
                risk_description = :risk_description,
                prevension_measure = :prevension_measure,
                refer_regu_describe = :refer_regu_describe,
                editor = :editor,
                edit_date = :edit_date,
                valid_status = :valid_status
            WHERE indicator_id = :indicator_id;
    </database>
    <database id="queryKnowledgeIndicators" resultMap="java.util.HashMap" description="按条件分页查询风险指标知识库">
        <!-- 获取并处理分页参数 -->
        #set($pageSize = $P.pageSize)
        #if(!$pageSize || $pageSize == '')
            #set($pageSize = 10)
        #end

        #set($offset = $P.offset)
        #if(!$offset || $offset == '')
            #set($offset = 0)
        #end

        SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
            #if ($P.businessName and $P.businessName!='')
                AND business = :businessName
            #end
            #if ($P.processName and $P.processName!='')
                AND process = :processName
            #end
            #if ($P.subProcessName and $P.subProcessName!='')
                AND sub_process = :subProcessName
            #end
            #if ($P.indicatorName and $P.indicatorName!='')
                AND indicator_name like CONCAT('%', :indicatorName, '%')
            #end
        ORDER BY indicator_id
        LIMIT ${pageSize} OFFSET ${offset}
    </database>

    <!-- 查询总记录数用于分页 -->
    <database id="countKnowledgeIndicators" resultMap="java.util.HashMap" description="查询符合条件的风险指标知识库总数">
        SELECT
            COUNT(1) as total
        FROM
            knowledge_indicators
        WHERE
            1=1
            #if ($P.businessName and $P.businessName!='')
                AND business = :businessName
            #end
            #if ($P.processName and $P.processName!='')
                AND process = :processName
            #end
            #if ($P.subProcessName and $P.subProcessName!='')
                AND sub_process = :subProcessName
            #end
            #if ($P.indicatorName and $P.indicatorName!='')
                AND indicator_name like CONCAT('%', :indicatorName, '%')
            #end
            #if ($P.valid_status)
                AND valid_status = :valid_status
            #end
    </database>

    !-- 删除指标记录 -->
    <database id="deleteKnowledgeIndicator" resultMap="java.lang.Integer" description="删除指标知识库记录">
        DELETE FROM knowledge_indicators
        WHERE indicator_id = :indicator_id
    </database>

    <!-- 更新指标记录 -->
    <database id="updateKnowledgeIndicator" resultMap="java.lang.Integer" description="更新指标知识库记录">
        UPDATE knowledge_indicators
        SET
            indicator_name = :indicator_name,
            `describe` = :describe,
            business = :business,
            process = :process,
            sub_process = :sub_process,
            monitor_obj = :monitor_obj,
            risk_description = :risk_description,
            prevension_measure = :prevension_measure,
            refer_regu_describe = :refer_regu_describe,
            editor = :editor,
            edit_date = CURRENT_TIMESTAMP,
            valid_status = :valid_status
        WHERE
            indicator_id = :indicator_id
    </database>

    <!-- 根据ID查询单条记录 -->
    <database id="getKnowledgeIndicatorById" resultMap="java.util.HashMap" description="根据ID查询指标记录">
        SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status
        FROM
            knowledge_indicators
        WHERE
            indicator_id = :indicator_id
    </database>
        <database id="getKnowledgeIndicatorAll" resultMap="java.util.HashMap" description="查询全部知识库">
            SELECT
                *
            FROM
                knowledge_indicators
        </database>

  <!-- 新增知识指标 -->
  <database id="insertKnowledgeIndicator" resultClass="int" description="新增知识指标">
      INSERT INTO knowledge_indicators (
          indicator_id,
          indicator_name,
          `describe`,
          business,
          process,
          sub_process,
          monitor_obj,
          risk_description,
          prevension_measure,
          refer_regu_describe,
          editor,
          type,
          edit_date,
          valid_status
      ) VALUES (
          :indicatorId,
          :indicatorName,
          :describe,
          :business,
          :process,
          :subProcess,
          :monitorObj,
          :riskDescription,
          :prevensionMeasure,
          :referReguDescribe,
          :editor,
          :type,
          NOW(),
          1
      )
  </database>
</sqlMap>