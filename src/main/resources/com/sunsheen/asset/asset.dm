<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="asset" description="资产管理">

    <!-- 按照资产单价分档统计数量和总值 -->
    <database id="countAssetsByPriceRange" description="按照资产单价分档统计数量和总值">
        <![CDATA[
        SELECT
            CASE
                WHEN unit_price >= 1000 AND unit_price < 100000 THEN '档次1 (1000-10万元)'
                WHEN unit_price >= 100000 AND unit_price < 500000 THEN '档次2 (10万-50万元)'
                WHEN unit_price >= 500000 AND unit_price < 1000000 THEN '档次3 (50万-100万元)'
                ELSE '档次4 (≥100万元)'
            END AS price_range,
            COUNT(*) AS asset_count,
            COALESCE(SUM(amount), 0) AS asset_total_value
        FROM
            asset_Registration
        WHERE unit_price >= 1000 -- 仅统计大于等于1000元的资产
          AND (asset_category_name IS NULL OR asset_category_name != '无形资产')
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        GROUP BY
            price_range
        ORDER BY
            CASE
                WHEN price_range = '档次1 (1000-10万元)' THEN 1
                WHEN price_range = '档次2 (10万-50万元)' THEN 2
                WHEN price_range = '档次3 (50万-100万元)' THEN 3
                ELSE 4
            END
        ]]>
    </database>

    <!-- 已使用年限分析 -->
    <database id="analyzeUsedYears" description="按已使用年限分析资产数量">
        <![CDATA[
        SELECT
            CASE
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 4 THEN '4年以内'
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) >= 5 AND TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 8 THEN '5-8年'
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) >= 9 AND TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 15 THEN '9-15年'
                ELSE '16年以上'
            END AS year_range,
            COUNT(*) AS asset_count
        FROM
            asset_Registration
        WHERE asset_entry_date IS NOT NULL
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        GROUP BY
            year_range
        ORDER BY
            CASE
                WHEN year_range = '4年以内' THEN 1
                WHEN year_range = '5-8年' THEN 2
                WHEN year_range = '9-15年' THEN 3
                ELSE 4
            END
        ]]>
    </database>

    <!-- 资产总额及当年新增额 -->
    <database id="getAssetTotalSummary" description="获取资产总额及当年新增额">
        <![CDATA[
        SELECT
            COALESCE(SUM(amount), 0) AS total_asset_amount,
            COALESCE(SUM(CASE
                WHEN YEAR(asset_entry_date) = YEAR(CURDATE()) THEN amount
                ELSE 0
            END), 0) AS current_year_new_amount
        FROM
            asset_Registration
        WHERE
            1 = 1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        ]]>
    </database>


    <database id="getDepartmentAssetUsageSummary" description="获取各使用部门的资产总值统计，可选按部门代码过滤">
        <![CDATA[
        SELECT
            ar.user_department_name AS department_name,
            ar.user_department_code AS department_code,
            COALESCE(SUM(ar.amount), 0) AS asset_total_value
        FROM
            asset_Registration ar
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.userDepartmentCode and $P.userDepartmentCode != '')
            AND ar.user_department_code = :userDepartmentCode
        #end
        GROUP BY
            ar.user_department_name, ar.user_department_code
        ORDER BY
            asset_total_value DESC
        ]]>
    </database>

    <database id="getAssetDetailsByDepartment" description="根据部门代码分页查询资产详细信息">
        <![CDATA[select asset_name,
            asset_code,
            asset_category_name,
            asset_entry_date,
            amount,
            user_department_name,
            status_name,
            model_brand,
            specification,
            storage_location
        FROM
            asset_Registration
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.userDepartmentCode and $P.userDepartmentCode != '')
            AND user_department_code = :userDepartmentCode
        #end
        ORDER BY
            asset_entry_date DESC
        LIMIT $P.dm_limit OFFSET $P.dm_offset
        ]]>
    </database>

    <database id="countAssetDetailsByDepartment" description="根据部门代码统计资产总数">
        <![CDATA[
        SELECT
            COUNT(id) AS total_assets
        FROM
            asset_Registration
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.userDepartmentCode and $P.userDepartmentCode != '')
            AND user_department_code = :userDepartmentCode
        #end
        ]]>
    </database>

    <database id="getAssetDetailsByPriceRange" description="根据单价区间分页查询资产详细信息，可选按使用部门名称过滤">
        <![CDATA[select asset_name,
            asset_code,
            asset_category_name,
            unit_price,
            model_brand,
            specification,
            user_department_name,
            status_name,
            asset_entry_date,
            storage_location,
            quantity
        FROM
            asset_Registration
        WHERE 1=1
        AND asset_category_name LIKE '%设备%'
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R1')
            AND unit_price >= 1000 AND unit_price < 100000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R2')
            AND unit_price >= 100000 AND unit_price < 500000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R3')
            AND unit_price >= 500000 AND unit_price < 1000000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R4')
            AND unit_price >= 1000000
        #else
            AND unit_price >= 1000 -- 确保至少筛选大于等于1000元的
        #end
        ORDER BY
            unit_price DESC, asset_entry_date DESC
        LIMIT $P.dm_limit OFFSET $P.dm_offset
        ]]>
    </database>

    <database id="countAssetDetailsByPriceRange" description="根据单价区间统计资产总数，可选按使用部门名称过滤">
        <![CDATA[
        SELECT
            COUNT(id) AS total_assets
        FROM
            asset_Registration
        WHERE 1=1
        AND asset_category_name LIKE '%设备%'
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R1')
            AND unit_price >= 1000 AND unit_price < 100000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R2')
            AND unit_price >= 100000 AND unit_price < 500000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R3')
            AND unit_price >= 500000 AND unit_price < 1000000
        #elseif ($P.priceRangeIdentifier and $P.priceRangeIdentifier == 'R4')
            AND unit_price >= 1000000
        #else
            AND unit_price >= 1000 -- 确保至少筛选大于等于1000元的
        #end
        ]]>
    </database>

    <!-- 年度仪器设备购置分析 -->
    <database id="getAnnualAssetPurchaseAnalysis" description="年度资产购置分析，获取每年新增资产总值和截止当年的累计资产总值，默认查询近五年">
        <![CDATA[
        SELECT
            entry_year AS year,
            COALESCE(SUM(ar.amount), 0) AS newAssetValue,
            (SELECT COALESCE(SUM(sub.amount), 0)
             FROM asset_Registration sub
             WHERE YEAR(sub.asset_entry_date) <= entry_year
             AND sub.asset_entry_date IS NOT NULL
             #if ($P.userDepartmentName and $P.userDepartmentName != '')
                 AND sub.user_department_name = :userDepartmentName
             #end
            ) AS totalAssetValue
        FROM (
            SELECT DISTINCT YEAR(asset_entry_date) AS entry_year
            FROM asset_Registration
            WHERE asset_entry_date IS NOT NULL
            #if ($P.userDepartmentName and $P.userDepartmentName != '')
                AND user_department_name = :userDepartmentName
            #end
            -- 默认查询近五年，如果有传参则使用传入的年份范围
            #if ($P.startYear and $P.startYear != '')
                AND YEAR(asset_entry_date) >= :startYear
            #else
                AND YEAR(asset_entry_date) >= (YEAR(CURDATE()) - 4)
            #end
            #if ($P.endYear and $P.endYear != '')
                AND YEAR(asset_entry_date) <= :endYear
            #else
                AND YEAR(asset_entry_date) <= YEAR(CURDATE())
            #end
        ) years
        LEFT JOIN asset_Registration ar ON YEAR(ar.asset_entry_date) = years.entry_year
        WHERE ar.asset_entry_date IS NOT NULL
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        GROUP BY entry_year
        ORDER BY entry_year
        ]]>
    </database>

    <database id="sumTeachingAndResearchFundingValue" description="按资金科目分组统计资产总值，取总金额前六个">
        <![CDATA[
        SELECT
            funding_subject_name,
            SUM(amount) AS total_value
        FROM
            asset_Registration
        WHERE
            funding_subject_name IS NOT NULL
            AND funding_subject_name != ''
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        GROUP BY
            funding_subject_name
        ORDER BY
            total_value DESC
        LIMIT 6
        ]]>
    </database>

    <!-- 月度资产购置分析 - 当年按月统计 -->
    <database id="getMonthlyAssetPurchasesCurrentYear" description="获取当年每月新增资产总值，用于月度购置分析">
        <![CDATA[
        SELECT
            MONTH(asset_entry_date) AS registration_month,
            COALESCE(SUM(amount), 0) AS monthly_new_assets_value
        FROM
            asset_Registration
        WHERE
            YEAR(asset_entry_date) = YEAR(CURDATE())
            AND asset_entry_date IS NOT NULL
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        GROUP BY
            MONTH(asset_entry_date)
        ORDER BY
            registration_month
        ]]>
    </database>

    <!-- 根据教育部分类名分组统计资产数量和总金额 -->
    <database id="getAssetStatisticsByCategoryName" description="根据教育部分类名分组展示资产数量和总金额">
        <![CDATA[
        SELECT
            category_name,
            category_code,
            COUNT(*) AS asset_count,
            COALESCE(SUM(quantity), 0) AS total_quantity,
            COALESCE(SUM(amount), 0) AS total_amount
        FROM
            asset_Registration
        WHERE
            category_name IS NOT NULL
            AND category_name != ''
            AND category_name <> ''
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.departmentName and $P.departmentName != '')
            AND user_department_name = :departmentName
        #end
        #if ($P.categoryName and $P.categoryName != '')
            AND category_name = :categoryName
        #end
        GROUP BY
            category_name, category_code
        ORDER BY
            total_amount DESC, asset_count DESC
        ]]>
    </database>

    <!-- 根据使用部门名称分组统计资产数量和总金额 -->
    <database id="getAssetStatisticsByDepartmentName" description="根据使用部门名称分组展示资产数量和总金额">
        <![CDATA[
        SELECT
            user_department_name AS department_name,
            user_department_code AS department_code,
            COUNT(*) AS asset_count,
            COALESCE(SUM(quantity), 0) AS total_quantity,
            COALESCE(SUM(amount), 0) AS total_amount
        FROM
            asset_Registration
        WHERE
            user_department_name IS NOT NULL
            AND user_department_name != ''
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.categoryName and $P.categoryName != '')
            AND category_name = :categoryName
        #end
        #if ($P.assetCategoryName and $P.assetCategoryName != '')
            AND asset_category_name = :assetCategoryName
        #end
        GROUP BY
            user_department_name, user_department_code
        ORDER BY
            total_amount DESC, asset_count DESC
        ]]>
    </database>

    <!-- 根据经费科目名称分页查询资产详细信息 -->
    <database id="getAssetDetailsByFundingSubject" description="根据经费科目名称分页查询资产详细信息">
        <![CDATA[
        SELECT
            asset_name,
            asset_code,
            asset_category_name,
            category_name,
            unit_price,
            amount,
            quantity,
            model_brand,
            specification,
            manufacturer,
            user_department_name,
            user_name,
            status_name,
            storage_location,
            asset_entry_date,
            funding_subject_name,
            funding_subject_code,
            asset_origin_name,
            purchase_form_name,
            supplier,
            contract_number,
            invoice_number
        FROM
            asset_Registration
        WHERE 1=1
        #if ($P.fundingSubjectName and $P.fundingSubjectName != '')
            AND funding_subject_name = :fundingSubjectName
        #end
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.assetCategoryName and $P.assetCategoryName != '')
            AND asset_category_name = :assetCategoryName
        #end
        ORDER BY
            asset_entry_date DESC, amount DESC
        LIMIT $P.dm_limit OFFSET $P.dm_offset
        ]]>
    </database>

    <!-- 根据经费科目名称统计资产总数 -->
    <database id="countAssetDetailsByFundingSubject" description="根据经费科目名称统计资产总数">
        <![CDATA[
        SELECT
            COUNT(id) AS total_assets
        FROM
            asset_Registration
        WHERE 1=1
        #if ($P.fundingSubjectName and $P.fundingSubjectName != '')
            AND funding_subject_name = :fundingSubjectName
        #end
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        #if ($P.assetCategoryName and $P.assetCategoryName != '')
            AND asset_category_name = :assetCategoryName
        #end
        ]]>
    </database>

    <!-- 根据经费科目名称获取资产统计摘要 -->
    <database id="getAssetSummaryByFundingSubject" description="根据经费科目名称获取资产统计摘要">
        <![CDATA[
        SELECT
            funding_subject_name,
            funding_subject_code,
            COUNT(*) AS asset_count,
            COALESCE(SUM(quantity), 0) AS total_quantity,
            COALESCE(SUM(amount), 0) AS total_amount,
            COALESCE(AVG(unit_price), 0) AS avg_unit_price,
            COALESCE(MAX(unit_price), 0) AS max_unit_price,
            COALESCE(MIN(unit_price), 0) AS min_unit_price
        FROM
            asset_Registration
        WHERE 1=1
        #if ($P.fundingSubjectName and $P.fundingSubjectName != '')
            AND funding_subject_name = :fundingSubjectName
        #end
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND user_department_name = :userDepartmentName
        #end
        GROUP BY
            funding_subject_name, funding_subject_code
        ]]>
    </database>

    <!-- 查询大型设备列表及使用率信息 -->
    <database id="getLargeEquipmentWithUsageRate" description="查询大型设备列表及使用率信息">
        <![CDATA[
        SELECT 
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date as purchase_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            ar.manufacturer,
            aur.statistics_year,
            aur.statistics_month,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.annual_shared_hours,
            aur.effective_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            aur.is_large_equipment,
            aur.is_precious_equipment,
            aur.is_shared_platform,
            aur.shared_platform_name,
            aur.annual_benefit_score,
            aur.benefit_evaluation_level,
            aur.teaching_service_hours,
            aur.research_service_hours,
            aur.external_service_hours,
            aur.training_service_hours,
            aur.internal_fee_income,
            aur.external_fee_income,
            aur.maintenance_cost,
            aur.operator_count,
            aur.certified_operator_count,
            aur.technician_count
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.assetName and $P.assetName != '')
            AND ar.asset_name LIKE CONCAT('%', :assetName, '%')
        #end
        #if ($P.usageLevel and $P.usageLevel != '')
            AND aur.usage_level = :usageLevel
        #end
        #if ($P.warningStatus and $P.warningStatus != '')
            AND aur.warning_status = :warningStatus
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        ORDER BY ar.amount DESC, aur.usage_rate DESC
        LIMIT $P.dm_limit OFFSET $P.dm_offset
        ]]>
    </database>

    <!-- 统计大型设备总数 -->
    <database id="countLargeEquipmentWithUsageRate" description="统计大型设备总数">
        <![CDATA[
        SELECT COUNT(*) AS total_count
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.assetName and $P.assetName != '')
            AND ar.asset_name LIKE CONCAT('%', :assetName, '%')
        #end
        #if ($P.usageLevel and $P.usageLevel != '')
            AND aur.usage_level = :usageLevel
        #end
        #if ($P.warningStatus and $P.warningStatus != '')
            AND aur.warning_status = :warningStatus
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        ]]>
    </database>

    <!-- 大型设备使用率统计概览 -->
    <database id="getLargeEquipmentUsageStatistics" description="大型设备使用率统计概览">
        <![CDATA[
        SELECT 
            COUNT(*) as total_equipment_count,
            COUNT(CASE WHEN aur.usage_rate IS NOT NULL THEN 1 END) as has_usage_data_count,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate,
            COALESCE(AVG(aur.effective_usage_rate), 0) as avg_effective_usage_rate,
            COALESCE(AVG(aur.shared_rate), 0) as avg_shared_rate,
            COUNT(CASE WHEN aur.usage_level = '优秀' THEN 1 END) as high_efficiency_count,
            COUNT(CASE WHEN aur.usage_level = '良好' THEN 1 END) as good_efficiency_count,
            COUNT(CASE WHEN aur.usage_level = '合格' THEN 1 END) as normal_efficiency_count,
            COUNT(CASE WHEN aur.usage_level = '不合格' THEN 1 END) as low_efficiency_count,
            COUNT(CASE WHEN aur.warning_status != '正常' THEN 1 END) as warning_count,
            COALESCE(SUM(ar.amount), 0) as total_equipment_value,
            COALESCE(SUM(aur.internal_fee_income + aur.external_fee_income), 0) as total_fee_income,
            COALESCE(SUM(aur.maintenance_cost), 0) as total_maintenance_cost
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        ]]>
    </database>

    <!-- 按部门统计大型设备使用率 -->
    <database id="getLargeEquipmentUsageByDepartment" description="按部门统计大型设备使用率">
        <![CDATA[
        SELECT
            ar.user_department_name as department_name,
            COUNT(*) as equipment_count,
            COUNT(CASE WHEN aur.usage_rate IS NOT NULL THEN 1 END) as has_usage_data_count,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate,
            COALESCE(AVG(aur.effective_usage_rate), 0) as avg_effective_usage_rate,
            COALESCE(SUM(ar.amount), 0) as total_equipment_value,
            COALESCE(SUM(aur.internal_fee_income + aur.external_fee_income), 0) as total_fee_income,
            COUNT(CASE WHEN aur.usage_level = '优秀' THEN 1 END) as high_efficiency_count,
            COUNT(CASE WHEN aur.usage_level = '不合格' THEN 1 END) as low_efficiency_count,
            COUNT(CASE WHEN aur.warning_status != '正常' THEN 1 END) as warning_count
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        GROUP BY ar.user_department_name
        ORDER BY avg_usage_rate DESC, total_equipment_value DESC
        ]]>
    </database>

    <!-- 查询单个资产的使用率详情 -->
    <database id="getAssetUsageRateDetail" description="查询单个资产的使用率详情">
        <![CDATA[
        SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date as purchase_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            ar.manufacturer,
            aur.statistics_year,
            aur.statistics_month,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.annual_shared_hours,
            aur.annual_fault_downtime_hours,
            aur.effective_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            aur.teaching_service_hours,
            aur.research_service_hours,
            aur.external_service_hours,
            aur.training_service_hours,
            aur.internal_fee_income,
            aur.external_fee_income,
            aur.maintenance_cost,
            aur.operator_count,
            aur.certified_operator_count,
            aur.technician_count,
            aur.is_large_equipment,
            aur.is_precious_equipment,
            aur.is_shared_platform,
            aur.shared_platform_name,
            aur.open_time_per_week,
            aur.annual_benefit_score,
            aur.benefit_evaluation_level,
            aur.created_time,
            aur.updated_time
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.asset_code = :assetCode
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        ORDER BY aur.statistics_year DESC, aur.statistics_month DESC
        LIMIT 1
        ]]>
    </database>

    <!-- 使用率等级分布统计（用于饼图） -->
    <database id="getUsageLevelDistribution" description="获取使用率等级分布统计">
        <![CDATA[
        SELECT 
            COALESCE(aur.usage_level, '未统计') as usage_level,
            COUNT(*) as equipment_count,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND (aur.statistics_year = :statisticsYear OR aur.statistics_year IS NULL)
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        GROUP BY aur.usage_level
        ORDER BY equipment_count DESC
        ]]>
    </database>

    <!-- 预警状态分布统计 -->
    <database id="getWarningStatusDistribution" description="获取预警状态分布统计">
        <![CDATA[
        SELECT 
            COALESCE(aur.warning_status, '未统计') as warning_status,
            COUNT(*) as equipment_count,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND (aur.statistics_year = :statisticsYear OR aur.statistics_year IS NULL)
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        GROUP BY aur.warning_status
        ORDER BY equipment_count DESC
        ]]>
    </database>

    <!-- 月度使用率趋势统计 -->
    <database id="getMonthlyUsageTrend" description="获取月度使用率趋势统计">
        <![CDATA[
        SELECT 
            aur.statistics_month as month,
            COUNT(*) as equipment_count,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate,
            COALESCE(AVG(aur.effective_usage_rate), 0) as avg_effective_usage_rate,
            COALESCE(AVG(aur.shared_rate), 0) as avg_shared_rate
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
            AND aur.statistics_month IS NOT NULL
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND aur.statistics_year = YEAR(CURDATE())
        #end
        GROUP BY aur.statistics_month
        ORDER BY aur.statistics_month
        ]]>
    </database>

    <!-- 设备价值分布统计 -->
    <database id="getEquipmentValueDistribution" description="获取设备价值分布统计">
        <![CDATA[
        SELECT 
            CASE
                WHEN ar.amount >= 100000 AND ar.amount < 500000 THEN '10-50万元'
                WHEN ar.amount >= 500000 AND ar.amount < 1000000 THEN '50-100万元'
                WHEN ar.amount >= 1000000 AND ar.amount < 5000000 THEN '100-500万元'
                ELSE '500万元以上'
            END AS value_range,
            COUNT(*) as equipment_count,
            COALESCE(SUM(ar.amount), 0) as total_value,
            COALESCE(AVG(aur.usage_rate), 0) as avg_usage_rate
        FROM asset_Registration ar
        LEFT JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        WHERE ar.unit_price >= 100000  -- 大型设备通常指单价10万元以上
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND (aur.statistics_year = :statisticsYear OR aur.statistics_year IS NULL)
        #else
            AND (aur.statistics_year = YEAR(CURDATE()) OR aur.statistics_year IS NULL)
        #end
        GROUP BY value_range
        ORDER BY 
            CASE
                WHEN value_range = '10-50万元' THEN 1
                WHEN value_range = '50-100万元' THEN 2
                WHEN value_range = '100-500万元' THEN 3
                ELSE 4
            END
        ]]>
    </database>

    <!-- 查询使用率超过指定阈值的资产 -->
    <database id="getAssetsExceedUsageThreshold" description="查询使用率超过指定阈值的资产">
        <![CDATA[
        SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.indicator_id,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
            #if ($P.indicatorId and $P.indicatorId != '')
                il.indicator_id = :indicatorId
            #else
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            #end
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.assetName and $P.assetName != '')
            AND ar.asset_name LIKE CONCAT('%', :assetName, '%')
        #end
        #if ($P.minThreshold and $P.minThreshold != '')
            AND il.threshold >= :minThreshold
        #end
        #if ($P.maxThreshold and $P.maxThreshold != '')
            AND il.threshold <= :maxThreshold
        #end
        #if ($P.warningLevel and $P.warningLevel != '')
            AND il.warning_level = :warningLevel
        #end
        #if ($P.supervisorLevel and $P.supervisorLevel != '')
            AND il.supervisor_level = :supervisorLevel
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND aur.statistics_year = YEAR(CURDATE())
        #end
        #if ($P.minUsageRate and $P.minUsageRate != '')
            AND aur.usage_rate >= :minUsageRate
        #end
        #if ($P.maxUsageRate and $P.maxUsageRate != '')
            AND aur.usage_rate <= :maxUsageRate
        #end
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
        #if ($P.dm_limit and $P.dm_limit != '')
            LIMIT :dm_limit OFFSET :dm_offset
        #end
        ]]>
    </database>

    <!-- 统计使用率超过阈值的资产数量 -->
    <database id="countAssetsExceedUsageThreshold" description="统计使用率超过阈值的资产数量">
        <![CDATA[
        SELECT
            COUNT(*) as total_count,
            AVG(aur.usage_rate) as avg_usage_rate,
            AVG(aur.usage_rate - il.threshold) as avg_exceed_amount,
            MAX(aur.usage_rate) as max_usage_rate,
            MIN(aur.usage_rate) as min_usage_rate
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
            #if ($P.indicatorId and $P.indicatorId != '')
                il.indicator_id = :indicatorId
            #else
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            #end
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
        #if ($P.userDepartmentName and $P.userDepartmentName != '')
            AND ar.user_department_name = :userDepartmentName
        #end
        #if ($P.assetName and $P.assetName != '')
            AND ar.asset_name LIKE CONCAT('%', :assetName, '%')
        #end
        #if ($P.minThreshold and $P.minThreshold != '')
            AND il.threshold >= :minThreshold
        #end
        #if ($P.maxThreshold and $P.maxThreshold != '')
            AND il.threshold <= :maxThreshold
        #end
        #if ($P.warningLevel and $P.warningLevel != '')
            AND il.warning_level = :warningLevel
        #end
        #if ($P.supervisorLevel and $P.supervisorLevel != '')
            AND il.supervisor_level = :supervisorLevel
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND aur.statistics_year = YEAR(CURDATE())
        #end
        #if ($P.minUsageRate and $P.minUsageRate != '')
            AND aur.usage_rate >= :minUsageRate
        #end
        #if ($P.maxUsageRate and $P.maxUsageRate != '')
            AND aur.usage_rate <= :maxUsageRate
        #end
        ]]>
    </database>

    <!-- 按部门统计使用率超过阈值的资产 -->
    <database id="getAssetsExceedThresholdByDepartment" description="按部门统计使用率超过阈值的资产">
        <![CDATA[
        SELECT
            ar.user_department_name,
            COUNT(*) as exceed_count,
            AVG(aur.usage_rate) as avg_usage_rate,
            AVG(aur.usage_rate - il.threshold) as avg_exceed_amount,
            SUM(ar.amount) as total_equipment_value,
            MAX(aur.usage_rate) as max_usage_rate,
            MIN(aur.usage_rate) as min_usage_rate
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
            #if ($P.indicatorId and $P.indicatorId != '')
                il.indicator_id = :indicatorId
            #else
                il.indicator_name = '资产使用率'
            #end
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
        #if ($P.warningLevel and $P.warningLevel != '')
            AND il.warning_level = :warningLevel
        #end
        #if ($P.supervisorLevel and $P.supervisorLevel != '')
            AND il.supervisor_level = :supervisorLevel
        #end
        #if ($P.statisticsYear and $P.statisticsYear != '')
            AND aur.statistics_year = :statisticsYear
        #else
            AND aur.statistics_year = YEAR(CURDATE())
        #end
        GROUP BY ar.user_department_name
        ORDER BY exceed_count DESC, avg_exceed_amount DESC
        ]]>
    </database>

</sqlMap>
