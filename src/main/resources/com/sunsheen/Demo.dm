<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="Demo" description="示例配置">
	<excel id="queryExcel" resultMap="java.util.HashMap" description="查询Excel文件">
    	select * from TC_SEX where code=:code
    </excel>
    
    <data id="upload" resultMap="java.util.HashMap" description="上传文件">
        <![CDATA[
			./aa
      	]]>
    </data>
    
    <git id="gitpath" type="commit" memo="第一次提交" resultMap="java.util.HashMap" repository="D:/workspace/testSpace/hearkenTest/.git" username="shujian" password="100200" description="git目录">
        <![CDATA[
			$P.fileurl
      	]]>
    </git>
    
    <git id="gitupdate" type="update" resultMap="java.util.HashMap" repository="D:/workspace/testSpace/hearkenTestUpdate/hearkenTest/.git" username="shujian" password="100200" description="git目录">
        <![CDATA[
			$P.fileurl
      	]]>
    </git>
    
    
    <database id="updatesf" resultMap="java.util.HashMap" description="查询语句简单配置1">
		update SF_CY_FEED set CREATE_DATE=:CREATE_DATE,
		REMARK=:REMARK where SF_CY_FEED_id = '965DF9C2285B11B2B47E8EDE749E4296'
    	
	</database>

	<database id="select" resultMap="java.util.HashMap" description="查询语句简单配置1">
		<cache>31</cache>
		select * from demo_amd where 1=1
    		</database>
	
	<database id="selectMap" resultMap="java.util.HashMap" description="查询语句简单配置1">
		<cache>31</cache>
		select * from demo_amd where id='100'
    		</database>
	
	<database id="select_errordatasource" resultMap="java.util.HashMap" description="查询语句简单配置1" persistenceUnit="datasource1">
		select * from demo_amd where 1=1
    		</database>
	
	<database id="select_sqlnull" resultMap="java.util.HashMap" description="查询语句简单配置1">
		
	</database>
	
	<database id="mysqlTable" resultMap="java.util.HashMap" description="查询语句简单配置1">
		SELECT table_name,table_comment FROM information_schema.tables 
		WHERE table_type = 'BASE TABLE' AND table_schema = :databaseName;
	</database>
	
	<database id="mysqlTable_column" resultMap="java.util.HashMap" description="查询语句简单配置1">
		SELECT column_name,data_type,column_comment FROM INFORMATION_SCHEMA.COLUMNS 
		WHERE TABLE_SCHEMA = :databaseName 
  		AND TABLE_NAME = :tablename;
	</database>
	
	<database id="tree" resultMap="java.util.HashMap" description="查询语句简单配置1">
		select * from demo_tree
	</database>

    <database id="insert" resultMap="java.lang.Integer" description="新增数据库表数据">
    	insert into demo_code(code,text) values(:code,:text)
    </database>

    <database id="update" resultMap="java.lang.Integer" description="更新数据库表数据">
        update demo_code set text=:text where code = :code;
    </database>

    <database id="delete" resultMap="java.lang.Integer" description="删除数据库表数据">
        delete from  demo_code where code=:code
    </database>
    
    <database id="insert_demo_vacate" resultMap="java.lang.Integer" description="请假管理新增" persistenceUnit="db_no_dia">
    	insert into demo_vacate(id,staffid,name,text,timebegin,timeend,apply_state,runid,notes,counts) 
    	values('#idString()',:staffid,:name,:text,:timebegin,:timeend,:apply_state,:runid,:notes,:counts)
    </database>
    
    <database id="update_demo_vacate" resultMap="java.lang.Integer" description="请假管理修改">
        update demo_vacate set id=:id
              	              	      	      	              	        where id=:id
    </database>
    
    <chart id="stackcolumn" description="测试数据">
	<![CDATA[
		{
		  xAxis: {
		    type: $Q.list("Demo.select_demo_char_income").getJson(),
		    boundaryGap: $Q.list("Demo.select_demo_char_income").getMap(1).getValue("month"),
		    data: $Q.list("Demo.select_demo_char_income").getValue("month")
		  },
		  yAxis: {
		    type: 'value'
		  },
		  series: [
		    {
		      data: $Q.list("Demo.select_demo_char_income").getValue("income"),
		      type: 'line',
		      areaStyle: {}
		    }
		  ]
		}
	]]>
	</chart>
    
    <database id="update_demo_vacate_runid" resultMap="java.lang.Integer" description="请假管理流程审批">
        update demo_vacate set apply_state = :apply_state
        where id=:id
    </database>
    
    <database id="select_demo_vacate" resultMap="java.util.HashMap" description="请假管理查询">
		select a.*,b.text as staffname from demo_vacate a left join demo_code b
		on a.staffid = b.code where 1=1
		                                                	</database>
	
	<database id="select_demo_code" resultMap="java.util.HashMap" description="下拉框实例查询">
		select * from demo_code order by sequence
	</database>
	
	<database id="select_demo_vacate1" resultMap="java.util.HashMap" description="请假管理查询">
		select * from demo_vacate where 1=1
		
	</database>
	
	<database id="delete_demo_vacate" resultMap="java.lang.Integer" description="请假管理删除">
        delete from  demo_vacate where id=:id
    </database>
    
    <database id="select_demo_vacate_id" resultMap="java.util.HashMap" description="查询语句">
		select * from demo_vacate where id = :id
	</database>
	
	<database id="insert_master" resultMap="java.util.HashMap" description="主表插入">
		insert into tos_test_master (id,useryi) values (:id,:useryi)
	</database>
	
	<database id="insert_slave" resultMap="java.util.HashMap" description="从表插入">
		insert into tos_test_slave (id,master_id,mnb) values (:id,:master_id,:mnb)
	</database>
	
	<database id="del_master" resultMap="java.util.HashMap" description="主表删除">
		delete from tos_test_master where id = :id
	</database>
	
	<database id="del_slave" resultMap="java.util.HashMap" description="从表删除">
		delete from tos_test_slave where id = :id
	</database>
	
	<database id="select_demo_char_income" resultMap="java.util.HashMap" description="查询语句">
		select * from demo_char_income where 1=1
			</database>
	
	<database id="select_demo_char_pie" resultMap="java.util.HashMap" description="饼状图查询">
		<![CDATA[
		select * from demo_chart_pie where 1=1
		      	      	]]>
	</database>
	
	<database id="select_requisition_form" resultMap="java.util.HashMap" description="查询语句">
		select * from tb_requisition_form
	</database>
	
	<database id="select_requisition_form_detail" resultMap="java.util.HashMap" description="查询语句">
		select * from tb_requisition_form_detail where tb_requisition_form_id=:tb_requisition_form_id
	</database>
	
	<database id="select_demo_char_one" resultMap="java.util.HashMap" description="多维折线图">
		<![CDATA[
		select * from demo_chart_one where 1=1
		      	      	]]>
	</database>
	
	<database id="select_demo_char_income" resultMap="java.util.HashMap" description="折线图">
		<![CDATA[
		select * from demo_char_income where 1=1
		      	]]>
	</database>
	
	<database id="select_demo_char_mscolumn" resultMap="java.util.HashMap" description="多维柱状图">
		<![CDATA[
		select * from demo_chart_mscolumn where 1=1
		      	]]>
	</database>
	
	<database id="select_demo_char_msbar" resultMap="java.util.HashMap" description="多维条形图">
		<![CDATA[
		select * from demo_chart_msbar where 1=1
		      	]]>
	</database>
	
	<database id="tree_insert" resultMap="java.lang.Integer" description="新增数据库表数据">
    	insert into demo_tree(id,belongto,grade,iconcls,memo,sequence,text,adddate,checkbox) 
    	values(:id,:belongto,:grade,:iconcls,:memo,:sequence,:text,:adddate,:checkbox)
    </database>

    <database id="tree_update" resultMap="java.lang.Integer" description="更新数据库表数据">
        update demo_tree set belongto=:belongto,
        grade=:grade,iconcls=:iconcls,memo=:memo,sequence=:sequence,text=:text,
        adddate=:adddate,checkbox=:checkbox
        where id=:id
    </database>

    <database id="tree_delete" resultMap="java.lang.Integer" description="删除数据库表数据">
        delete from  demo_tree where id=:id
    </database>
    
    <data id="testQ" resultMap="java.lang.Integer" description="删除数据库表数据">
        <![CDATA[
			{
				"max_value":"$Q.list("Demo.select_demo_vacate").max(["timebegin"])"
			}
      	]]>
    </data>
    
    <url id="testurlList" method="get" interface="http://localhost:9385/hearkenDemo/data/QueryDataForList.svt?dataId=$P.uu" resultMap="java.util.List" description="访问第三方接口">
        <return-scalar column="id" filed="id1"/>
       
    </url>
    
    <url id="testurlMap" method="get" interface="http://localhost:9385/hearkenDemo/data/GridData.svt?dataId=Demo.select_demo_vacate" resultMap="java.util.HashMap" description="访问第三方接口">
        <return-scalar column="id" filed="id1"/>
       
    </url>
    
    <url id="testurlpost" method="post" interface="http://localhost:9385/hearkenDemo/json/SaveData.svt" description="访问第三方接口">
        <![CDATA[
			{
				   "dataId":"Demo.insert_demo_vacate",
				   "data":{
				   			"name":"张三",
				   			"age":12
				   }
				   
				}
      	]]>
    </url>
    
    
    <url id="deepseekTest" method="post" resultMap="reactor.core.publisher.Flux"  interface="http://***********:11434/api/generate" description="访问deepseek">
        <![CDATA[
			{
			    "model": "deepseek-r1:14B",
			    "prompt": "$P.question",
			    "stream": true
			}
      	]]>
    </url>
    
    <data id="testurlMapdata" description="删除数据库表数据">
        <![CDATA[
        $Q.map("Demo.testurlMap").getList("DS")
			
      	]]>
    </data>
    
    
    <data id="testP" resultMap="java.lang.Integer" description="删除数据库表数据">
        <![CDATA[
			{
				"config":"$C.getProperty("application.registry.address")",
				"group_max":$Q.list("Demo.select_demo_vacate").group("max",["staffid"],["name"]),
				"group_min":$Q.list("Demo.select_demo_vacate").group("min",["staffid"],["name"]),
				"group_sum":$Q.list("Demo.select_demo_vacate").group("sum",["staffid","id"],["name"]).order(["staffid desc","name asc"]).get(0).get("staffid"),
				"order":$Q.list("Demo.select_demo_vacate").order(["name desc"]),
				"value":$Q.list("Demo.select_demo_vacate").getValue("staffid"),
				"json":$Q.list("Demo.select_demo_vacate").getJson(),
				"String":$Q.list("Demo.select_demo_vacate").getString(),
				"max":$Q.list("Demo.select_demo_vacate",{"name":"张三"}).max("staffid"),
				"min":$Q.list("Demo.select_demo_vacate",{"name":"张三"}).min("staffid"),
				"sum":$Q.list("Demo.select_demo_vacate",{"name":"张三"}).sum(["staffid","id"])
			}
      	]]>
    </data>
    
    <data id="testU" resultMap="java.lang.Integer" description="删除数据库表数据">
        <![CDATA[
			{
				"yyy":$C.get("application.registry.address")","#C("application.registry.address");
				"test":$Q.list("Demo.select_demo_vacate",{"name":"张三","interfaceId":"getDataByRegionByTimeRange","dataCode":"SURF_CHN_MUL_HOR"}).group("max",["staffid"],["name"]),
			}
      	]]>
    </data>
    
    <database id="select_type" resultMap="java.util.HashMap" description="查询语句">
    	<return-scalar column="griddata" filed="griddata" type="org.postgresql.jdbc.PgArray"/>
    	<return-scalar column="id" filed="id" type="java.lang.String"/>
		select id,griddata from "grid_data" order by id
	</database>
	
	<database id="querySaveDBFileByFileName" resultMap="java.util.HashMap" description="通过文件名查询入库文件">
		SELECT * FROM "grid_file_data" WHERE "fileName" = 'Z_NWGD_C_BCCD_20240514005602_P_RFFC_SPHD-TMP_20240514010000_02401.GRB2';
	</database>
	
	<database id="oracleTest" resultMap="java.util.HashMap" description="测试分页">
		SELECT * FROM ENTST_MEASUR
	</database>
	
	<database id="dmTest" resultMap="java.util.HashMap" description="测试分页">
		SELECT * FROM SF_CY_SLOT
	</database>
	
	<filelist id="queryNas" description="最顶层目录暂时不能为正则,获取文件名称包含RUC与FOG的文件列表">
		<![CDATA[
			H:/DATA/NAFP/NWFD/SCMOC/(BABJ|BCCD)/$P.year/$P.time1/.*(FOG|HZ)_$C.hourRange($P.time2,"-3","5")_\d{5}\.GRB2$
		]]>
		
	</filelist>
	
	<filelist id="queryall" description="最顶层目录暂时不能为正则,获取目录下所有的文件列表">
		<![CDATA[
			H:/DATA/NAFP/NWFD/SCMOC/(BABJ|BCCD)/$P.year/$P.time1/.*
		]]>
		
	</filelist>
	
	<filelist id="queryr" recursion="true" description="最顶层目录暂时不能为正则,递归获取文件夹下的文件">
		<cache>31</cache>
		<![CDATA[
			C:/Users/<USER>/Desktop/ceshi/.*
		]]>
		
	</filelist>
	
	
	
	<data id="test_data" description="原样返回数据">
		<![CDATA[
			根据文字模版生成字符串，自定义的数据$P.test的数据
		]]>
	</data>
	
	<data id="list" description="执行其他dataId">
		$Q.list("Demo.select")
	</data>
	
	<data id="max" description="取code值最大的一条数据">
		$Q.list("Demo.select").max(["code"])
	</data>
	
	<data id="cma_stat4"  description="简单统计语句">
		<cache>32</cache>
		<![CDATA[
            $Q.list("Demo.select").count(["* as counts"])
      	]]>
	</data>
	
	<data id="count" description="统计数量量">
		$Q.list("Demo.select").count(["* as counts"])
	</data>
	
	<data id="group" description="按照text与memo字段分组，统计数据量">
		$Q.list("Demo.select").group("count",["* as counts"],["text","memo"])
	</data>
	
	<data id="sum" description="字段求和">
		$Q.list("Demo.select").sum(["sequence"])
	</data>
	
	<data id="avg" description="求平均值">
		$Q.list("Demo.select").avg(["sequence"])
	</data>
	<data id="min" description="取字段sequence值最小的一条数据">
		$Q.list("Demo.select").min(["sequence"])
	</data>
	<data id="retain" description="保留2位小数，四舍五入">
		$Q.list("Demo.select").avg(["sequence"]).retain(2,["sequence"])
	</data>
	
	<data id="order" description="按照字段排序">
		$Q.list("Demo.select").order(["id desc,sequence asc"])
	</data>
	
	<data id="top" description="获取前50%的数据">
		<cache>30</cache>
		$Q.list("Demo.select").order(["id desc,sequence asc"]).top(["50%"])
	</data>
	
	<data id="extend" description="自定义处理方法">
		$Q.list("Demo.select").extend("TestCustom",{"dataId":"Demo.select1"})
	</data>
	
	<elasticsearch id="esSelect" method="post" interface="http://localhost:9200/_sql?format=json" resultMap="java.util.HashMap" description="es查询数据测试" persistenceUnit="kib">
		<return-scalar column="id" filed="id1"/>
		<![CDATA[
			SELECT * FROM $P.indexName ORDER BY age ASC
		]]>
	</elasticsearch>
	
	<elasticsearch id="esSelectByDsl" method="post" interface="http://localhost:9200/my_index/_search" resultMap="java.util.HashMap" description="es通过dsl查询">
		<return-scalar column="id" filed="id1"/>
		<![CDATA[
			{  
			  "query": {  
	        	"match": {  
			      "age": "$P.age"  
			    }
			  }  
			}
		]]>
	</elasticsearch>
	
	<elasticsearch id="esSelectMany" method="post" interface="http://localhost:9200/my_index/_search" resultMap="java.util.HashMap" description="es通过dsl多字段查询">
		<return-scalar column="id" filed="id1"/>
		<![CDATA[
			{  
			  "query": {  
			    "bool": {  
			      "must": [  
			        {  
			          "match": {  
			            "age": "$P.age"  
			          }  
			        } 
			        			      ]  
			    }  
			  }  
			}
		]]>
	</elasticsearch>
	
	<elasticsearch id="esInsert" method="post" interface="http://localhost:9200/my_index/_doc/$P.id" resultMap="java.util.HashMap" description="es插入数据测试">
		<![CDATA[
			{
			    "id":$P.id,
			    "name": "$P.name",
			    "age": "$P.age",
			    "sequence": $P.sequence
			}
		]]>
	</elasticsearch>
	
	<elasticsearch id="esUpdate" method="post" interface="http://localhost:9200/my_index/_update/3" resultMap="java.util.HashMap" description="es修改数据测试">
		<![CDATA[
			{  
			    "doc": {  
			        "name": "$P.name" 
			    }  
			}
		]]>
	</elasticsearch>
	
	<elasticsearch id="esDelete" method="delete" interface="http://localhost:9200/my_index/_doc/4" resultMap="java.util.HashMap" description="es根据id删除数据测试">
		
	</elasticsearch>
	
	<elasticsearch id="esDeleteByQuery" method="post" interface="http://localhost:9200/my_index/_delete_by_query" resultMap="java.util.HashMap" description="es根据某个字段的值删除数据">
		{  
		  "query": {  
		    "match": {  
		      "name": "$P.name"
		    }  
		  }  
		}
	</elasticsearch>
	
	<elasticsearch id="esDeleteMany" method="post" interface="https://escq.it.sunsheen.cn/my_index/_delete_by_query" resultMap="java.util.HashMap" description="es根据多个字段的值删除数据">
		{  
		  "query": {  
		    "bool": {  
		      "must": [  
		        {  
		          "match": {  
		            "name": "$P.name"
		          }  
		        },  
		        {  
		          "match": {  
		            "age":"$P.age"
		          }  
		        }  
		      ]  
		    }  
		  }  
		}
	</elasticsearch>
	
	<elasticsearch id="esCreIndex" method="put" interface="https://escq.it.sunsheen.cn/$P.indexName" resultMap="java.util.HashMap" description="es新建索引">
		<![CDATA[
			{  
			    "settings": {  
			        "number_of_shards": 3,  
			        "number_of_replicas": 2  
			    },  
			    "mappings": {  
			        "properties": {  
			            "id": { "type": "text" },
			            "name": { "type": "text" },  
			            "age": { "type": "keyword" },  
			            "sequence": { "type": "integer" }   
			        }  
			    }  
			}
		]]>
	</elasticsearch>
	
	<elasticsearch id="esDelIndex" method="delete" interface="http://localhost:9200/$P.indexName" resultMap="java.util.HashMap" description="es删除索引">
		
	</elasticsearch>
</sqlMap>