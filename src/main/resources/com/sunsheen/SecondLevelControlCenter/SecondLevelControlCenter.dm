<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="SecondLevelControlCenter" description="二阶指标相关的">
   <database id="getIndicatorSummary" resultMap="java.util.HashMap" description="查询预警的数量和占比">
    WITH counts AS (
        SELECT
            (SELECT COUNT(*) FROM warning_list WHERE warning_level = 0) AS healthy_count,
            (SELECT COUNT(*) FROM warning_2ed_level WHERE warning_level = 1) AS yellow_count,
            (SELECT COUNT(*) FROM warning_2ed_level WHERE warning_level = 2) AS red_count
    )
    SELECT
        healthy_count AS healthyCount,
        yellow_count AS yellowWarningCount,
        red_count AS redWarningCount,
        (healthy_count + yellow_count + red_count) AS totalCount,
        CASE
            WHEN (healthy_count + yellow_count + red_count) = 0 THEN 0
            ELSE ROUND(healthy_count * 100.0 / (healthy_count + yellow_count + red_count), 2)
        END AS healthPercent,
        CASE
            WHEN (healthy_count + yellow_count + red_count) = 0 THEN 0
            ELSE ROUND(yellow_count * 100.0 / (healthy_count + yellow_count + red_count), 2)
        END AS yellowPercentage,
        CASE
            WHEN (healthy_count + yellow_count + red_count) = 0 THEN 0
            ELSE ROUND(red_count * 100.0 / (healthy_count + yellow_count + red_count), 2)
        END AS redPercentage
    FROM counts
</database>
    <database id="selectList" resultMap="java.util.HashMap" description="查询二阶列表">
        <!-- 获取并处理分页参数 -->
        #set($pageSize = $P.pageSize)
        #if(!$pageSize || $pageSize == '')
            #set($pageSize = 10)
        #end

        #set($offset = $P.offset)
        #if(!$offset || $offset == '')
            #set($offset = 0)
        #end
        
        select wls.*,il.indicator_name indicator_name from warning_2ed_level wls
        left join indicator_level il on wls.indicator_id=il.indicator_id
        LIMIT ${pageSize} OFFSET ${offset}
    </database>

    <database id="selectListCount" resultType="int" description="查询二阶列表总记录数">
         select count(*) from warning_2ed_level
    </database>

</sqlMap>