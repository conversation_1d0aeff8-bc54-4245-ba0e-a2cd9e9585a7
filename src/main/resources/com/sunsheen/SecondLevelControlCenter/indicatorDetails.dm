<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="indicatorDetails" description="指标详情">

<database id="getWarningList" resultMap="java.util.HashMap" description="查询预警详情">
  <![CDATA[
    select
      w.warning_id,
      w.bussiness_id,
      w.indicator_id,
      w.indicator_name,
      w.warning_level,
      w.bussiness,
      w.process,
      w.sub_process,
      w.dept_name,
      w.isClosed,
      a.business_type,
      a.audit_status,
      a.using_department,
      a.asset_name,
      a.initiator,
      a.total_quantity,
      a.total_amount,
      a.task_executor,
      a.stay_hours,
      k.risk_description,
      k.refer_regu_describe
    from warning_2ed_level w
    left join assets_workflow_data a on w.bussiness_id = a.document_number
    left join knowledge_indicators k on w.indicator_id = k.indicator_id
    where 1=1
    #if($P.indicator_id)
      and w.indicator_id = :indicator_id
    #end
    #if($P.start_time)
      and w.start_time = :start_time
    #end
    #if($P.end_time)
      and w.end_time = :end_time
    #end
    order by w.update_time desc
    limit 1
  ]]>
</database>

<database id="getWarning_level" resultMap="java.util.HashMap" description="查询预警级别">
  <![CDATA[
    select
      indicator_id,
      indicator_name,
      supervisor_level,
      warning_level,
      `describe`,
      threshold
    from indicator_level
    where indicator_id = :indicator_id
      and supervisor_level = 2
  ]]>
</database>

<database id="getTrace" resultMap="java.util.HashMap" description="查询追踪记录">
  <![CDATA[
    select
      original_level,
      new_level,
      DATE_FORMAT(change_time, '%Y-%m-%d %H:%i:%s') as change_time,
      change_type,
      change_reason
    from warning_trace
    where warning_id = :warning_id
    order by change_time desc
  ]]>
</database>

</sqlMap>