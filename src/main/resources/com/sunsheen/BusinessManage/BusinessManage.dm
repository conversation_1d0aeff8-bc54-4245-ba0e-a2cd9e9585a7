<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="BusinessManage" description="业务流程管理">

   <database id="insertBusiness" resultMap="java.lang.Integer" description="新增业务流程">
    	INSERT INTO business_manage
        (orders,businessId, businessName)
        VALUES(:orders,:businessId, :businessName);
    </database>


<database id = "updateBusiness" resultMap="java.lang.Integer" description="修改业务流程信息">
    UPDATE business_manage
    SET businessName=:businessName,flows=:flows
    WHERE businessId=:businessId;
    </database>

  <database id="selectBusiness" resultMap="java.lang.Integer" description="查询用户">
    SELECT *
    FROM business_manage et
    WHERE 1=1
     #if ($P.businessName and $P.businessName!='')
              and et.businessName like :businessName
            #end
    </database>


      <database id="selectCount" resultMap="java.lang.Integer" description="查询数据条数">
        SELECT count(*) as count
        FROM business_manage et
        WHERE 1=1
         #if ($P.businessName and $P.businessName!='')
                  and et.businessName like :businessName
                #end
        </database>

        <database id="deleteBussiness" resultMap="java.lang.Integer" description="删除业务信息">
                    delete from
                        business_manage
                    where businessId = :businessId
            </database>

</sqlMap>