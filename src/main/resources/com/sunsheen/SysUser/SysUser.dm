<?xml version="1.0" encoding="UTF-8"?>

<sqlMap namespace="SysUser" description="角色管理">

    <database id="selectUser" resultMap="java.lang.Integer" description="查询所有用户">
               SELECT nickName, userName, dept, sex, race, roleName, status
               FROM sys_user
               where 1=1
               #if ($P.nickName and $P.nickName != '')
                   and nickName like CONCAT('%',:nickName,'%')
               #end
               #if ($P.userName and $P.userName != '')
                  and userName like CONCAT('%',:userName,'%')
               #end
               #if ($P.dept and $P.dept != '')
                  and dept like CONCAT('%',:dept,'%')
               #end
            </database>


    <database id="selectUserCount" resultMap="java.lang.Integer" description="查询用户数据条数">
                SELECT COUNT(*) as count
              FROM sys_user
              where 1=1
              #if ($P.nickName and $P.nickName != '')
                  and nickName like CONCAT('%',:nickName,'%')
              #end
              #if ($P.userName and $P.userName != '')
                 and userName like CONCAT('%',:userName,'%')
              #end
              #if ($P.dept and $P.dept != '')
                 and dept like CONCAT('%',:dept,'%')
              #end
            </database>

    <database id="selectByUser" resultMap="java.util.Map" description="通过用户名查询用户">
               SELECT nickName, userName, dept, sex, race, roleName, status, deptId
               FROM sys_user
               where 1=1
               #if ($P.nickName and $P.nickName != '')
                   and nickName like CONCAT('%',:nickName,'%')
               #end
               #if ($P.userName and $P.userName != '')
                  and userName like CONCAT('%',:userName,'%')
               #end
               #if ($P.dept and $P.dept != '')
                  and dept like CONCAT('%',:dept,'%')
               #end
                </database>

    <database id="selectByUserCount" resultMap="java.lang.Integer" description="通过用户名查询用户条数">
               SELECT COUNT(*) as count
               FROM sys_user
               where 1=1
               #if ($P.nickName and $P.nickName != '')
                   and nickName like CONCAT('%',:nickName,'%')
               #end
               #if ($P.userName and $P.userName != '')
                  and userName like CONCAT('%',:userName,'%')
               #end
               #if ($P.dept and $P.dept != '')
                  and dept like CONCAT('%',:dept,'%')
               #end
               </database>

      <database id="selectUserByNickName" resultMap="java.util.Map" description="通过用户名查询用户">
                    SELECT nickName, userName, dept, sex, race, roleName, status, deptId
                    FROM sys_user
                    where nickName = :nickName
    </database>

    <database id="updateRole" resultMap="java.util.Map" description="修改角色数据到角色表">
        UPDATE sys_user
        SET roleName=:roleName
        WHERE nickName=:nickName;
    </database>

    <database id="selectDeptIdByUser" resultMap="java.util.Map" description="通过用户名查询部门id和名称">
        SELECT deptId,dept
        FROM sys_user
        where nickName = :nickName
    </database>
    <database id="selectNickNameAndUserNameByDeptId" resultMap="java.util.Map" description="通过部门id查询用户名和姓名">
        SELECT nickName,userName
        FROM sys_user
        where deptId = :deptId
    </database>

    <database id="selectCountNickNameAndUserNameByDeptId" resultMap="java.util.Map"
              description="通过部门id查询用户名和姓名">
        SELECT COUNT(*)
        FROM sys_user
        where deptId = :deptId
    </database>
    <database id="selectAllNickNameAndUserName" resultMap="java.util.Map" description="查询所有部门的用户名和姓名">
        SELECT nickName,userName
        FROM sys_user
    </database>

    <database id="selectDepartmentGroupByDept" resultMap="java.lang.Map" description="查询部门分组">
        SELECT dept,deptId
        FROM sys_user
        GROUP BY dept,deptId
    </database>

    <database id="selectRoleNameBynickName" resultMap="java.util.Map" description="通过用户名查询角色名称">
        SELECT roleName
        FROM sys_user
        WHERE nickName = :nickName
    </database>

    <database id="addUser" resultMap="java.lang.Integer" description="新增用户">
        INSERT INTO sys_user
        (dept, deptId, nickName, userName
        #if ($P.sex and $P.sex != '')
            , sex
        #end
        #if ($P.race and $P.race != '')
            , race
        #end
        #if ($P.roleName and $P.roleName != '')
            , roleName
        #end
            , status
        )
        VALUES
        (:dept, :deptId, :nickName, :userName
        #if ($P.sex and $P.sex != '')
            , :sex
        #end
        #if ($P.race and $P.race != '')
            , :race
        #end
        #if ($P.roleName and $P.roleName != '')
            , :roleName
        #end
            , :status
        )
    </database>

    <database id="selectDeptList" resultMap="java.util.Map" description="查询部门列表">
        SELECT * FROM dict_department
    </database>

    <database id="deleteUser" resultMap="java.lang.Integer" description="删除用户">
        delete from
            sys_user
        where nickName = :nickName
    </database>

    <database id="selectDeptRoleNameByUserId" resultMap="java.util.Map" description="根据用户id查询">
        SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
    </database>

</sqlMap>