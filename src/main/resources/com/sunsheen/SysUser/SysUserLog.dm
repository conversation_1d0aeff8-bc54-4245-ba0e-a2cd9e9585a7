<?xml version="1.0" encoding="UTF-8"?>

<sqlMap namespace="SysUserLog" description="用户登录日志管理">

    <database id="insertUserLog" resultMap="java.lang.Integer" description="插入用户登录登出日志">
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    </database>

    <database id="selectUserLog" resultMap="java.lang.Integer" description="查询所有用户登录登出日志">
                   SELECT logId,nickName, userName, type, time, dept, ip
                   FROM sys_user_log
                   where 1=1
                   #if ($P.nickName and $P.nickName != '')
                       and nickName like CONCAT('%',:nickName,'%')
                   #end
                   #if ($P.userName and $P.userName != '')
                      and userName like CONCAT('%',:userName,'%')
                   #end
                   #if ($P.dept and $P.dept != '')
                      and dept like CONCAT('%',:dept,'%')
                   #end
                   #if ($P.type and $P.type != '')
                       and type like CONCAT('%',:type,'%')
                   #end
                   ORDER BY time DESC
                </database>


        <database id="selectUserLogCount" resultMap="java.lang.Integer" description="查询用户登录登出数据条数">
                    SELECT COUNT(*) as count
                  FROM sys_user_log
                  where 1=1
                  #if ($P.nickName and $P.nickName != '')
                      and nickName like CONCAT('%',:nickName,'%')
                  #end
                  #if ($P.userName and $P.userName != '')
                     and userName like CONCAT('%',:userName,'%')
                  #end
                  #if ($P.dept and $P.dept != '')
                     and dept like CONCAT('%',:dept,'%')
                  #end
                  #if ($P.type and $P.type != '')
                      and type like CONCAT('%',:type,'%')
                  #end
                </database>
</sqlMap>