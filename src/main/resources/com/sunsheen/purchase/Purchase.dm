<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="Purchase" description="采购监管大屏">

    <!-- 1. 采购总额统计 - 按月分组对比 -->
    <database id="purchase_total_amount" resultMap="java.util.HashMap" description="采购总额：按月分组，今年vs去年同月对比">
        <![CDATA[
        SELECT
            month_num,
            CASE month_num
                WHEN 1 THEN '1月'
                WHEN 2 THEN '2月'
                WHEN 3 THEN '3月'
                WHEN 4 THEN '4月'
                WHEN 5 THEN '5月'
                WHEN 6 THEN '6月'
                WHEN 7 THEN '7月'
                WHEN 8 THEN '8月'
                WHEN 9 THEN '9月'
                WHEN 10 THEN '10月'
                WHEN 11 THEN '11月'
                WHEN 12 THEN '12月'
            END as month_name,

            -- 今年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pi.budget ELSE 0 END), 0) as current_year_budget,
            -- 去年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pi.budget ELSE 0 END), 0) as last_year_budget,

            -- 今年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.total_price ELSE 0 END), 0) as current_year_goods_amount,
            -- 去年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.total_price ELSE 0 END), 0) as last_year_goods_amount,

            -- 今年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as current_year_actual,
            -- 去年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as last_year_actual,

            -- 今年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_project_count,
            -- 去年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN 1 ELSE 0 END) as last_year_project_count,

            -- 今年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.pg_id ELSE NULL END) as current_year_goods_count,
            -- 去年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.pg_id ELSE NULL END) as last_year_goods_count,

            -- 今年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pa.pa_id ELSE NULL END) as current_year_attachment_count,
            -- 去年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pa.pa_id ELSE NULL END) as last_year_attachment_count

        FROM (
            SELECT 1 as month_num UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION
            SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
        ) months
        LEFT JOIN pms_project_info pi ON MONTH(pi.apply_time) = months.month_num
            AND YEAR(pi.apply_time) IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
            AND pi.is_delete = 0
        LEFT JOIN pms_project_goods pg ON pi.AppliProCode = pg.AppliProCode
        LEFT JOIN pms_project_attachment pa ON pi.AppliProCode = pa.AppliProCode
        GROUP BY month_num, month_name
        ORDER BY month_num
        ]]>
    </database>

    <!-- 1.1 采购总额统计（含计算字段） -->
    <data id="purchase_total_amount_with_calculation" description="采购总额统计（含同比环比增长率计算）">
        <![CDATA[
        $Q.list("Purchase.purchase_total_amount").extend("PurchaseTotalAmountCalculator")
        ]]>
    </data>

    <!-- 2. 采购项目数统计 -->
    <database id="purchase_project_count" resultMap="java.util.HashMap" description="采购项目数：进行中/已完成/异常项目数量分布">
        <![CDATA[
        SELECT
            COUNT(*) as total_projects,
            SUM(CASE WHEN p_status = '00' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN p_status = '01' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN p_status = '02' THEN 1 ELSE 0 END) as exception_projects,
            -- 本年度项目数
            SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_projects
        FROM pms_project_info
        WHERE is_delete = 0
        ]]>
    </database>

    <!-- 3. 预算执行率统计 -->
    <database id="budget_execution_rate" resultMap="java.util.HashMap" description="预算执行率：实际支出与预算对比，突出超支/结余状态">
        <![CDATA[
        SELECT
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN 1 ELSE 0 END), 0) as over_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN 1 ELSE 0 END), 0) as under_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount = budget THEN 1 ELSE 0 END), 0) as exact_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN winner_amount - budget ELSE 0 END), 0) as over_budget_amount,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN budget - winner_amount ELSE 0 END), 0) as under_budget_amount
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE()) AND winner_amount IS NOT NULL
        ]]>
    </database>

    <!-- 4. 政采/非政采统计 -->
    <database id="government_procurement_stats" resultMap="java.util.HashMap" description="政采/非政采：项目数量、资金分布">
        <![CDATA[
        SELECT
            p_type,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            CASE
                WHEN p_type = '政府集中采购' THEN '政采'
                WHEN p_type IN ('部门集中采购', '分散采购') THEN '非政采'
                ELSE '其他'
            END as procurement_category
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY p_type, procurement_category
        ]]>
    </database>

    <!-- 5. 采购类别统计 -->
    <database id="purchase_category_stats" resultMap="java.util.HashMap" description="采购类别：货物、服务、工程分类统计">
        <![CDATA[
        SELECT
            CASE
                WHEN purcharse_type = '货物' THEN '货物'
                WHEN purcharse_type = '服务' THEN '服务'
                WHEN purcharse_type IN ('工程', '工程_零星维修') THEN '工程'
                ELSE '其他'
            END as category,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY category
        ORDER BY total_budget DESC
        ]]>
    </database>

    <!-- 6. 使用方向统计 -->
    <database id="usage_direction_stats" resultMap="java.util.HashMap" description="使用方向：行政、教学、科研分类统计">
        <![CDATA[
        SELECT
            CASE
                WHEN usage_direction_name IS NULL OR usage_direction_name = '' THEN '未分类'
                WHEN usage_direction_name LIKE '%行政%' OR usage_direction_name LIKE '%管理%' THEN '行政'
                WHEN usage_direction_name LIKE '%教学%' OR usage_direction_name LIKE '%教育%' THEN '教学'
                WHEN usage_direction_name LIKE '%科研%' OR usage_direction_name LIKE '%研究%' THEN '科研'
                ELSE usage_direction_name
            END as usage_direction,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY usage_direction
        ORDER BY total_budget DESC
        ]]>
    </database>

    <!-- 7. 采购用途统计 -->
    <database id="purchase_content_stats" resultMap="java.util.HashMap" description="采购用途统计：基于content字段分组">
        <![CDATA[
        SELECT
            CASE
                WHEN content IS NULL OR content = '' THEN '未填写'
                ELSE content
            END as content_name,
            COUNT(*) as project_count
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY content_name
        ORDER BY project_count DESC
        ]]>
    </database>

    <!-- 综合概览数据 -->
    <database id="purchase_overview_summary" resultMap="java.util.HashMap" description="采购全局概览综合数据">
        <![CDATA[
        SELECT
            -- 基础统计
            COUNT(*) as total_projects,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,

            -- 项目状态分布
            SUM(CASE WHEN p_status = '00' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN p_status = '01' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN p_status = '02' THEN 1 ELSE 0 END) as exception_projects,

            -- 采购类型分布
            SUM(CASE WHEN p_type = '政府集中采购' THEN 1 ELSE 0 END) as gov_procurement_count,
            SUM(CASE WHEN p_type IN ('部门集中采购', '分散采购') THEN 1 ELSE 0 END) as non_gov_procurement_count,

            -- 采购类别分布
            SUM(CASE WHEN purcharse_type = '货物' THEN 1 ELSE 0 END) as goods_count,
            SUM(CASE WHEN purcharse_type = '服务' THEN 1 ELSE 0 END) as service_count,
            SUM(CASE WHEN purcharse_type IN ('工程', '工程_零星维修') THEN 1 ELSE 0 END) as project_count,

            -- 预算执行情况
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN 1 ELSE 0 END), 0) as over_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount <= budget AND winner_amount IS NOT NULL THEN 1 ELSE 0 END), 0) as within_budget_count

        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        ]]>
    </database>

    <!-- 时间维度分析 - 月度趋势 -->
    <database id="monthly_trend_analysis" resultMap="java.util.HashMap" description="月度采购趋势分析">
        <![CDATA[
        SELECT
            DATE_FORMAT(apply_time, '%Y-%m') as month,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget
        FROM pms_project_info
        WHERE is_delete = 0
            AND apply_time >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(apply_time, '%Y-%m')
        ORDER BY month
        ]]>
    </database>

    <!-- 部门维度分析 -->
    <database id="department_analysis" resultMap="java.util.HashMap" description="部门采购情况分析">
        <![CDATA[
        SELECT
            p_deptId as dept_id,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget,
            SUM(CASE WHEN p_status = '01' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN p_status = '00' THEN 1 ELSE 0 END) as ongoing_count
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY p_deptId
        ORDER BY total_budget DESC
        LIMIT 20
        ]]>
    </database>

    <!-- 采购规模分析 -->
    <database id="procurement_scale_analysis" resultMap="java.util.HashMap" description="采购规模分布分析">
        <![CDATA[
        SELECT
            CASE
                WHEN budget < 10000 THEN '小额采购(<1万)'
                WHEN budget >= 10000 AND budget < 100000 THEN '中等采购(1-10万)'
                WHEN budget >= 100000 AND budget < 1000000 THEN '大额采购(10-100万)'
                WHEN budget >= 1000000 THEN '重大采购(≥100万)'
                ELSE '其他'
            END as scale_category,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY scale_category
        ORDER BY
            CASE scale_category
                WHEN '小额采购(<1万)' THEN 1
                WHEN '中等采购(1-10万)' THEN 2
                WHEN '大额采购(10-100万)' THEN 3
                WHEN '重大采购(≥100万)' THEN 4
                ELSE 5
            END
        ]]>
    </database>

    <!-- 数据处理方法 - 计算同比环比 -->
    <data id="purchase_growth_analysis" description="采购增长分析（同比环比计算）">
        <![CDATA[
        $Q.list("Purchase.purchase_total_amount").extend("PurchaseGrowthCalculator")
        ]]>
    </data>

    <!-- 数据处理方法 - 预算执行率计算 -->
    <data id="budget_execution_analysis" description="预算执行率分析">
        <![CDATA[
        $Q.list("Purchase.budget_execution_rate").extend("BudgetExecutionCalculator")
        ]]>
    </data>

    <!-- 前端图表专用 - 月度采购总额对比数据 -->
    <database id="purchase_monthly_chart_data" resultMap="java.util.HashMap" description="前端ECharts月度采购总额对比数据">
        <![CDATA[
        SELECT
            DATE_FORMAT(apply_time, '%Y-%m') as month,
            DATE_FORMAT(apply_time, '%m月') as month_name,
            YEAR(apply_time) as year,
            MONTH(apply_time) as month_num,
            COALESCE(SUM(budget), 0) as total_amount,
            COUNT(*) as project_count,
            ROUND(AVG(budget), 2) as avg_amount
        FROM pms_project_info
        WHERE is_delete = 0
            AND apply_time >= DATE_SUB(CURDATE(), INTERVAL 24 MONTH)
        GROUP BY YEAR(apply_time), MONTH(apply_time)
        ORDER BY year, month_num
        ]]>
    </database>

    <!-- 前端图表专用 - 格式化的月度对比数据 -->
    <data id="purchase_chart_formatted_data" description="前端ECharts格式化的月度对比数据">
        <![CDATA[
        $Q.list("Purchase.purchase_monthly_chart_data").extend("PurchaseChartDataFormatter")
        ]]>
    </data>

    <!-- 前端图表专用 - 实时采购总额统计 -->
    <database id="purchase_realtime_stats" resultMap="java.util.HashMap" description="实时采购总额统计数据">
        <![CDATA[
        SELECT
            -- 本年度数据
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) THEN budget ELSE 0 END), 0) as current_year_total,
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) AND MONTH(apply_time) = MONTH(CURDATE()) THEN budget ELSE 0 END), 0) as current_month_total,

            -- 去年同期数据
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) - 1 THEN budget ELSE 0 END), 0) as last_year_total,
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) - 1 AND MONTH(apply_time) = MONTH(CURDATE()) THEN budget ELSE 0 END), 0) as last_year_same_month_total,

            -- 上月数据
            COALESCE(SUM(CASE WHEN
                (YEAR(apply_time) = YEAR(CURDATE()) AND MONTH(apply_time) = MONTH(CURDATE()) - 1) OR
                (YEAR(apply_time) = YEAR(CURDATE()) - 1 AND MONTH(apply_time) = 12 AND MONTH(CURDATE()) = 1)
                THEN budget ELSE 0 END), 0) as last_month_total,

            -- 项目数量
            SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_projects,
            SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) AND MONTH(apply_time) = MONTH(CURDATE()) THEN 1 ELSE 0 END) as current_month_projects,

            -- 实际支出
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) AND winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as current_year_actual,
            COALESCE(SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) AND MONTH(apply_time) = MONTH(CURDATE()) AND winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as current_month_actual

        FROM pms_project_info
        WHERE is_delete = 0
        ]]>
    </database>

    <!-- 前端图表专用 - 带计算的实时统计数据 -->
    <data id="purchase_realtime_with_calculation" description="带同比环比计算的实时统计数据">
        <![CDATA[
        $Q.list("Purchase.purchase_realtime_stats").extend("PurchaseRealtimeCalculator")
        ]]>
    </data>

    <!-- 综合仪表盘数据 -->
    <data id="purchase_dashboard_data" description="采购监管大屏综合数据">
        <![CDATA[
        {
            "totalAmount": $Q.list("Purchase.purchase_total_amount_with_calculation"),
            "projectCount": $Q.list("Purchase.purchase_project_count"),
            "budgetExecution": $Q.list("Purchase.budget_execution_rate"),
            "governmentProcurement": $Q.list("Purchase.government_procurement_stats"),
            "categoryStats": $Q.list("Purchase.purchase_category_stats"),
            "usageDirection": $Q.list("Purchase.usage_direction_stats"),
            "contentStats": $Q.list("Purchase.purchase_content_stats"),
            "contentSummary": $Q.list("Purchase.purchase_content_summary"),
            "contentMonthlyTrend": $Q.list("Purchase.purchase_content_monthly_trend"),
            "monthlyTrend": $Q.list("Purchase.monthly_trend_analysis"),
            "departmentAnalysis": $Q.list("Purchase.department_analysis"),
            "scaleAnalysis": $Q.list("Purchase.procurement_scale_analysis"),
            "summary": $Q.list("Purchase.purchase_overview_summary"),
            "chartData": $Q.list("Purchase.purchase_chart_formatted_data"),
            "realtimeStats": $Q.list("Purchase.purchase_realtime_with_calculation")
        }
        ]]>
    </data>

</sqlMap>
