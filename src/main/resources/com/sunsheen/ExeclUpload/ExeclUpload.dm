<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="ExcelUpload" description="部门信息管理表">

   <database id="insertDepart" resultMap="java.lang.Integer" description="新增部门信息管理表数据">
    	INSERT INTO dict_department
        (ID, university, dept_id, dept_name, sub_dept_id, sub_dept_name, editor, edit_date)
        VALUES(:ID, :university, :dept_id,:dept_name, :sub_dept_id, :sub_dept_name, :editor, :edit_date);
    </database>

        <database id="selectById" resultMap="java.util.HashMap" description="查询部门信息">
            SELECT
            *
            FROM
            dict_department
            where ID = :ID and sub_dept_id = :sub_dept_id and dept_id = :dept_id
        </database>

</sqlMap>