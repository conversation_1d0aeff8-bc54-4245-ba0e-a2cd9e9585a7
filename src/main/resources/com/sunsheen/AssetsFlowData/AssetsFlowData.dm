<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="AssetsFlowData" description="工作流程查询">

    <database id="selectWorkflowData" description="查询工作流程数据" resultMap="java.util.HashMap">
        select business_type as flowName, audit_status as flowTitle, task_executor as auditor, start_time as startTime, stay_hours as stayHours
        from
            assets_workflow_data
        where 1=1
        #if ($P.using_department and $P.using_department != '')
            and using_department = :using_department
        #end

    </database>

    <database id="selectWorkflowDataTest" description="查询工作流程数据" resultMap="java.util.HashMap">
        select *
        from
        assets_workflow_data
    </database>

    <database id="countSelectWorkflowData" description="查询工作流程数据" resultMap="java.lang.Long">
        select
            COUNT(*)
        from
            assets_workflow_data
        where 1=1
        #if ($P.using_department and $P.using_department != '')
            and using_department = :using_department
        #end
    </database>

</sqlMap>