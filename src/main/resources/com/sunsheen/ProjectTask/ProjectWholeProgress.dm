<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="ProjectWholeProgress" description="项目总进展">

<database id="insertProjectwholeProgress" description="新增项目总进展">
    INSERT INTO project_whole_progress (
        progress_name,
        project_id,
        reporter_id,
        reporter_name,
        last_week_plan,
        completion,
        gap_reason,
        next_week_plan,
        current_progress,
        created_at,
        progress_percent,
        deviation_percent,
        week_number
    ) VALUES (
        :progressName,
        :projectId,
        :reporterId,
        :reporterName,
        :lastWeekPlan,
        :completion,
        :gapReason,
        :nextWeekPlan,
        :currentProgress,
        :createdAt,
        :progressPercent,
        :deviationPercent,
        :weekNumber
    )
</database>

<database id="deleteProjectwholeProgress" description="删除项目总进展">
    DELETE FROM project_whole_progress
    WHERE id = :id
</database>

<database id="updateProjectwholeProgress" description="更新项目总进展（全字段）">
    UPDATE project_whole_progress
    SET
        progress_name = :progressName,
        last_week_plan = :lastWeekPlan,
        completion = :completion,
        gap_reason = :gapReason,
        next_week_plan = :nextWeekPlan,
        current_progress = :currentProgress,
        progress_percent = :progressPercent,
        deviation_percent = :deviationPercent,
        week_number = :weekNumber
    WHERE id = :id
</database>

<database id="selectProjectwholeProgress" resultMap="java.util.HashMap" description="动态查询项目进展">
    SELECT *
    FROM project_whole_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.projectId and $P.projectId != '')
        AND project_id = :projectId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.weekNumber and $P.weekNumber != '')
        AND week_number = :weekNumber
    #end
    #if ($P.progressName and $P.progressName != '')
        AND progress_name LIKE CONCAT('%', :progressName, '%')
    #end
    ORDER BY created_at DESC
</database>

<database id="countSelectProjectwholeProgress" resultMap="java.util.HashMap" description="动态查询项目进展">
    SELECT COUNT(*)
    FROM project_whole_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.projectId and $P.projectId != '')
        AND project_id = :projectId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.weekNumber and $P.weekNumber != '')
        AND week_number = :weekNumber
    #end
    #if ($P.progressName and $P.progressName != '')
        AND progress_name LIKE CONCAT('%', :progressName, '%')
    #end
</database>

<database id="selectProjectwholeProgressByTime" resultMap="java.util.HashMap" description="根据时间段动态查询项目进展">
    SELECT *
    FROM project_whole_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.projectId and $P.projectId != '')
        AND project_id = :projectId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.weekNumber and $P.weekNumber != '')
        AND week_number = :weekNumber
    #end
    #if ($P.progressName and $P.progressName != '')
        AND progress_name LIKE CONCAT('%', :progressName, '%')
    #end
    #if ($P.startTime and $P.endTime and $P.startTime != '' and $P.endTime != '')
        AND created_at BETWEEN :startTime AND :endTime
    #end
    ORDER BY created_at DESC
</database>

<database id="countSelectProjectwholeProgressByTime" resultMap="java.util.HashMap" description="根据时间段动态查询项目进展">
    SELECT COUNT(*)
    FROM project_whole_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.projectId and $P.projectId != '')
        AND project_id = :projectId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.weekNumber and $P.weekNumber != '')
        AND week_number = :weekNumber
    #end
    #if ($P.progressName and $P.progressName != '')
        AND progress_name LIKE CONCAT('%', :progressName, '%')
    #end
    #if ($P.startTime and $P.endTime and $P.startTime != '' and $P.endTime != '')
        AND created_at BETWEEN :startTime AND :endTime
    #end
</database>

</sqlMap>