<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="RegularTask" description="常规任务">

<database id="insertTest" resultMap="java.util.HashMap" description="新增任务/项目分工">
    INSERT INTO demo_code
            (code, text)
        VALUES
            (:code, :text);
</database>

<database id="insertProjectTask" resultMap="java.lang.Integer" description="新增项目/任务">
    INSERT INTO project_task
        (name, type, status, owner_id, owner_name, department_id, department_name)
    VALUES
        (:name, :type, :status, :ownerId, :ownerName, :departmentId, :departmentName);
</database>

<database id="countTaskAssignment" resultMap="java.lang.Long" description="查询任务分工存在">
    SELECT COUNT(*)
        FROM task_assignment
    WHERE
        ref_id = :refId
        AND participant_id = :loginAccount
</database>

<database id="countProjectTask" resultMap="java.lang.Long" description="查询是否为项目经理">
    SELECT COUNT(*)
        FROM project_task
    WHERE
        id = :refId
        AND owner_id = :loginAccount
</database>

<database id="selectTaskAssignmentByRefId" resultMap="java.util.HashMap" description="查询任务分工">
    SELECT ta.participant_id, ta.participant_name
    FROM task_assignment ta
    WHERE ref_id = :refId
</database>

<database id="selectTaskAssignment" resultMap="java.util.HashMap" description="查询任务分工">
    SELECT *
        FROM task_assignment ta
        WHERE 1 = 1
        #if ($P.id and $P.id != '')
            AND ta.id = :id
        #end
        #if ($P.type and $P.type != '')
            AND ta.type = :type
        #end
        #if ($P.refId and $P.refId != '')
            AND ta.ref_id = :refId
        #end
        #if ($P.participantId and $P.participantId != '')
            AND ta.participant_id = :participantId
        #end
        #if ($P.participantName and $P.participantName != '')
            AND ta.participant_name LIKE CONCAT('%', :participantName, '%')
        #end
        #if ($P.createdAt and $P.createdAt != '')
            AND ta.created_at = :createdAt
        #end
        #if ($P.content and $P.content != '')
        AND ta.content = :content
        #end
        #if ($P.position and $P.position != '')
        AND ta.position = :position
        #end
</database>

<database id="countSelectTaskAssignment" resultMap="java.util.HashMap" description="查询任务分工">
    SELECT COUNT(*)
        FROM task_assignment ta
        WHERE 1 = 1
        #if ($P.id and $P.id != '')
            AND ta.id = :id
        #end
        #if ($P.type and $P.type != '')
            AND ta.type = :type
        #end
        #if ($P.refId and $P.refId != '')
            AND ta.ref_id = :refId
        #end
        #if ($P.participantId and $P.participantId != '')
            AND ta.participant_id = :participantId
        #end
        #if ($P.participantName and $P.participantName != '')
            AND ta.participant_name LIKE CONCAT('%', :participantName, '%')
        #end
        #if ($P.createdAt and $P.createdAt != '')
            AND ta.created_at = :createdAt
        #end
</database>

<database id="selectProjectTask" resultMap="java.util.HashMap" description="动态查询项目/任务信息（包含一个type）">
    SELECT pt.*
    FROM project_task pt
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND pt.id = :id
    #end
    #if ($P.name and $P.name != '')
        AND pt.name LIKE CONCAT('%', :name, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
        AND pt.created_at = :createdAt
    #end
    #if ($P.status and $P.status != '')
        AND pt.status = :status
    #end
    #if ($P.ownerId and $P.ownerId != '')
        AND pt.owner_id = :ownerId
    #end
    #if ($P.ownerName and $P.ownerName != '')
        AND pt.owner_name LIKE CONCAT('%', :ownerName, '%')
    #end
    #if ($P.departmentId and $P.departmentId != '')
        AND pt.department_id = :departmentId
    #end
    #if ($P.departmentName and $P.departmentName != '')
        AND pt.department_name LIKE CONCAT('%', :departmentName, '%')
    #end
</database>

<database id="countSelectProjectTask" resultType="int" description="统计项目/任务总数（包含筛选条件）">
    SELECT COUNT(*)
    FROM project_task pt
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
    AND pt.id = :id
    #end
    #if ($P.name and $P.name != '')
    AND pt.name LIKE CONCAT('%', :name, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
    AND pt.created_at = :createdAt
    #end
    #if ($P.status and $P.status != '')
    AND pt.status = :status
    #end
    #if ($P.ownerId and $P.ownerId != '')
    AND pt.owner_id = :ownerId
    #end
    #if ($P.ownerName and $P.ownerName != '')
    AND pt.owner_name LIKE CONCAT('%', :ownerName, '%')
    #end
    #if ($P.departmentId and $P.departmentId != '')
    AND pt.department_id = :departmentId
    #end
    #if ($P.departmentName and $P.departmentName != '')
    AND pt.department_name LIKE CONCAT('%', :departmentName, '%')
    #end
</database>

<database id="selectProjectTaskRel" resultMap="java.util.HashMap" description="动态查询项目/任务信息（包含一个type）">
    SELECT pt.*
    FROM project_task pt
    LEFT JOIN task_assignment ta ON pt.id = ta.ref_id
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
    AND pt.id = :id
    #end
    #if ($P.name and $P.name != '')
    AND pt.name LIKE CONCAT('%', :name, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
    AND pt.created_at = :createdAt
    #end
    #if ($P.status and $P.status != '')
    AND pt.status = :status
    #end
    #if ($P.ownerId and $P.ownerId != '')
    AND pt.owner_id = :ownerId
    #end
    #if ($P.ownerName and $P.ownerName != '')
    AND pt.owner_name LIKE CONCAT('%', :ownerName, '%')
    #end
    #if ($P.departmentId and $P.departmentId != '')
    AND pt.department_id = :departmentId
    #end
    #if ($P.departmentName and $P.departmentName != '')
    AND pt.department_name LIKE CONCAT('%', :departmentName, '%')
    #end
    AND :loginAccount = pt.owner_id or :loginAccount = ta.participant_id
    GROUP BY pt.id
</database>

<database id="countSelectProjectTaskRel" resultType="int" description="统计项目/任务总数（包含筛选条件）">
    SELECT COUNT(*)
    FROM (
    SELECT pt.id
    FROM project_task pt
    LEFT JOIN task_assignment ta ON pt.id = ta.ref_id
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
    AND pt.id = :id
    #end
    #if ($P.name and $P.name != '')
    AND pt.name LIKE CONCAT('%', :name, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
    AND pt.created_at = :createdAt
    #end
    #if ($P.status and $P.status != '')
    AND pt.status = :status
    #end
    #if ($P.ownerId and $P.ownerId != '')
    AND pt.owner_id = :ownerId
    #end
    #if ($P.ownerName and $P.ownerName != '')
    AND pt.owner_name LIKE CONCAT('%', :ownerName, '%')
    #end
    #if ($P.departmentId and $P.departmentId != '')
    AND pt.department_id = :departmentId
    #end
    #if ($P.departmentName and $P.departmentName != '')
    AND pt.department_name LIKE CONCAT('%', :departmentName, '%')
    #end
    AND :loginAccount = pt.owner_id or :loginAccount = ta.participant_id
    GROUP BY pt.id
    ) AS tmp
</database>

    <database id="getKey" resultMap="java.math.BigInteger" description="获取自增id">
    SELECT LAST_INSERT_ID();
</database>

<database id="insertTaskProgress" resultMap="java.lang.Integer" description="新增任务进展">
    INSERT INTO task_progress
        (sub_task_name, task_id, reporter_id, reporter_name, work_content, work_result, issues)
    VALUES
        (:subTaskName, :taskId, :reporterId, :reporterName, :workContent, :workResult, :issues);
</database>

<database id="insertTaskAssignment" resultMap="java.lang.Integer" description="新增任务/项目分工">
    INSERT INTO task_assignment
        (type, ref_id, participant_id, participant_name, position, content)
    VALUES
        (:type, :refId, :participantId, :participantName, :position, :content);
</database>

<database id="queryAccount" resultMap="java.util.HashMap" description="新增任务/项目分工">
    SELECT * FROM
    sys_user
    WHERE
    nickName = :loginAccount
</database>

<database id="updateTaskProgress" description="更新任务进展信息">
    UPDATE task_progress
    SET
        sub_task_name = :subTaskName,
        work_content = :workContent,
        work_result = :workResult,
        issues = :issues
    WHERE id = :id
</database>

<database id="deleteTaskProgress" description="根据ID删除任务进展记录">
    DELETE FROM task_progress
    WHERE id = :id
</database>

<database id="deleteTaskAssignment" description="根据ID删除任务分工记录">
    DELETE FROM task_assignment
    WHERE id = :id
</database>

<database id="deleteProjectTask" description="根据ID删除任务/项目记录">
    DELETE FROM project_task
    WHERE id = :id
</database>

<database id="countSelectTaskProgress" resultMap="java.util.HashMap" description="动态查询任务进展记录">
    SELECT COUNT(*)
    FROM task_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.subTaskName and $P.subTaskName != '')
        AND sub_task_name LIKE CONCAT('%', :subTaskName, '%')
    #end
    #if ($P.taskId and $P.taskId != '')
        AND task_id = :taskId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.workContent and $P.workContent != '')
        AND work_content LIKE CONCAT('%', :workContent, '%')
    #end
    #if ($P.workResult and $P.workResult != '')
        AND work_result LIKE CONCAT('%', :workResult, '%')
    #end
    #if ($P.issues and $P.issues != '')
        AND issues LIKE CONCAT('%', :issues, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
        AND created_at = :createdAt
    #end
</database>

<database id="selectTaskProgress" resultMap="java.util.HashMap" description="动态查询任务进展记录">
    SELECT *
    FROM task_progress
    WHERE 1 = 1
    #if ($P.id and $P.id != '')
        AND id = :id
    #end
    #if ($P.subTaskName and $P.subTaskName != '')
        AND sub_task_name LIKE CONCAT('%', :subTaskName, '%')
    #end
    #if ($P.taskId and $P.taskId != '')
        AND task_id = :taskId
    #end
    #if ($P.reporterId and $P.reporterId != '')
        AND reporter_id = :reporterId
    #end
    #if ($P.reporterName and $P.reporterName != '')
        AND reporter_name LIKE CONCAT('%', :reporterName, '%')
    #end
    #if ($P.workContent and $P.workContent != '')
        AND work_content LIKE CONCAT('%', :workContent, '%')
    #end
    #if ($P.workResult and $P.workResult != '')
        AND work_result LIKE CONCAT('%', :workResult, '%')
    #end
    #if ($P.issues and $P.issues != '')
        AND issues LIKE CONCAT('%', :issues, '%')
    #end
    #if ($P.createdAt and $P.createdAt != '')
        AND created_at = :createdAt
    #end
    ORDER BY created_at DESC
</database>

<database id="selectLatestTaskProgressByTaskId" resultMap="java.util.HashMap" description="查询每位参与人最近一次任务进展记录（需存在分工记录）">
    SELECT tp.*
    FROM task_progress tp
    INNER JOIN (
        SELECT reporter_id, MAX(created_at) AS latest_time
        FROM task_progress
        WHERE task_id = :taskId
        GROUP BY reporter_id
    ) latest ON tp.reporter_id = latest.reporter_id AND tp.created_at = latest.latest_time
    INNER JOIN task_assignment ta ON ta.ref_id = tp.task_id AND ta.participant_id = tp.reporter_id
    WHERE tp.task_id = :taskId
</database>

<database id="updateProjectTask" description="全字段更新项目任务">
    UPDATE project_task
    SET
        name = :name,
        status = :status,
        owner_id = :ownerId,
        owner_name = :ownerName,
        department_id = :departmentId,
        department_name = :departmentName
    WHERE id = :id
</database>

<database id="updateTaskAssignment" description="全字段更新分工 ">
    UPDATE task_assignment
    SET
    position = :position,
    content = :content
    WHERE id = :id
</database>

</sqlMap>