<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="AIAgent" description="资产预警">
    <database id="getAbnormalAssetsNumber">
        <![CDATA[
        SELECT
            SUM(quantity >= 200) AS count_warning_level_2,
            SUM(quantity < 200) AS count_warning_level_1
        FROM assets_over_regis
        WHERE 1=1
        #if($P.department_name and $P.department_name != '')
            and department_name = :department_name
        #end
        ]]>
    </database>
    <database id="getMove">
        <![CDATA[
        SELECT COUNT(DISTINCT user_code) AS count_warning_level_1
        FROM assets_teacher_leave
        WHERE 1=1
        #if($P.department_name and $P.department_name != '')
            and department_name = :department_name
        #end

        ]]>
    </database>
    <database id="getTransfer">
        SELECT
        count(*) as count_warning_level_1
        FROM
        assets_dept_change
        WHERE 1=1
        #if($P.department_name and $P.department_name != '')
        and new_department = :department_name
        #end
    </database>
    <database id="getAssetAlert">
        SELECT
        process,
        SUM(CASE WHEN warning_level = 1 THEN 1 ELSE 0 END) AS count_warning_level_1,
        SUM(CASE WHEN warning_level = 2 THEN 1 ELSE 0 END) AS count_warning_level_2,
        COUNT(*) AS total_count -- 每个分组的总行数
        FROM warning_list
        WHERE process IN ('资产登记', '资产增值', '资产减值', '低值耐用品登记', '资产报废')
        #if($P.department_name and $P.department_name != '')
        and dept_name = :department_name
        #end
        GROUP BY process
        ORDER BY process
    </database>
</sqlMap>