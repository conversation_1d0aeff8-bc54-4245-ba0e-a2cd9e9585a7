<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="ProcessMonitoringLevelOne" description="流程查询">

<database id="queryBusinessTypes" resultMap="java.util.HashMap" description="获取所有流程类型">
    SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
</database>

<database id="querySubmittedProcesses" resultMap="java.util.HashMap" description="统计提交的流程数">
    SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        #if ($P.startTime and $P.startTime != '')
            WHERE start_time >= :startTime
        #end
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        #if ($P.startTime and $P.startTime != '')
            WHERE start_time >= :startTime
        #end
    ) AS all_processes
    GROUP BY business_type;
</database>

<database id="queryOngoingProcesses" resultMap="java.util.HashMap" description="统计进行中的流程数">
    SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    #if ($P.startTime and $P.startTime != '')
        WHERE start_time >= :startTime
    #end
    GROUP BY business_type;
</database>

<database id="queryCompletedProcesses" resultMap="java.util.HashMap" description="统计结束的流程数">
    SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    #if ($P.startTime and $P.startTime != '')
        WHERE end_time >= :startTime
    #end
    GROUP BY business_type;
</database>

<database id="queryWarnings" resultMap="java.util.HashMap" description="统计预警数据">
      SELECT w.process AS business_type,
               COUNT(*) AS total_warnings,
               SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
               SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM assets_workflow_data a
            LEFT JOIN
            warning_list w
        ON
            a.document_number = w.bussiness_id
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
            #if ($P.startTime and $P.startTime != '')
                AND w.start_time >= :startTime
            #end
        GROUP BY w.process;
</database>

<database id="queryTotalMonitoringIndicator" resultMap="java.util.HashMap" description="全校监测指标（流程总数）">
    SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
            #if ($P.department_name and $P.department_name != '')
                WHERE using_department = :department_name
            #end
        UNION ALL
        SELECT id FROM assets_workflow_his
            #if ($P.department_name and $P.department_name != '')
                WHERE using_department = :department_name
            #end
        UNION ALL
        SELECT user_code FROM asset_Registration
            #if ($P.department_name and $P.department_name != '')
                WHERE user_department_name = :department_name
            #end
        GROUP BY user_code
    ) AS all_processes;
</database>

<database id="queryWarningCount" resultMap="java.util.HashMap" description="统计预警数据（总数）">
    SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
    #if ($P.department_name and $P.department_name != '')
    AND dept_name = :department_name
    #end
</database>



<database id="queryWorkflowDetails" resultMap="java.util.HashMap" description="查询资产审批流程相关字段">
    SELECT
        document_number,
        asset_name,
        audit_status,
        using_department,
        total_quantity,
        total_amount
    FROM
        assets_workflow_data;
</database>

<database id="queryWarningDetails" resultMap="java.util.HashMap" description="查询两个表的关系字段">
    SELECT
        w.warning_level,
        a.business_type,
        a.asset_name,
        a.document_number,
        a.asset_name,
        a.audit_status,
        a.using_department,
        a.total_quantity,
        a.total_amount
    FROM
        assets_workflow_data a
    INNER JOIN
        warning_list w
    ON
        a.document_number = w.bussiness_id;
</database>

<database id="queryAllWorkflowDataWithWarnings" resultMap="java.util.HashMap" description="查询资产审批流程所有字段及关联预警数据">
    SELECT
        a.id,
        a.business_type,
        a.document_number,
        a.audit_status,
        a.using_department,
        a.asset_name,
        a.initiator,
        a.total_quantity,
        a.total_amount,
        a.task_executor,
        a.stay_hours,
        a.start_time,
        a.update_time,
        w.warning_id,
        w.warning_level,
        w.indicator_name,
        w.start_time AS warning_start_time,
        w.update_time AS warning_update_time
    FROM
        assets_workflow_data a
    LEFT JOIN
        warning_list w
    ON
        a.document_number = w.bussiness_id
    WHERE
        audit_status != "资产报废待终审";
</database>


</sqlMap>