<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="WarningListUpdate" description="预警列表更新">

  <!-- 定时任务标识 -->
  <database id="scheduleWarningListUpdate" resultMap="java.lang.Integer" description="执行预警更新定时任务">
    <![CDATA[
    SELECT 1 AS task_executed
    ]]>
  </database>

  <!-- 调用存储过程 -->
  <database id="callWarningListUpdate" statementType="CALLABLE" description="调用预警更新存储过程">
    <![CDATA[
    CALL proc_warning_list_update()
    ]]>
  </database>

  <!-- 调用异常数据捕获存储过程 -->
    <database id="callCaptureAssetExceptions" statementType="CALLABLE" description="调用异常数据捕获存储过程">
      <![CDATA[
      CALL proc_capture_asset_exceptions()
      ]]>
    </database>

    <!-- 调用警告列表更新存储过程 -->
    <database id="callUpdateWarningList" statementType="CALLABLE" description="调用警告列表更新存储过程">
      <![CDATA[
      CALL proc_update_warning_list()
      ]]>
    </database>

</sqlMap>
