<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="research_showboard" description="科研看板">

    <!-- 横向项目成果查询 -->
    <database id="select_HXXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="横向项目成果查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t1.cnt, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, COUNT(*) AS cnt FROM research.KYGLXT_HXXMCG t1
        WHERE IFNULL(t1.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t1 ON y.year = t1.LXNF
        ORDER BY y.year
        ) AS year_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t2.funding, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, SUM(XMZJF) AS funding FROM research.KYGLXT_HXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t2 ON y.year = t2.LXNF
        ORDER BY y.year
        ) AS year_funding,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_HXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS total_funding
        FROM research.KYGLXT_HXXMCG t
        WHERE IFNULL(t.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 纵向项目成果查询 -->
    <database id="select_ZXXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="纵向项目成果查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t1.cnt, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, COUNT(*) AS cnt FROM research.KYGLXT_ZXXMCG t1
        WHERE IFNULL(t1.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t1 ON y.year = t1.LXNF
        ORDER BY y.year
        ) AS year_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t2.funding, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, SUM(XMZJF) AS funding FROM research.KYGLXT_ZXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t2 ON y.year = t2.LXNF
        ORDER BY y.year
        ) AS year_funding,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_ZXXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS total_funding
        FROM research.KYGLXT_ZXXMCG t
        WHERE IFNULL(t.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 校内项目成果批量导入 -->
    <database id="select_XNXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="校内项目成果批量导入">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_XNXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t1.cnt, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, COUNT(*) AS cnt FROM research.KYGLXT_XNXMCG t1
        WHERE IFNULL(t1.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t1 ON y.year = t1.LXNF
        ORDER BY y.year
        ) AS year_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', y.year, '":', IFNULL(t2.funding, 0))), '}')
        FROM (
        SELECT 2015 AS year UNION SELECT 2016 UNION SELECT 2017 UNION SELECT 2018 UNION SELECT 2019
        UNION SELECT 2020 UNION SELECT 2021 UNION SELECT 2022 UNION SELECT 2023 UNION SELECT 2024
        UNION SELECT 2025
        ) y
        LEFT JOIN (
        SELECT LXNF, SUM(XMZJF) AS funding FROM research.KYGLXT_XNXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY LXNF
        ) t2 ON y.year = t2.LXNF
        ORDER BY y.year
        ) AS year_funding,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_XNXMCG t2
        WHERE IFNULL(t2.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS total_funding
        FROM research.KYGLXT_XNXMCG t
        WHERE IFNULL(t.`CDDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 期刊论文数据查询 -->
    <database id="select_QKLWSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="期刊论文数据查询">
        SELECT
        t.ID,
        t.QKLWMC,
        t.GXZ,
        t.CBDW,
        t.CBSJ,
        t.CBDWJB,
        (SELECT COUNT(*) FROM research.KYGLXT_QKLWSJ t2
        WHERE IFNULL(t2.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(CBSJ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_QKLWSJ t1
        WHERE CBSJ IS NOT NULL AND IFNULL(t1.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_QKLWSJ t
        WHERE IFNULL(t.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 会议论文数据管理 -->
    <database id="select_HYLWSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="会议论文数据管理">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HYLWSJ t2
        WHERE IFNULL(t2.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(CBSJ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_HYLWSJ t1
        WHERE CBSJ IS NOT NULL AND IFNULL(t1.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_HYLWSJ t
        WHERE IFNULL(t.`GXZGSBM`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 专利数据归档 -->
    <database id="select_ZLSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="专利数据归档">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZLSJ t2
        WHERE IFNULL(t2.`SQJG`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(SQR) AS year, COUNT(*) AS cnt FROM research.KYGLXT_ZLSJ t1
        WHERE SQR IS NOT NULL AND IFNULL(t1.`SQJG`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', ZLLB, '":', cnt)), '}')
        FROM (SELECT ZLLB, COUNT(*) AS cnt FROM research.KYGLXT_ZLSJ t1
        WHERE ZLLB IS NOT NULL AND IFNULL(t1.`SQJG`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY ZLLB) AS sub
        ) AS type_count
        FROM research.KYGLXT_ZLSJ t
        WHERE IFNULL(t.`SQJG`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 研究报告数据查询 -->
    <database id="select_YJBGSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="研究报告数据查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_YJBGSJ t2
        WHERE IFNULL(t2.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(TJSJ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_YJBGSJ t1
        WHERE TJSJ IS NOT NULL AND IFNULL(t1.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_YJBGSJ t
        WHERE IFNULL(t.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 报纸数据管理 -->
    <database id="select_BAOZSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="报纸数据管理">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_BAOZSJ t2
        WHERE IFNULL(t2.`SMDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(FBRQ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_BAOZSJ t1
        WHERE FBRQ IS NOT NULL AND IFNULL(t1.`SMDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_BAOZSJ t
        WHERE IFNULL(t.`SMDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 著作权数据查询 -->
    <database id="select_ZZQSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="著作权数据查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZZQSJ t2
        WHERE IFNULL(t2.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(WBSJ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_ZZQSJ t1
        WHERE WBSJ IS NOT NULL AND IFNULL(t1.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_ZZQSJ t
        WHERE IFNULL(t.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 科研项目数据统计 -->
    <database id="select_CYXMSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="科研项目数据统计">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_CYXMSJ t2
        WHERE IFNULL(t2.`WCRDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT TJND AS year, COUNT(*) AS cnt FROM research.KYGLXT_CYXMSJ t1
        WHERE TJND IS NOT NULL AND IFNULL(t1.`WCRDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_CYXMSJ t
        WHERE IFNULL(t.`WCRDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 获奖数据统计 -->
    <database id="select_HJSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="获奖数据统计">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HJSJ t2
        WHERE IFNULL(t2.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', year, '":', cnt)), '}')
        FROM (SELECT YEAR(HJRQ) AS year, COUNT(*) AS cnt FROM research.KYGLXT_HJSJ t1
        WHERE HJRQ IS NOT NULL AND IFNULL(t1.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%') GROUP BY year) AS sub
        ) AS year_count
        FROM research.KYGLXT_HJSJ t
        WHERE IFNULL(t.`WCDW`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        LIMIT 1
    </database>

    <!-- 成果项目关联查询 -->
    <database id="select_CGXMGL" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="成果项目关联查询">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_CGXMGL t2
        WHERE IFNULL(t2.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.KYGLXT_CGXMGL t
        WHERE IFNULL(t.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
    </database>

    <!-- 项目成员关系维护 -->
    <database id="select_XMFZRXMCYGL" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="项目成员关系维护">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_XMFZRXMCYGL t2
        WHERE IFNULL(t2.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.KYGLXT_XMFZRXMCYGL t
        WHERE IFNULL(t.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
    </database>

    <!-- 标准数据查询 -->
    <database id="select_BZSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="标准数据查询">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_BZSJ t2
        WHERE IFNULL(t2.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.KYGLXT_BZSJ t
        WHERE IFNULL(t.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
    </database>

    <!-- 艺术作品数据管理 -->
    <database id="select_YSZPSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="艺术作品数据管理">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_YSZPSJ t2
        WHERE IFNULL(t2.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.KYGLXT_YSZPSJ t
        WHERE IFNULL(t.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
    </database>

    <!-- 成果及获奖与作者对应关系维护 -->
    <database id="select_CGJHJYZZDYGX" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="成果及获奖与作者对应关系维护">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_CGJHJYZZDYGX t2
        WHERE IFNULL(t2.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.KYGLXT_CGJHJYZZDYGX t
        WHERE IFNULL(t.`学院列名`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
    </database>

    <!-- 平台信息 -->
    <database id="select_research_platform_info" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="平台信息">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.research_platform_info t2
        WHERE IFNULL(t2.`affiliated_college`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')) AS record_count
        FROM research.research_platform_info t
        WHERE IFNULL(t.`affiliated_college`, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%');
    </database>

    <!-- 项目合集 -->
    <database id="select_all_project_info" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="项目合集">
        SELECT * FROM (
            SELECT '横向' AS project_type, HTMC, FZR FROM research.KYGLXT_HXXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
            UNION ALL
            SELECT '纵向' AS project_type, HTMC, FZR FROM research.KYGLXT_ZXXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
            UNION ALL
            SELECT '校内' AS project_type, HTMC, FZR FROM research.KYGLXT_XNXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        ) t
        WHERE t.project_type = :type AND t.HTMC LIKE CONCAT('%', IFNULL(:htmc, ''), '%')
        LIMIT :pageStart, :pageSize
    </database>

    <!-- 项目合集总数 -->
    <database id="select_all_project_info_count" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="项目合集总数">
        SELECT COUNT(*) AS total FROM (
            SELECT '横向' AS project_type, HTMC, FZR FROM research.KYGLXT_HXXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
            UNION ALL
            SELECT '纵向' AS project_type, HTMC, FZR FROM research.KYGLXT_ZXXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
            UNION ALL
            SELECT '校内' AS project_type, HTMC, FZR FROM research.KYGLXT_XNXMCG
            WHERE IFNULL(CDDW, '') LIKE CONCAT('%', IFNULL(:dept, ''), '%')
        ) t
        WHERE t.project_type = :type AND t.HTMC LIKE CONCAT('%', IFNULL(:htmc, ''), '%')
    </database>

</sqlMap>