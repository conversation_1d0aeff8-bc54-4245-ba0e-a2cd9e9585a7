<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="normal_user" description="科研看板-普通用户">

    <!-- 横向项目成果查询 -->
    <database id="select_HXXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="横向项目成果查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HXXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_HXXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS total_funding
        FROM research.KYGLXT_HXXMCG t
        WHERE IFNULL(t.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')

    </database>

    <!-- 纵向项目成果查询 -->
    <database id="select_ZXXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="纵向项目成果查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZXXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_ZXXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS total_funding
        FROM research.KYGLXT_ZXXMCG t
        WHERE IFNULL(t.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 校内项目成果批量导入 -->
    <database id="select_XNXMCG" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="校内项目成果批量导入">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_XNXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count,
        (SELECT SUM(XMZJF) FROM research.KYGLXT_XNXMCG t2
        WHERE IFNULL(t2.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS total_funding
        FROM research.KYGLXT_XNXMCG t
        WHERE IFNULL(t.`FZR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 期刊论文数据查询 -->
    <database id="select_QKLWSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="期刊论文数据查询">
        SELECT
        t.ID,
        t.QKLWMC,
        t.GXZ,
        t.CBDW,
        t.CBSJ,
        t.CBDWJB,
        (SELECT COUNT(*) FROM research.KYGLXT_QKLWSJ t2
        WHERE IFNULL(t2.`GXZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_QKLWSJ t
        WHERE IFNULL(t.`GXZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 会议论文数据管理 -->
    <database id="select_HYLWSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="会议论文数据管理">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HYLWSJ t2
        WHERE IFNULL(t2.`GXZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_HYLWSJ t
        WHERE IFNULL(t.`GXZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 专利数据归档 -->
    <database id="select_ZLSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="专利数据归档">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZLSJ t2
        WHERE IFNULL(t2.`FMR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count,
        (SELECT CONCAT('{', GROUP_CONCAT(CONCAT('"', ZLLB, '":', cnt)), '}')
        FROM (SELECT ZLLB, COUNT(*) AS cnt FROM research.KYGLXT_ZLSJ t1
        WHERE ZLLB IS NOT NULL AND IFNULL(t1.`FMR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%') GROUP BY ZLLB) AS sub
        ) AS type_count
        FROM research.KYGLXT_ZLSJ t
        WHERE IFNULL(t.`FMR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 研究报告数据查询 -->
    <database id="select_YJBGSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="研究报告数据查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_YJBGSJ t2
        WHERE IFNULL(t2.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_YJBGSJ t
        WHERE IFNULL(t.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 报纸数据管理 -->
    <database id="select_BAOZSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="报纸数据管理">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_BAOZSJ t2
        WHERE IFNULL(t2.`SMZZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_BAOZSJ t
        WHERE IFNULL(t.`SMZZ`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 著作权数据查询 -->
    <database id="select_ZZQSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="著作权数据查询">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_ZZQSJ t2
        WHERE IFNULL(t2.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_ZZQSJ t
        WHERE IFNULL(t.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 科研项目数据统计 -->
    <database id="select_CYXMSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="科研项目数据统计">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_CYXMSJ t2
        WHERE IFNULL(t2.`BXWCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_CYXMSJ t
        WHERE IFNULL(t.`BXWCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 获奖数据统计 -->
    <database id="select_HJSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="获奖数据统计">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.KYGLXT_HJSJ t2
        WHERE IFNULL(t2.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_HJSJ t
        WHERE IFNULL(t.`WCR`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
         
    </database>

    <!-- 成果项目关联查询 -->
    <database id="select_CGXMGL" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="成果项目关联查询">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_CGXMGL t2
        WHERE IFNULL(t2.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_CGXMGL t
        WHERE IFNULL(t.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
    </database>

    <!-- 项目成员关系维护 -->
    <database id="select_XMFZRXMCYGL" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="项目成员关系维护">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_XMFZRXMCYGL t2
        WHERE IFNULL(t2.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_XMFZRXMCYGL t
        WHERE IFNULL(t.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
    </database>

    <!-- 标准数据查询 -->
    <database id="select_BZSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="标准数据查询">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_BZSJ t2
        WHERE IFNULL(t2.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_BZSJ t
        WHERE IFNULL(t.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
    </database>

    <!-- 艺术作品数据管理 -->
    <database id="select_YSZPSJ" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="艺术作品数据管理">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_YSZPSJ t2
        WHERE IFNULL(t2.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_YSZPSJ t
        WHERE IFNULL(t.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
    </database>

    <!-- 成果及获奖与作者对应关系维护 -->
    <database id="select_CGJHJYZZDYGX" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="成果及获奖与作者对应关系维护">
        SELECT *, (SELECT COUNT(*) FROM research.KYGLXT_CGJHJYZZDYGX t2
        WHERE IFNULL(t2.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')) AS record_count
        FROM research.KYGLXT_CGJHJYZZDYGX t
        WHERE IFNULL(t.`XM`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
    </database>

    <!-- 平台信息 -->
    <database id="select_research_platform_info" persistenceUnit="research" dataSource="researchDB" resultMap="java.util.HashMap" description="平台信息">
        SELECT
        t.*,
        (SELECT COUNT(*) FROM research.research_platform_info t2
        WHERE (IFNULL(t2.`leader`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
           OR IFNULL(t2.`contact_person`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%'))) AS record_count
        FROM research.research_platform_info t
        WHERE (IFNULL(t.`leader`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%')
           OR IFNULL(t.`contact_person`, '') LIKE CONCAT('%', IFNULL(:username, ''), '%'));
    </database>

</sqlMap>