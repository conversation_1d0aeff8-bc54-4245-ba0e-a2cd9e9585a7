<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="AnnualWorkPlan" description="资产管理">
    <database id="insertAnnualWorkPlan" description="新增年度工作计划">
        INSERT INTO annual_work_plan (
        title,
        content,
        progress,
        progress_percent,
        deviation_percent,
        department_id,
        department_name
        ) VALUES (
        :title,
        :content,
        :progress,
        :progressPercent,
        :deviationPercent,
        :departmentId,
        :departmentName
        )
    </database>
    <database id="deleteAnnualWorkPlanById" description="根据ID删除年度工作计划">
        DELETE FROM annual_work_plan
        WHERE id = :id
    </database>
    <database id="updateAnnualWorkPlan" description="更新年度工作计划（全字段）">
        UPDATE annual_work_plan
        SET
        title = :title,
        content = :content,
        progress = :progress,
        progress_percent = :progressPercent,
        deviation_percent = :deviationPercent,
        department_id = :departmentId,
        department_name = :departmentName
        WHERE id = :id
    </database>
    <database id="selectAnnualWorkPlan" resultMap="java.util.HashMap" description="动态查询年度工作计划">
        SELECT *
        FROM annual_work_plan
        WHERE 1 = 1
        #if ($P.id and $P.id != '')
        AND id = :id
        #end
        #if ($P.title and $P.title != '')
        AND title LIKE CONCAT('%', :title, '%')
        #end
        #if ($P.content and $P.content != '')
        AND content LIKE CONCAT('%', :content, '%')
        #end
        #if ($P.progress and $P.progress != '')
        AND progress LIKE CONCAT('%', :progress, '%')
        #end
        #if ($P.departmentId and $P.departmentId != '')
        AND department_id = :departmentId
        #end
        #if ($P.departmentName and $P.departmentName != '')
        AND department_name LIKE CONCAT('%', :departmentName, '%')
        #end
        #if ($P.createdAt and $P.createdAt != '')
        AND DATE(created_at) = :createdAt
        #end
        ORDER BY created_at DESC
    </database>
    <database id="countSelectAnnualWorkPlan" resultMap="java.util.HashMap" description="动态查询年度工作计划">
        SELECT COUNT(*)
        FROM annual_work_plan
        WHERE 1 = 1
        #if ($P.id and $P.id != '')
        AND id = :id
        #end
        #if ($P.title and $P.title != '')
        AND title LIKE CONCAT('%', :title, '%')
        #end
        #if ($P.content and $P.content != '')
        AND content LIKE CONCAT('%', :content, '%')
        #end
        #if ($P.progress and $P.progress != '')
        AND progress LIKE CONCAT('%', :progress, '%')
        #end
        #if ($P.departmentId and $P.departmentId != '')
        AND department_id = :departmentId
        #end
        #if ($P.departmentName and $P.departmentName != '')
        AND department_name LIKE CONCAT('%', :departmentName, '%')
        #end
        #if ($P.createdAt and $P.createdAt != '')
        AND DATE(created_at) = :createdAt
        #end
    </database>

    <database id="selectDepartmentIdByNickName" resultMap="java.util.HashMap" description="查询部门名">
        SELECT deptId, dept
        FROM sys_user
        WHERE nickName = :loginAccount
    </database>

</sqlMap>