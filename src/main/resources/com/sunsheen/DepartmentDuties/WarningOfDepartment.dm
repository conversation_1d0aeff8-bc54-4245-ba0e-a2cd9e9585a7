<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="WarningList" description="根据部门查询警告信息">

    <database id="selectWarningInfoByDepartment" resultMap="java.lang.Map" description="查询用户">
        SELECT
            bussiness,
            indicator_id,
            indicator_name,
            warning_level,
            COUNT(*) AS warning_count,
            SUM(CASE WHEN isClosed = 0 THEN 1 ELSE 0 END) AS active_warnings,
            SUM(CASE WHEN isClosed = 1 THEN 1 ELSE 0 END) AS closed_warnings,
            MAX(update_time) AS last_updated
        FROM
            warning_list
        WHERE 1=1
            #if ($P.dept_name and $P.dept_name != '')
                AND dept_name = :dept_name
            #end
        GROUP BY
            bussiness,
            indicator_id,
            indicator_name,
            warning_level
        ORDER BY
            bussiness,
            indicator_id
    </database>


</sqlMap>
