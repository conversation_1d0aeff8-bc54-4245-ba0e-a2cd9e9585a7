<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="DepartmentDuties" description="部门职责管理">
    <!-- 数据插入 -->
    <database id="insertDepartmentDuties" resultMap="java.lang.Integer" description="新增部门职责">
        INSERT INTO department_duties (task_name, description, department_id, department_name)
        VALUES(:task_name, :description, :department_id, :department_name)
    </database>
    <!-- 支持单个ID删除 -->
    <database id="deleteDepartmentDuties" resultMap="java.lang.Integer" description="删除部门职责">
        DELETE FROM department_duties
        WHERE id = :id
    </database>

    <!-- 新增：支持批量删除多个ID -->
    <database id="batchDeleteDepartmentDuties" resultMap="java.lang.Integer" description="批量删除部门职责">
        DELETE FROM department_duties
        WHERE id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            :id
        </foreach>
    </database>
    <database id="selectDepartmentDuties" resultMap="java.lang.Map" description="查询所有部门职责">
        SELECT id,task_name, description, created_at, department_id, department_name
        FROM department_duties
    </database>

    <database id="selectDepartmentDutiesByDepartmentId" resultMap="java.lang.Map"
              description="通过部门查询所有部门职责">
        SELECT id,task_name, description, created_at, department_id, department_name
        FROM department_duties
        where department_id = :department_id
    </database>

    <database id="selectCountDepartmentDutiesByDepartmentId" resultMap="java.lang.Map"
              description="通过部门查询所有部门职责条数">
        SELECT COUNT(*)
        FROM department_duties
        where department_id = :department_id
    </database>

    <database id="selectDepartmentDutiesById" resultMap="java.lang.Map" description="通过id查询部门职责">
        SELECT id,task_name, description, created_at, department_id, department_name
        FROM department_duties
        WHERE id=:id
    </database>
    <database id="selectDepartmentDutiesCount" resultMap="java.lang.Integer" description="查询部门职责数据总条数">
        SELECT COUNT(*) as count
        FROM department_duties
    </database>
    <database id="updateDepartmentDuties" resultMap="java.lang.Integer" description="更新部门职责">
        UPDATE department_duties
        SET task_name =:task_name, description =:description, department_id =:department_id, department_name
        =:department_name
        WHERE id =:id
    </database>

    <database id="selectDepartmentPersonTask" resultMap="java.lang.Integer" description="部门人员岗位信息展示">
        SELECT ta.participant_id AS participantId, su.userName, pt.id AS taskId, pt.type, ta.position,
        ta.content, pt.name AS taskName
        FROM task_assignment ta
        INNER JOIN project_task pt
        ON ta.ref_id = pt.id
        AND ta.type = pt.type
        LEFT JOIN sys_user su
        ON ta.participant_id = su.nickName
        WHERE ta.participant_id = :participantId
    </database>

    <database id="countDepartmentPersonTask" resultMap="java.lang.Integer" description="部门人员岗位信息总数统计">
        SELECT COUNT(*)
        FROM task_assignment ta
        INNER JOIN project_task pt
        ON ta.ref_id = pt.id
        AND ta.type = pt.type
        LEFT JOIN sys_user su
        ON ta.participant_id = su.nickName
        WHERE ta.participant_id = :participantId
    </database>

</sqlMap>
