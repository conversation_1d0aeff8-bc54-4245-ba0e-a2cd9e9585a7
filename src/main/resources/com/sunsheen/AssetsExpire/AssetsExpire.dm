<sqlMap namespace="AssetsExpire" description="资产到期管理">

    <!-- 插入资产到期记录 -->
    <database id="insertAssetsExpire" resultMap="java.lang.Integer" description="新增资产到期信息">
        INSERT INTO assets_expire
        (department, department_code, name, employee_id, employee_status,
         fiscal_type_code, fiscal_type_name, education_type_code, education_type_name,
         asset_name, storage_date, service_months, quantity, amount)
        VALUES(:department, :department_code, :name, :employee_id, :employee_status,
               :fiscal_type_code, :fiscal_type_name, :education_type_code, :education_type_name,
               :asset_name, :storage_date, :service_months, :quantity, :amount);
    </database>

    <!-- 查询资产到期记录列表 -->
    <database id="selectAssetsExpire" resultMap="java.util.HashMap" description="按所有字段动态查询资产到期信息">
        SELECT *
        FROM assets_expire ae
        WHERE 1 = 1
        #if ($P.id and $P.id != '')
            AND ae.id = :id
        #end
        #if ($P.department and $P.department != '')
            AND ae.department LIKE CONCAT('%', :department, '%')
        #end
        #if ($P.department_code and $P.department_code != '')
            AND ae.department_code = :department_code
        #end
        #if ($P.name and $P.name != '')
            AND ae.name LIKE CONCAT('%', :name, '%')
        #end
        #if ($P.employee_id and $P.employee_id != '')
            AND ae.employee_id = :employee_id
        #end
        #if ($P.employee_status and $P.employee_status != '')
            AND ae.employee_status = :employee_status
        #end
        #if ($P.fiscal_type_code and $P.fiscal_type_code != '')
            AND ae.fiscal_type_code = :fiscal_type_code
        #end
        #if ($P.fiscal_type_name and $P.fiscal_type_name != '')
            AND ae.fiscal_type_name LIKE CONCAT('%', :fiscal_type_name, '%')
        #end
        #if ($P.education_type_code and $P.education_type_code != '')
            AND ae.education_type_code = :education_type_code
        #end
        #if ($P.education_type_name and $P.education_type_name != '')
            AND ae.education_type_name LIKE CONCAT('%', :education_type_name, '%')
        #end
        #if ($P.asset_name and $P.asset_name != '')
            AND ae.asset_name LIKE CONCAT('%', :asset_name, '%')
        #end
        #if ($P.storage_date and $P.storage_date != '')
            AND ae.storage_date = :storage_date
        #end
        #if ($P.service_months and $P.service_months != '')
            AND ae.service_months = :service_months
        #end
    </database>

    <!-- 查询资产到期记录列表总数 -->
        <database id="selectAssetsExpireCount" resultMap="java.util.HashMap" description="按所有字段动态查询资产到期信息总数">
            SELECT COUNT(*) as count
            FROM assets_expire ae
            WHERE 1 = 1
            #if ($P.id and $P.id != '')
                AND ae.id = :id
            #end
            #if ($P.department and $P.department != '')
                AND ae.department LIKE CONCAT('%', :department, '%')
            #end
            #if ($P.department_code and $P.department_code != '')
                AND ae.department_code = :department_code
            #end
            #if ($P.name and $P.name != '')
                AND ae.name LIKE CONCAT('%', :name, '%')
            #end
            #if ($P.employee_id and $P.employee_id != '')
                AND ae.employee_id = :employee_id
            #end
            #if ($P.employee_status and $P.employee_status != '')
                AND ae.employee_status = :employee_status
            #end
            #if ($P.fiscal_type_code and $P.fiscal_type_code != '')
                AND ae.fiscal_type_code = :fiscal_type_code
            #end
            #if ($P.fiscal_type_name and $P.fiscal_type_name != '')
                AND ae.fiscal_type_name LIKE CONCAT('%', :fiscal_type_name, '%')
            #end
            #if ($P.education_type_code and $P.education_type_code != '')
                AND ae.education_type_code = :education_type_code
            #end
            #if ($P.education_type_name and $P.education_type_name != '')
                AND ae.education_type_name LIKE CONCAT('%', :education_type_name, '%')
            #end
            #if ($P.asset_name and $P.asset_name != '')
                AND ae.asset_name LIKE CONCAT('%', :asset_name, '%')
            #end
            #if ($P.storage_date and $P.storage_date != '')
                AND ae.storage_date = :storage_date
            #end
            #if ($P.service_months and $P.service_months != '')
                AND ae.service_months = :service_months
            #end
        </database>

    <!-- 根据DepartmentCode分类查询 -->
    <database id="sumByDepartmentCode" resultMap="java.util.HashMap" description="按部门编号分类统计总数量与总金额（按金额倒序）">
        SELECT
            department_code,
            MAX(department) AS department,
            SUM(quantity) AS total_quantity,
            SUM(amount) AS total_amount
        FROM assets_expire
        GROUP BY department_code
        ORDER BY total_amount DESC
    </database>

    <!-- 删除资产到期记录 -->
    <database id="deleteAssetsExpire" resultMap="java.lang.Integer" description="删除资产到期信息">
        DELETE FROM assets_expire
        WHERE id = :id
    </database>

</sqlMap>
