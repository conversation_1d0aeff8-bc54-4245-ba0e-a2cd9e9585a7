<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="DataCollection" description="数据采集">

<database id="updateAssetTeacherStats" resultMap="java.util.HashMap" description="资产和教师信息统计">
  <![CDATA[
    {call proc_etl_stat()}
  ]]>
</database>

<database id="updateKeyan" resultMap="java.util.HashMap" description="科研信息统计">
  <![CDATA[
    {call proc_research_etl_stat()}
  ]]>
</database>

<database id="getList" resultMap="java.util.HashMap" description="获取统计数据">
  <![CDATA[
    SELECT DISTINCT * FROM etl_stat
  ]]>
</database>

<database id="getEtlStatTableData" resultMap="java.util.HashMap" description="获取etl_stat按部门和表分组的数据量和时间">
  <![CDATA[
    SELECT
      dept_name,
      table_name,
      table_comment,
      counts,
      update_time
    FROM etl_stat
    ORDER BY dept_name, table_name, update_time DESC
  ]]>
</database>

<database id="getEtlStatByTableName" resultMap="java.util.HashMap" description="根据表名获取etl_stat数据">
  <![CDATA[
    SELECT
      id,
      dept_name,
      table_name,
      table_comment,
      counts,
      update_time
    FROM etl_stat
    WHERE table_name = :tableName
    ORDER BY update_time DESC
  ]]>
</database>

<database id="getEtlStatSummary" resultMap="java.util.HashMap" description="获取etl_stat汇总统计">
  <![CDATA[
    SELECT
      COUNT(DISTINCT table_name) as total_tables,
      SUM(counts) as total_records,
      MAX(update_time) as latest_update,
      MIN(update_time) as earliest_update
    FROM etl_stat
  ]]>
</database>

<database id="getEtlStatDailyTrend" resultMap="java.util.HashMap" description="获取每日数据采集趋势统计">
  <![CDATA[
    SELECT
      DATE(update_time) as date,
      COUNT(DISTINCT table_name) as table_count,
      SUM(counts) as total_counts,
      COUNT(*) as job_count,
      COUNT(DISTINCT dept_name) as dept_count
    FROM etl_stat
    WHERE update_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY DATE(update_time)
    ORDER BY date DESC
  ]]>
</database>

</sqlMap>
