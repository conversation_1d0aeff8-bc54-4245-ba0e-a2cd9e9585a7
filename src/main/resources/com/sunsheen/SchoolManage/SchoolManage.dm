<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="SchoolManage" description="学校信息管理表">

   <database id="insertSchool" resultMap="java.lang.Integer" description="新增学校信息管理表数据">
    	INSERT INTO dict_university
        (ID, name)
        VALUES(:ID, :name);
    </database>


 <database id="selectSchool" resultMap="java.lang.Integer" description="查询用户">
               SELECT *
               FROM dict_university
            </database>

    <database id="selectCounts" resultMap="java.util.HashMap" description="查询学校信息">
        SELECT
        count(*) as total
        FROM
        dict_university
    </database>

        <database id="selectById" resultMap="java.util.HashMap" description="查询学校信息">
            SELECT
            ID ,
            name
            FROM
            dict_university
            where ID = :ID OR name = :name
        </database>

<database id="deleteSchool" resultMap="java.lang.Integer" description="删除学校信息管理表数据">
            delete from
                dict_university
            where ID = :schoolId
    </database>


<database id = "updateSchool" resultMap="java.util.Map" description="修改学校信息">
    UPDATE dict_university
    SET ID=:ID,name=:name
    WHERE ID=:oldId;
    </database>

    </sqlMap>