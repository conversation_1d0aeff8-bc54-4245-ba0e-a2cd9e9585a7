<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="PurchaseException" description="采购异常">

    <database id="getGetArgException" resultMap="java.util.Integer" description="应论证未论证异常分页数据">
                SELECT
                    project_code,
                    project_name,
                    p_dept_id,
                    budget,
                    is_argumentation,
                    argumentation_file_paths,
                    audit_status,
                    create_time
                FROM pms_exception_argumentation
                ORDER BY
                    create_time DESC
                LIMIT :pageSize OFFSET :offset
            </database>

        <database id="countArgException" resultMap="java.lang.Integer" description="应论证未论证异常总数">
                SELECT COUNT(*)
                FROM (SELECT
                          project_code,
                          project_name,
                          p_dept_id,
                          budget,
                          is_argumentation,
                          argumentation_file_paths,
                          audit_status,
                          create_time
                      FROM pms_exception_argumentation) As a
            </database>

        <database id="getArgExceptionByDept" resultMap="java.util.Integer" description="按部门应论证未论证异常分页数据">
                SELECT
                    project_code,
                    project_name,
                    p_dept_id,
                    budget,
                    is_argumentation,
                    argumentation_file_paths,
                    audit_status,
                    create_time
                FROM pms_exception_argumentation
                WHERE p_dept_id = :p_dept_id
                ORDER BY
                    create_time DESC
                LIMIT :pageSize OFFSET :offset
            </database>

        <database id="countArgExceptionByDept" resultMap="java.lang.Integer" description="按部门应论证未论证异常总数">
                SELECT COUNT(*)
                    FROM (SELECT
                              project_code,
                              project_name,
                              p_dept_id,
                              budget,
                              is_argumentation,
                              argumentation_file_paths,
                              audit_status,
                              create_time
                          FROM pms_exception_argumentation
                          WHERE p_dept_id = :p_dept_id) As a
            </database>


            <database id="getMajorException" resultMap="java.util.Integer" description="三重一大预警异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            pg_id,
                            p_dept_id,
                            budget,
                            is_college_important,
                            dept_meeting_log,
                            is_school_important,
                            school_meeting_log,
                            audit_status,
                            create_time
                        FROM pms_exception_major_decision
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countMajorException" resultMap="java.lang.Integer" description="三重一大预警异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  pg_id,
                                  p_dept_id,
                                  budget,
                                  is_college_important,
                                  dept_meeting_log,
                                  is_school_important,
                                  school_meeting_log,
                                  audit_status,
                                  create_time
                              FROM pms_exception_major_decision) As a
                    </database>

                <database id="getMajorExceptionByDept" resultMap="java.util.Integer" description="按部门三重一大预警异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            pg_id,
                            p_dept_id,
                            budget,
                            is_college_important,
                            dept_meeting_log,
                            is_school_important,
                            school_meeting_log,
                            audit_status,
                            create_time
                        FROM pms_exception_major_decision
                        WHERE p_dept_id = :p_dept_id
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countMajorExceptionByDept" resultMap="java.lang.Integer" description="按部门三重一大预警异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  pg_id,
                                  p_dept_id,
                                  budget,
                                  is_college_important,
                                  dept_meeting_log,
                                  is_school_important,
                                  school_meeting_log,
                                  audit_status,
                                  create_time
                              FROM pms_exception_major_decision
                              WHERE p_dept_id = :p_dept_id) As a
                    </database>
</sqlMap>
