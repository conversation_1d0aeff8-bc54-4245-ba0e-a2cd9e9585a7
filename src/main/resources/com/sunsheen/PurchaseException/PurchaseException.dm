<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="PurchaseException" description="采购异常">

    <database id="getGetArgException" resultMap="java.util.Integer" description="应论证未论证异常分页数据">
                SELECT
                    project_code,
                    project_name,
                    p_dept_id,
                    budget,
                    is_argumentation,
                    argumentation_file_paths,
                    audit_status,
                    create_time
                FROM pms_exception_argumentation
                ORDER BY
                    create_time DESC
                LIMIT :pageSize OFFSET :offset
            </database>

        <database id="countArgException" resultMap="java.lang.Integer" description="应论证未论证异常总数">
                SELECT COUNT(*)
                FROM (SELECT
                          project_code,
                          project_name,
                          p_dept_id,
                          budget,
                          is_argumentation,
                          argumentation_file_paths,
                          audit_status,
                          create_time
                      FROM pms_exception_argumentation) As a
            </database>

        <database id="getArgExceptionByDept" resultMap="java.util.Integer" description="按部门应论证未论证异常分页数据">
                SELECT
                    project_code,
                    project_name,
                    p_dept_id,
                    budget,
                    is_argumentation,
                    argumentation_file_paths,
                    audit_status,
                    create_time
                FROM pms_exception_argumentation
                WHERE p_dept_id = :p_dept_id
                ORDER BY
                    create_time DESC
                LIMIT :pageSize OFFSET :offset
            </database>

        <database id="countArgExceptionByDept" resultMap="java.lang.Integer" description="按部门应论证未论证异常总数">
                SELECT COUNT(*)
                    FROM (SELECT
                              project_code,
                              project_name,
                              p_dept_id,
                              budget,
                              is_argumentation,
                              argumentation_file_paths,
                              audit_status,
                              create_time
                          FROM pms_exception_argumentation
                          WHERE p_dept_id = :p_dept_id) As a
            </database>


            <database id="getMajorException" resultMap="java.util.Integer" description="三重一大预警异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            pg_id,
                            p_dept_id,
                            budget,
                            is_college_important,
                            dept_meeting_log,
                            is_school_important,
                            school_meeting_log,
                            audit_status,
                            create_time
                        FROM pms_exception_major_decision
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countMajorException" resultMap="java.lang.Integer" description="三重一大预警异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  pg_id,
                                  p_dept_id,
                                  budget,
                                  is_college_important,
                                  dept_meeting_log,
                                  is_school_important,
                                  school_meeting_log,
                                  audit_status,
                                  create_time
                              FROM pms_exception_major_decision) As a
                    </database>

                <database id="getMajorExceptionByDept" resultMap="java.util.Integer" description="按部门三重一大预警异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            pg_id,
                            p_dept_id,
                            budget,
                            is_college_important,
                            dept_meeting_log,
                            is_school_important,
                            school_meeting_log,
                            audit_status,
                            create_time
                        FROM pms_exception_major_decision
                        WHERE p_dept_id = :p_dept_id
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countMajorExceptionByDept" resultMap="java.lang.Integer" description="按部门三重一大预警异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  pg_id,
                                  p_dept_id,
                                  budget,
                                  is_college_important,
                                  dept_meeting_log,
                                  is_school_important,
                                  school_meeting_log,
                                  audit_status,
                                  create_time
                              FROM pms_exception_major_decision
                              WHERE p_dept_id = :p_dept_id) As a
                    </database>

            <database id="getGetAcceptanceStandard" resultMap="java.util.Integer" description="验收标准降低异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            p_dept_id,
                            question,
                            is_rectification_involved,
                            is_rectification_completed,
                            acceptance_result,
                            date,
                            warning_type,
                            create_time
                        FROM pms_exception_acceptance_standard_lower
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countAcceptanceStandard" resultMap="java.lang.Integer" description="验收标准降低异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  p_dept_id,
                                  question,
                                  is_rectification_involved,
                                  is_rectification_completed,
                                  acceptance_result,
                                  date,
                                  warning_type,
                                  create_time
                              FROM pms_exception_acceptance_standard_lower) As a
                    </database>

                <database id="getAcceptanceStandardByDept" resultMap="java.util.Integer" description="按部门验收标准降低异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            p_dept_id,
                            question,
                            is_rectification_involved,
                            is_rectification_completed,
                            acceptance_result,
                            date,
                            warning_type,
                            create_time
                        FROM pms_exception_acceptance_standard_lower
                        WHERE p_dept_id = :p_dept_id
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countAcceptanceStandardByDept" resultMap="java.lang.Integer" description="按部门验收标准降低异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  p_dept_id,
                                  question,
                                  is_rectification_involved,
                                  is_rectification_completed,
                                  acceptance_result,
                                  date,
                                  warning_type,
                                  create_time
                              FROM pms_exception_acceptance_standard_lower
                              WHERE p_dept_id = :p_dept_id) As a
                    </database>

            <database id="getGetIllegalWarranty" resultMap="java.util.Integer" description="违规退还质保金异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            p_dept_id,
                            contract_code,
                            contract_type_code,
                            warranty_period,
                            refund_apply_date,
                            performance_confirm_status,
                            confirm_file_scan,
                            warning_type,
                            create_time
                        FROM pms_exception_illegal_warranty_refund
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countIllegalWarranty" resultMap="java.lang.Integer" description="违规退还质保金异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  p_dept_id,
                                  contract_code,
                                  contract_type_code,
                                  warranty_period,
                                  refund_apply_date,
                                  performance_confirm_status,
                                  confirm_file_scan,
                                  warning_type,
                                  create_time
                              FROM pms_exception_illegal_warranty_refund) As a
                    </database>

                <database id="getIllegalWarrantyByDept" resultMap="java.util.Integer" description="按部门违规退还质保金异常分页数据">
                        SELECT
                            project_code,
                            project_name,
                            p_dept_id,
                            contract_code,
                            contract_type_code,
                            warranty_period,
                            refund_apply_date,
                            performance_confirm_status,
                            confirm_file_scan,
                            warning_type,
                            create_time
                        FROM pms_exception_illegal_warranty_refund
                        WHERE p_dept_id = :p_dept_id
                        ORDER BY
                            create_time DESC
                        LIMIT :pageSize OFFSET :offset
                    </database>

                <database id="countIllegalWarrantyByDept" resultMap="java.lang.Integer" description="按部门违规退还质保金异常总数">
                        SELECT COUNT(*)
                        FROM (SELECT
                                  project_code,
                                  project_name,
                                  p_dept_id,
                                  contract_code,
                                  contract_type_code,
                                  warranty_period,
                                  refund_apply_date,
                                  performance_confirm_status,
                                  confirm_file_scan,
                                  warning_type,
                                  create_time
                              FROM pms_exception_illegal_warranty_refund
                              WHERE p_dept_id = :p_dept_id) As a
                    </database>

                    <database id="getYellowWarningCount" resultType="java.lang.Long" description="获取黄色预警数量">
                        SELECT COUNT(*)
                        FROM pms_exception_warning_list
                        WHERE warning_level = 0
                    </database>

                    <database id="getRedWarningCount" resultType="java.lang.Long" description="获取红色预警数量">
                        SELECT COUNT(*)
                        FROM pms_exception_warning_list
                        WHERE warning_level = 1
                    </database>
</sqlMap>
