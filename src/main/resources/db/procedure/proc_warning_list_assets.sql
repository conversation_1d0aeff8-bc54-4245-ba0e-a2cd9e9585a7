CREATE PROCEDURE `proc_update_warning_list`()
BEGIN
    -- 关闭已过时的资产数量警告，并更新 end_time、update_time 和 closed_by
UPDATE warning_list wl
SET isClosed = 1,
    end_time = NOW(),
    update_time = NOW(),
    closed_by = '系统'
WHERE warning_id LIKE 'QUANT_%'
  AND NOT EXISTS (
    SELECT 1 FROM assets_over_regis aor WHERE aor.user_code = SUBSTRING(wl.warning_id, 7)
)
  AND (closed_by IS NULL OR closed_by = '系统');

-- 插入或更新资产数量异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('QUANT_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        CASE
            WHEN quantity > (
                SELECT threshold FROM indicator_level
                WHERE indicator_id = 'zc003' AND warning_level = 2 LIMIT 1
            ) THEN 2
            ELSE 1
            END AS warning_level,
        department_name,
        user_code,
        'zc003',
        '个人资产数量异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_over_regis
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = NOW(),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != '系统' THEN closed_by
                                         WHEN VALUES(isClosed) = 1 THEN '系统'
                                         ELSE closed_by
                             END;


    -- 关闭部门变动的过时警告，并更新 end_time、update_time 和 closed_by
UPDATE warning_list wl
SET isClosed = 1,
    end_time = NOW(),
    update_time = NOW(),
    closed_by = '系统'
WHERE warning_id LIKE 'DEPARTMENT_%'
  AND NOT EXISTS (
    SELECT 1 FROM assets_dept_change adc WHERE adc.user_code = SUBSTRING(wl.warning_id, 11)
)
  AND (closed_by IS NULL OR closed_by = '系统');

-- 插入或更新资产使用单位异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('DEPARTMENT_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        1,
        new_department,
        user_code,
        'zc004',
        '资产使用单位异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_dept_change
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = NOW(),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != '系统' THEN closed_by
                                         WHEN isClosed = 0 THEN '系统' -- 提前计算逻辑条件
                                         ELSE closed_by
                             END;


    -- 关闭人员状态的过时警告，并更新 end_time、update_time 和 closed_by
UPDATE warning_list wl
SET isClosed = 1,
    end_time = NOW(),
    update_time = NOW(),
    closed_by = '系统'
WHERE warning_id LIKE 'MOVE_%'
  AND NOT EXISTS (
    SELECT 1 FROM assets_teacher_leave atl WHERE atl.user_code = SUBSTRING(wl.warning_id, 6)
)
  AND (closed_by IS NULL OR closed_by = '系统');

-- 插入或更新人员离职状态异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('MOVE_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        1,
        department_name,
        user_code,
        'zc005',
        '资产登记人员状态异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_teacher_leave
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = VALUES(update_time),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != '系统' THEN closed_by
                                         WHEN VALUES(isClosed) = 1 THEN '系统'
                                         ELSE closed_by
                             END;

END