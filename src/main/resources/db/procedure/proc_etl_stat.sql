CREATE PROCEDURE `proc_etl_stat`()
    COMMENT '统计从其他系统收集来的数据数目'
BEGIN
    -- 声明变量
    DECLARE v_job_id VARCHAR(50);
    DECLARE v_start_time DATETIME;
    DECLARE v_current_time DATETIME;
    DECLARE v_asset_total INT DEFAULT 0;
    DECLARE v_teacher_total INT DEFAULT 0;

    -- 初始化时间和作业ID
    SET v_start_time = NOW();
    SET v_job_id = CONCAT('ASSET_STATS_', DATE_FORMAT(v_start_time, '%Y%m%d%H%i%s'));  -- 使用时间戳创建唯一ID

    -- 1. 统计资产登记表(asset_Registration)
    SET v_current_time = NOW();
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ar'),
    '资产管理',
    'asset_Registration',
    '资产登记表',
    COUNT(*),
    v_current_time
FROM
    asset_Registration;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ar'));

    -- 2. 统计资产到期表(assets_expire)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ae'),
    '资产管理',
    'assets_expire',
    '资产到期表',
    COUNT(*),
    v_current_time
FROM
    assets_expire;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ae'));

    -- 3. 统计资产登记表备份(assets_registration_bak)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_arb'),
    '资产管理',
    'assets_registration_bak',
    '资产登记表备份',
    COUNT(*),
    v_current_time
FROM
    assets_registration_bak;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_arb'));

    -- 4. 统计资产统计表(assets_statistic)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_as'),
    '资产管理',
    'assets_statistic',
    '资产统计表',
    COUNT(*),
    v_current_time
FROM
    assets_statistic;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_as'));

    -- 5. 统计资产审批流程数据表(assets_workflow_data)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_awd'),
    '资产管理',
    'assets_workflow_data',
    '资产审批流程数据表',
    COUNT(*),
    v_current_time
FROM
    assets_workflow_data;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_awd'));

    -- 6. 统计资产审批流程历史表(assets_workflow_his)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_awh'),
    '资产管理',
    'assets_workflow_his',
    '资产审批流程历史表',
    COUNT(*),
    v_current_time
FROM
    assets_workflow_his;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_awh'));

    -- 7. 统计教师信息表(teacher_infomation)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ti'),
    '人事管理',
    'teacher_infomation',
    '教师信息表',
    COUNT(*),
    v_current_time
FROM
    teacher_infomation;

SET v_teacher_total = (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ti'));

    -- 8. 插入资产管理总计
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
VALUES (
           CONCAT(v_job_id, '_tot'),
           '资产管理',
           'all_asset_tables',
           '资产管理相关表合计',
           v_asset_total,
           v_current_time
       );


-- 返回作业ID
SELECT v_job_id AS job_id, v_asset_total AS asset_total, v_teacher_total AS teacher_total;
END
