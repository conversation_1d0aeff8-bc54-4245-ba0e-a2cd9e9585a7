CREATE PROCEDURE `proc_warning_upgrade`()
    COMMENT '根据规则将一阶预警中的相关预警升级为二阶预警'
BEGIN
    DECLARE v_warning_id VARCHAR(100);
    DECLARE v_indicator_id VARCHAR(100);
    DECLARE v_new_warning_id VARCHAR(100);
    DECLARE done INT DEFAULT FALSE;
    DECLARE yellow_count INT DEFAULT 0;
    DECLARE red_count INT DEFAULT 0;
    DECLARE direct_red_count INT DEFAULT 0;
    DECLARE close_count INT DEFAULT 0;

    -- 查找需要升级到二阶黄色的一阶预警
    DECLARE cur_yellow CURSOR FOR
SELECT wl.warning_id, wl.indicator_id
FROM warning_list wl
         JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '1' OR il.warning_level = 1)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
  AND NOT EXISTS (
    SELECT 1 FROM warning_2ed_level wls
    WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
);

-- 查找需要直接升级到二阶红色的一阶预警
DECLARE cur_direct_red CURSOR FOR
SELECT wl.warning_id, wl.indicator_id
FROM warning_list wl
         JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '2' OR il.warning_level = 2)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
  AND NOT EXISTS (
    SELECT 1 FROM warning_2ed_level wls
    WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
);


DECLARE cur_red CURSOR FOR
SELECT wls.warning_id, wls.indicator_id
FROM warning_2ed_level wls
         JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
         JOIN indicator_level il ON wls.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '2' OR il.warning_level = 2)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wls.warning_level = '1'
  AND wls.isClosed = 0
  AND wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold;


DECLARE cur_close_second CURSOR FOR
SELECT wls.warning_id
FROM warning_2ed_level wls
         JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
WHERE wls.isClosed = 0
  AND wl.isClosed = 1;

DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

START TRANSACTION;


OPEN cur_yellow;

yellow_loop: LOOP
        FETCH cur_yellow INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE yellow_loop;
END IF;

        SET v_new_warning_id = UUID();


INSERT INTO warning_2ed_level
(warning_id, indicator_id, indicator_name, warning_level,
 update_time, status, original_warning_id,
 bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
 start_time, end_time)
SELECT
    v_new_warning_id, wl.indicator_id, wl.indicator_name, '1',
    NOW(), 'active', wl.warning_id,
    wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
    wl.start_time, wl.end_time
FROM warning_list wl
WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


UPDATE warning_list
SET status = 'upgraded',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_new_warning_id, v_indicator_id, '0', '1',
     NOW(), '自动升级', '一阶预警未处理时间超过阈值，自动升级为二阶黄色预警', 'system');

SET yellow_count = yellow_count + 1;
END LOOP;

CLOSE cur_yellow;
SET done = FALSE;


OPEN cur_direct_red;

direct_red_loop: LOOP
        FETCH cur_direct_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE direct_red_loop;
END IF;

        SET v_new_warning_id = UUID();


INSERT INTO warning_2ed_level
(warning_id, indicator_id, indicator_name, warning_level,
 update_time, status, original_warning_id,
 bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
 start_time, end_time)
SELECT
    v_new_warning_id, wl.indicator_id, wl.indicator_name, '2',
    NOW(), 'active', wl.warning_id,
    wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
    wl.start_time, wl.end_time
FROM warning_list wl
WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


UPDATE warning_list
SET status = 'upgraded_to_red_directly',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_new_warning_id, v_indicator_id, '0', '2',
     NOW(), '自动升级', '一阶预警未处理时间超过红色预警阈值，直接升级为二阶红色预警', 'system');

SET direct_red_count = direct_red_count + 1;
END LOOP;

CLOSE cur_direct_red;
SET done = FALSE;


OPEN cur_red;

red_loop: LOOP
        FETCH cur_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE red_loop;
END IF;


UPDATE warning_2ed_level
SET warning_level = '2',
    update_time = NOW(),
    status = 'upgraded_to_red'
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_warning_id, v_indicator_id, '1', '2',
     NOW(), '自动升级', '二阶黄色预警未处理时间超过阈值，自动升级为二阶红色预警', 'system');

SET red_count = red_count + 1;
END LOOP;

CLOSE cur_red;
SET done = FALSE;


OPEN cur_close_second;

close_loop: LOOP
        FETCH cur_close_second INTO v_warning_id;
        IF done THEN
            LEAVE close_loop;
END IF;


UPDATE warning_2ed_level
SET isClosed = 1,
    status = 'closed_with_first_level',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;

SET close_count = close_count + 1;
END LOOP;

CLOSE cur_close_second;

COMMIT;


SELECT
    yellow_count AS yellow_upgraded,
    direct_red_count AS directly_to_red_upgraded,
    red_count AS yellow_to_red_upgraded,
    close_count AS second_level_closed,
    (yellow_count + direct_red_count + red_count + close_count) AS total_processed;

END