DELIMITER $$

CREATE PROCEDURE proc_insert_purchase_exception()
BEGIN
    -- 处理 pms_project_info 表
    INSERT INTO pms_exception_over_standard (
        project_code, project_name, pg_id, p_dept_id, audit_status, catalog_id, reference, amount, unit, unit_price, total_price, purcharse_amount, create_time
    )
    SELECT
        pi.AppliProCode AS project_code,
        pi.ProName AS project_name,
        pg.pg_id,
        pi.p_deptId AS p_dept_id,
        pi.p_status AS audit_status,
        pg.catalog_id,
        pg.reference,
        pg.amount,
        pg.unit,
        pg.unit_price,
        pg.total_price,
        pg.purcharse_amount,
        NOW()
    FROM pms_project_info pi
    JOIN pms_project_goods pg ON pi.AppliProCode = pg.AppliProCode
    WHERE (pg.unit_price > 5000 OR pg.amount > 10)
      AND pi.p_status != '通过';

    INSERT INTO pms_exception_over_standard (
        project_code, project_name, pg_id, p_dept_id, audit_status, catalog_id, reference, amount, unit, unit_price, total_price, purcharse_amount, create_time
    )
    SELECT
        pi.AppliProCode AS project_code,
        pi.ProName AS project_name,
        pg.pg_id,
        pi.p_deptId AS p_dept_id,
        pi.p_status AS audit_status,
        pg.catalog_id,
        pg.reference,
        pg.amount,
        pg.unit,
        pg.unit_price,
        pg.total_price,
        pg.purcharse_amount,
        NOW()
    FROM pms_project_info pi
    JOIN pms_project_goods pg ON pi.AppliProCode = pg.AppliProCode
    WHERE (pg.unit_price > 5000 OR pg.amount > 10)
      AND pi.p_status = '通过';
END$$

DELIMITER ;