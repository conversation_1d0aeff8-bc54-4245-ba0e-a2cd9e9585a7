CREATE PROCEDURE `proc_capture_asset_exceptions`()
BEGIN
    -- 清空异常表
TRUNCATE TABLE assets_over_regis;
TRUNCATE TABLE assets_dept_change;
TRUNCATE TABLE assets_teacher_leave;

-- 插入个人资产超量异常数据，只取符合最大 `threshold` 的值
INSERT INTO assets_over_regis (
    user_name, user_code, department_name, department_code, quantity, amount, ranking
)
SELECT
    MAX(ar.user_name),
    ar.user_code,
    MAX(ar.user_department_name),
    MAX(ar.user_department_code),
    SUM(ar.quantity),
    SUM(ar.amount),
    DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
FROM asset_Registration ar
         JOIN (
    -- 先计算用户的 `SUM(quantity)`
    SELECT ar.user_code, SUM(ar.quantity) AS total_quantity
    FROM asset_Registration ar
    GROUP BY ar.user_code
) AS aggregated_quantity ON aggregated_quantity.user_code = ar.user_code
         JOIN (
    -- 计算最大 `threshold`
    SELECT il.indicator_id, il.threshold
    FROM indicator_level il
    WHERE il.indicator_id = 'zc003' AND il.supervisor_level=1 and il.warning_level='1'
#     ORDER BY il.threshold DESC
#         LIMIT 1
) AS max_threshold ON aggregated_quantity.total_quantity > max_threshold.threshold
GROUP BY ar.user_code;

-- 插入部门变动异常数据
INSERT INTO assets_dept_change (
    old_department, old_department_code, new_department, new_department_code,
    user_name, user_code, quantity, amount, ranking
)
SELECT
    MAX(ar.user_department_name),
    MAX(ar.user_department_code),
    MAX(ti.department),
    MAX(ti.department_code),
    MAX(ar.user_name),
    ar.user_code,
    SUM(ar.quantity),
    SUM(ar.amount),
    DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
FROM asset_Registration ar
         JOIN teacher_infomation ti ON ti.log_account = ar.user_code
WHERE LOWER(REGEXP_REPLACE(ar.user_department_name, '\\（.*?\\）', ''))
    != LOWER(REGEXP_REPLACE(ti.department, '\\（.*?\\）', ''))
GROUP BY ar.user_code;

-- 插入离职人员异常数据
INSERT INTO assets_teacher_leave (
    user_name, user_code, department_name, department_code, state, quantity, amount, ranking
)
SELECT
    MAX(ar.user_name),
    ar.user_code,
    MAX(ar.user_department_name),
    MAX(ar.user_department_code),
    '已离职/已退休',
    SUM(ar.quantity),
    SUM(ar.amount),
    DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
FROM asset_Registration ar
WHERE NOT EXISTS (
    SELECT 1 FROM teacher_infomation ti WHERE ar.user_code = ti.log_account
)
GROUP BY ar.user_code;
END