CREATE  PROCEDURE 'proc_warning_list_update'()
    COMMENT '1.数据筛选和动态匹配: 从 assets_workflow_data 表中筛选符合条件的记录，业务停留时间 (stay_hours) 达到一定阈值。 \n             2.动态从 indicator_level 表中提取对应的 indicator_id 和 indicator_name 等字段，确保警告信息精准匹配。 \n             3.预警规则计算: 根据 stay_hours 与 indicator_level.threshold 的比较，动态计算警告级别 (warning_level)。 判断每条记录属于哪种预警等级，例如 1 或 2。 \n             4.自动插入或更新: 将筛选出的记录插入到 warning_list 表中，如果记录已存在，则更新相关字段。 更新的字段包括停留时间 (value)、警告等级 (warning_level)、更新时间 (update_time)、以及关闭状态 (isClosed)。 \n             5.关闭状态计算: 根据 assets_workflow_his 表的历史记录判断预警是否关闭 (isClosed)，分为以下情况： 如果记录没有历史信息或审核状态发生变化，则标记为关闭 (isClosed = 1)。 否则标记为未关闭 (isClosed = 0)。 \n             6.事务处理: 使用事务 (START TRANSACTION 和 COMMIT) 确保所有数据操作是原子化的，避免因某些操作失败导致的数据不一致。 \n             7.返回结果: 最后通过 SELECT COUNT(*) 返回更新的警告记录数量，方便后续监控或分析。'
BEGIN
    -- 定义标志变量，用于标记游标是否完成遍历
    DECLARE done INT DEFAULT FALSE;

    -- 定义变量，用于存储从游标中提取的数据
    DECLARE v_warning_id VARCHAR(100);
    DECLARE v_indicator_id VARCHAR(100);
    DECLARE v_business_type VARCHAR(100);
    DECLARE v_using_department VARCHAR(255);
    DECLARE v_document_number VARCHAR(100);
    DECLARE v_stay_hours INT;
    DECLARE v_warning_level VARCHAR(45);
    DECLARE v_indicator_name VARCHAR(45);

    -- 定义游标，动态查询需要处理的数据
    DECLARE cur_warning CURSOR FOR
SELECT
    CONCAT('ZC_FLOW_', aw.document_number) AS warning_id,
    il.indicator_id,
    aw.business_type,
    aw.using_department,
    aw.document_number,
    aw.stay_hours,
    il.warning_level,
    il.indicator_name
FROM assets_workflow_data aw
         LEFT JOIN indicator_level il
                   ON il.threshold <= aw.stay_hours
                       AND il.indicator_id = 'zc001'
                       AND aw.audit_status != '资产报废待终审'
WHERE aw.stay_hours IS NOT NULL
  AND il.warning_level IS NOT NULL;

-- 定义异常处理器，当游标遍历完成时，设置 done = TRUE
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 开始事务
START TRANSACTION;

-- 处理仍存在于 assets_workflow_data 表中的记录
OPEN cur_warning;

warning_loop: LOOP
        FETCH cur_warning INTO
            v_warning_id, v_indicator_id, v_business_type, v_using_department,
            v_document_number, v_stay_hours, v_warning_level, v_indicator_name;

        IF done THEN
            LEAVE warning_loop;
END IF;

        -- 插入或更新警告列表
INSERT INTO warning_list (
    warning_id, bussiness, process, status, value, warning_level, dept_name,
    bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
)
SELECT
    v_warning_id, '资产管理', v_business_type, '正常',
    v_stay_hours, v_warning_level, v_using_department,
    v_document_number, v_indicator_id, v_indicator_name,
    NOW(), aw.start_time, aw.update_time,0,NULL
FROM assets_workflow_data aw
WHERE aw.document_number = v_document_number
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = NOW(),
                         isClosed = IF(isClosed = 0, VALUES(isClosed), isClosed), -- 只有 isClosed 仍为 0 时才更新
                         closed_by = IF(VALUES(isClosed) = 1 AND (closed_by IS NULL OR closed_by = '系统'), '系统', closed_by);
END LOOP;

    -- 关闭游标
CLOSE cur_warning;

-- 处理已经从 assets_workflow_data 消失的记录，但只更新 closed_by 为空或系统的记录
UPDATE warning_list wl
SET isClosed = 1,
    end_time = NOW(),
    update_time = NOW(),
    closed_by = '系统'
WHERE NOT EXISTS (
    SELECT 1 FROM assets_workflow_data aw
    WHERE aw.document_number = wl.bussiness_id
) AND indicator_id = 'zc001'
  AND (closed_by IS NULL OR closed_by = '系统');

-- 提交事务
COMMIT;

-- 返回更新的警告记录数
SELECT COUNT(*) AS updated_count FROM warning_list;
END