DELIMITER $$

CREATE PROCEDURE proc_insert_purchase_exception()
BEGIN
    -- 黄色预警：预算资金采购办公设备及家具，单价或数量超限，审核结果不是“通过”
    INSERT INTO pms_exception_over_standard (
        project_code, project_name, pg_id, p_dept_id, audit_status, catalog_id, reference, amount, unit, unit_price, total_price, purcharse_amount, create_time
    )
    SELECT
        pf.dep_num_id AS project_code,
        pf.proj_name AS project_name,
        pf.diding_detail_id AS pg_id,
        pf.p_deptId AS p_dept_id,
        pf.apply_responser_opinion AS audit_status,
        pg.catalog_id,
        pg.reference,
        pg.amount,
        pg.unit,
        pg.unit_price,
        pg.total_price,
        pg.purcharse_amount,
        NOW()
    FROM pms_file_declare pf
    JOIN pms_project_goods pg ON pf.diding_detail_id = pg.pg_id
    WHERE (pg.unit_price > 5000 OR pg.amount > 10) -- 假设阈值，实际可用配置表
      AND pf.apply_responser_opinion != '通过';

    -- 红色预警：超标配置申请审核结果为“通过”
    INSERT INTO pms_exception_over_standard (
        project_code, project_name, pg_id, p_dept_id, audit_status, catalog_id, reference, amount, unit, unit_price, total_price, purcharse_amount, create_time
    )
    SELECT
        pf.dep_num_id AS project_code,
        pf.proj_name AS project_name,
        pf.diding_detail_id AS pg_id,
        pf.p_deptId AS p_dept_id,
        pf.apply_responser_opinion AS audit_status,
        pg.catalog_id,
        pg.reference,
        pg.amount,
        pg.unit,
        pg.unit_price,
        pg.total_price,
        pg.purcharse_amount,
        NOW()
    FROM pms_file_declare pf
    JOIN pms_project_goods pg ON pf.diding_detail_id = pg.pg_id
    WHERE (pg.unit_price > 5000 OR pg.amount > 10)
      AND pf.apply_responser_opinion = '通过';
END$$

DELIMITER ;