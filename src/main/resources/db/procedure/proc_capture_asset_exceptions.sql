CREATE PROCEDURE `proc_capture_asset_exceptions`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 发生错误时回滚事务
        ROLLBACK;
    END;

    START TRANSACTION;

    -- 清空异常表
    TRUNCATE TABLE assets_over_regis;
    TRUNCATE TABLE assets_dept_change;
    TRUNCATE TABLE assets_teacher_leave;

    -- 创建临时表来缓存预处理用户资产信息
    CREATE TEMPORARY TABLE temp_user_assets (
        user_code VARCHAR(100),
        user_name VARCHAR(100),
        user_department_name VARCHAR(255),
        user_department_code VARCHAR(100),
        total_quantity INT,
        total_amount DECIMAL(18, 2),
        PRIMARY KEY (user_code)
    );

    -- 插入预处理数据到临时表
    INSERT INTO temp_user_assets (user_code, user_name, user_department_name, user_department_code, total_quantity, total_amount)
    SELECT
        ar.user_code,
        MAX(ar.user_name),
        MAX(ar.user_department_name),
        MAX(ar.user_department_code),
        SUM(ar.quantity),
        SUM(ar.amount)
    FROM asset_Registration ar
    WHERE ar.quantity > 0 AND ar.amount >= 0
    GROUP BY ar.user_code;

    -- 获取阈值
    SET @threshold = (
        SELECT il.threshold
        FROM indicator_level il
        WHERE il.indicator_id = 'zc003' AND il.supervisor_level = 1 AND il.warning_level = '1'
        LIMIT 1
    );

    -- 插入个人资产超量异常数据
    INSERT INTO assets_over_regis (
        user_name, user_code, department_name, department_code, quantity, amount, ranking
    )
    SELECT
        tua.user_name,
        tua.user_code,
        tua.user_department_name,
        tua.user_department_code,
        tua.total_quantity,
        tua.total_amount,
        DENSE_RANK() OVER (ORDER BY tua.total_quantity DESC, tua.user_code ASC) AS quantity_rank
    FROM temp_user_assets tua
    WHERE tua.total_quantity > @threshold;

    -- 插入部门变动异常数据
    INSERT INTO assets_dept_change (
        old_department, old_department_code, new_department, new_department_code,
        user_name, user_code, quantity, amount, ranking
    )
    SELECT
        tua.user_department_name,
        tua.user_department_code,
        MAX(ti.department),
        MAX(ti.department_code),
        tua.user_name,
        tua.user_code,
        tua.total_quantity,
        tua.total_amount,
        DENSE_RANK() OVER (ORDER BY tua.total_quantity DESC, tua.user_code ASC) AS quantity_rank
    FROM temp_user_assets tua
    JOIN teacher_infomation ti
        ON LOWER(ti.log_account) = LOWER(tua.user_code)
    WHERE tua.user_department_name != ti.department
    GROUP BY tua.user_code, tua.user_name, tua.user_department_name, tua.user_department_code,
             tua.total_quantity, tua.total_amount;

    -- 插入离职人员异常数据
    INSERT INTO assets_teacher_leave (
        user_name, user_code, department_name, department_code, state, quantity, amount, ranking
    )
    SELECT
        tua.user_name,
        tua.user_code,
        tua.user_department_name,
        tua.user_department_code,
        '已离职/已退休',
        tua.total_quantity,
        tua.total_amount,
        DENSE_RANK() OVER (ORDER BY tua.total_quantity DESC, tua.user_code ASC) AS quantity_rank
    FROM temp_user_assets tua
    WHERE NOT EXISTS (
        SELECT 1
        FROM teacher_infomation ti
        WHERE LOWER(ti.log_account) = LOWER(tua.user_code)
    );

    -- 删除临时表
    DROP TEMPORARY TABLE IF EXISTS temp_user_assets;

    COMMIT;
END
