-- <PERSON><PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';


USE `fswp` ;

-- -----------------------------------------------------
-- Table `fswp`.`knowledge_Regulations`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`knowledge_Regulations` ;

CREATE TABLE IF NOT EXISTS `fswp`.`knowledge_Regulations` (
    `ID` VARCHAR(36) NOT NULL,
    `title` VARCHAR(100) NULL COMMENT '文件名称',
    `file_no` VARCHAR(45) NULL COMMENT '文件编号',
    `subject` VARCHAR(45) NULL COMMENT '主题',
    `file_addr` VARCHAR(60) NULL COMMENT '文件地址',
    `publisher` VARCHAR(45) NULL COMMENT '发布单位',
    `pub_date` DATE NULL COMMENT '发布日期',
    `valid_status` TINYINT NULL COMMENT '有效状态，缺省1（有效）',
    `expire_date` DATE NULL COMMENT '失效日期\n',
    `editor` VARCHAR(45) NULL COMMENT '修订人（用户对应的姓名）',
    `edit_date` DATE NULL COMMENT '编辑日期',
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB
    COMMENT = '法规知识库\n';


-- -----------------------------------------------------
-- Table `fswp`.`business_manage`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`business_manage` ;


CREATE TABLE `business_manage` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `orders` int DEFAULT NULL COMMENT '排序',
  `businessId` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务id',
  `businessName` varchar(36) DEFAULT NULL COMMENT '业务名称',
  `flows` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '子流程json',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='业务流程表';

-- -----------------------------------------------------
-- Table `fswp`.`knowledge_indicators`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`knowledge_indicators` ;

CREATE TABLE IF NOT EXISTS `fswp`.`knowledge_indicators` (
    `indicator_id` VARCHAR(36) NOT NULL COMMENT '指标ID',
    `indicator_name` VARCHAR(45) NULL COMMENT '指标名称\n',
    `describe` VARCHAR(60) NULL COMMENT '描述\n',
    `business` VARCHAR(45) NULL COMMENT '业务',
    `process` VARCHAR(45) NULL COMMENT '流程',
    `sub_process` VARCHAR(45) NULL COMMENT '子流程（环节）',
    `monitor_obj` VARCHAR(45) NULL COMMENT '监控对象',
    `risk_description` VARCHAR(500) NULL COMMENT '风险描述',
    `prevension_measure` VARCHAR(500) NULL COMMENT '防控措施',
    `refer_regu_describe` VARCHAR(200) NULL COMMENT '制度依据的描述',
    `type` VARCHAR(200) NULL COMMENT '指标类型',
    `editor` VARCHAR(45) NULL COMMENT '编辑人',
    `edit_date` DATE NULL COMMENT '修改日期\n',
    `valid_status` TINYINT NULL COMMENT '是否有效（缺省1 有效）',
    PRIMARY KEY (`indicator_id`))
    ENGINE = InnoDB
    COMMENT = '监管指标库';

CREATE UNIQUE INDEX `indicator_name_UNIQUE` ON `fswp`.`knowledge_indicators` (`indicator_id` ASC, `indicator_name` ASC) INVISIBLE;


-- -----------------------------------------------------
-- Table `fswp`.`warning_trace`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`warning_trace` ;

CREATE TABLE IF NOT EXISTS `fswp`.`warning_trace` (
    `warning_id` VARCHAR(36) NOT NULL,
    PRIMARY KEY (`warning_id`))
    ENGINE = InnoDB
    COMMENT = '预警跟踪表\n';


-- -----------------------------------------------------
-- Table `fswp`.`indicator_level`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`indicator_level` ;

CREATE TABLE IF NOT EXISTS `fswp`.`indicator_level` (
    `ID` VARCHAR(36) NOT NULL,
    `indicator_id` VARCHAR(45) NULL COMMENT '指标ID',
    `indicator_name` VARCHAR(45) NULL COMMENT '指标名称',
    `warning_level` VARCHAR(45) NULL COMMENT '告警级别，读字典',
    `describe` VARCHAR(50) NULL COMMENT '预警规则的描述，比如什么情况是黄色预警',
    `supervisor_level` INT NULL COMMENT '一阶监管/二阶监管\n',
    `sql` VARCHAR(500) NULL ,
    `threshold` INT NULL,
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB
    COMMENT = '指标配置表';


-- -----------------------------------------------------
-- Table `fswp`.`warning_stat`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`warning_stat` ;

CREATE TABLE IF NOT EXISTS `fswp`.`warning_stat` (
    `ID` INT NOT NULL,
    `indicator_id` VARCHAR(45) NULL,
    `indicator_name` VARCHAR(45) NULL,
    `count_total` INT NULL,
    `count_normal` INT NULL,
    `count_warning_level1` INT NULL,
    `count_warning_level2` INT NULL,
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`warning_2ed_level`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`warning_2ed_level` ;

CREATE TABLE IF NOT EXISTS `fswp`.`warning_2ed_level` (
    `id` INT NOT NULL,
    PRIMARY KEY (`id`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`warning_list`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`warning_list` ;

CREATE TABLE IF NOT EXISTS `fswp`.`warning_list` (
    `warning_id` VARCHAR(36) NOT NULL,
    `bussiness` VARCHAR(20) NULL,
    `process` VARCHAR(20) NULL,
    `sub_process` VARCHAR(20) NULL,
    `status` VARCHAR(45) NULL COMMENT '指标状态（字符型指标）',
    `value` DECIMAL NULL COMMENT '指标值（数值型指标）',
    `warning_level` VARCHAR(45) NULL COMMENT '告警级别',
    `bussiness_id` VARCHAR(36) NULL,
    `indicator_id` VARCHAR(36) NOT NULL,
    `indicator_name` VARCHAR(36) NOT NULL,
    `update_time` DATETIME NULL COMMENT '更新时间',
    `start_time` DATETIME NULL,
    `end_time` DATETIME NULL,
    `close_staus` TINYINT(1) NULL,
    `1st_supervision_tag` VARCHAR(45) NULL,
    `1st_supervision_comment` VARCHAR(45) NULL,
    `warning_stat_ID` INT NOT NULL,
    `warning_2ed_level_id` INT NOT NULL,
    PRIMARY KEY (`warning_id`))
    ENGINE = InnoDB
    COMMENT = '项目监管指标实例';


-- -----------------------------------------------------
-- Table `fswp`.`dict_university`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`dict_university` ;

CREATE TABLE IF NOT EXISTS `fswp`.`dict_university` (
    `ID` VARCHAR(36) NOT NULL,
    `name` VARCHAR(80) NULL,
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB
    COMMENT = '单位字典表';

CREATE UNIQUE INDEX `name_UNIQUE` ON `fswp`.`dict_university` (`name` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `fswp`.`etl_stat`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`etl_stat` ;

CREATE TABLE IF NOT EXISTS `fswp`.`etl_stat` (
    `id` VARCHAR(36) NOT NULL,
    `dept_name` VARCHAR(45) NULL COMMENT '数据来源部门',
    `table_name` VARCHAR(45) NULL COMMENT '源表表名',
    `table_comment` VARCHAR(45) NULL COMMENT '表中文名',
    `counts` INT NULL COMMENT '数据量统计',
    PRIMARY KEY (`id`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`assets_teacher_leave`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_teacher_leave` ;

CREATE TABLE IF NOT EXISTS `fswp`.`assets_teacher_leave` (
    `id` INT NOT NULL,
    PRIMARY KEY (`id`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`assets_teacher_change_dept`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_teacher_change_dept` ;

CREATE TABLE IF NOT EXISTS `fswp`.`assets_teacher_change_dept` (
    `ID` INT NOT NULL,
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB;

--
-- -- -----------------------------------------------------
-- -- Table `fswp`.`teacher_infomation`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`teacher_infomation` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`teacher_infomation` (
--     `ID` INT NOT NULL,
--     `assets_teacher_leave_id` INT NOT NULL,
--     `assets_teacher_change_dept_ID` INT NOT NULL,
--     PRIMARY KEY (`ID`))
--     ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`dict_department`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`dict_department` ;

CREATE TABLE IF NOT EXISTS `fswp`.`dict_department` (
    `ID` VARCHAR(36) NOT NULL,
    `university` VARCHAR(45) NULL,
    `dept_id` VARCHAR(45) NULL COMMENT '二级单位编码\n',
    `dept_name` VARCHAR(45) NULL COMMENT '二级单位名称',
    `sub_dept_id` VARCHAR(45) NULL COMMENT '三级单位编码\n',
    `sub_dept_name` VARCHAR(45) NULL COMMENT '三级单位名称',
    `area` VARCHAR(45) NULL COMMENT '校区\n',
    `editor` VARCHAR(45) NULL COMMENT '修改人',
    `edit_date` DATE NULL COMMENT '修改日期',
    `etl_stat_id` VARCHAR(36) NOT NULL,
    `teacher_infomation_ID` INT NOT NULL,
    PRIMARY KEY (`ID`))
    ENGINE = InnoDB
    COMMENT = '部门字典表';

CREATE UNIQUE INDEX `idx_dept` ON `fswp`.`dict_department` (`university` ASC, `dept_id` ASC, `sub_dept_id` ASC) INVISIBLE;

--
-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_statistic`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_statistic` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_statistic` (
--     `idassets_statistic1` INT NOT NULL,
--     PRIMARY KEY (`idassets_statistic1`))
--     ENGINE = InnoDB
--     COMMENT = '资产统计表1\n';


-- -----------------------------------------------------
-- Table `fswp`.`assets_change`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_change` ;

CREATE TABLE IF NOT EXISTS `fswp`.`assets_change` (
    id BIGINT NOT NULL AUTO_INCREMENT,
    asset_code VARCHAR(10) CHARACTER SET utf8mb4 NOT NULL COMMENT '资产编号',
    asset_name VARCHAR(60) CHARACTER SET utf8mb4 NOT NULL COMMENT '资产名称',
    unit_price DECIMAL(14,2) NOT NULL COMMENT '变前单价(元)',
    model_brand VARCHAR(1000) CHARACTER SET utf8mb4 NOT NULL COMMENT '型号/品牌',
    specification VARCHAR(60) CHARACTER SET utf8mb4 NOT NULL COMMENT '规格',
    manufacturer_origin_publisher VARCHAR(200) CHARACTER SET utf8mb4 NOT NULL COMMENT '厂家/产地/出版社/开发方',
    serial_number VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COMMENT '出厂号',
    storage_location VARCHAR(60) CHARACTER SET utf8mb4 NOT NULL COMMENT '存放地',
    status_code VARCHAR(2) CHARACTER SET utf8mb4 NOT NULL COMMENT '现状编号',
    status_name VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COMMENT '现状',
    original_unit_code VARCHAR(12) CHARACTER SET utf8mb4 NOT NULL COMMENT '原使用单位号',
    original_unit_name VARCHAR(60) CHARACTER SET utf8mb4 NOT NULL COMMENT '原使用单位',
    college_code VARCHAR(12) CHARACTER SET utf8mb4 NOT NULL COMMENT '学院编号',
    college_name VARCHAR(60) CHARACTER SET utf8mb4 NOT NULL COMMENT '学院名称',
    user_code VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COMMENT '使用人编号',
    user_name VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '使用人',
    change_user_name VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COMMENT '变动使用人',
    change_user_code VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COMMENT '变动人员编号',
    change_location_name VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '变动地名称',
    change_location_code VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COMMENT '变动地编号',
    change_document_number VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COMMENT '变动单据号',
    incoming_unit_code VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COMMENT '调入单位号',
    incoming_unit_name VARCHAR(40) CHARACTER SET utf8mb4 NOT NULL COMMENT '调入单位',
    change_file TEXT NOT NULL COMMENT '变动文件',
    application_date DATETIME NOT NULL COMMENT '申请日期',
    business_type_name VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COMMENT '业务类型',
    business_type_code VARCHAR(30) CHARACTER SET utf8mb4 NOT NULL COMMENT '业务类型号',
    change_amount DECIMAL(12,2) NOT NULL COMMENT '变动金额(元)',
    PRIMARY KEY (id)
);


--
-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_registration`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_registration` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_registration` (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID：唯一标识每一条资产记录',
--     quantity NUMERIC(18, 2) COMMENT '数量(台/件/套/栋/块)：资产数量',
--     asset_code NVARCHAR(10) COMMENT '资产编号：资产的唯一编号',
--     asset_name NVARCHAR(60) COMMENT '资产名称：资产的名称',
--     category_code NVARCHAR(10) COMMENT '教育部分类号：资产的教育部分类编号',
--     category_name NVARCHAR(60) COMMENT '教育部分类名：资产的教育部分类名称',
--     unit_price NUMERIC(14, 2) COMMENT '单价(元)：资产的单价',
--     model_brand NVARCHAR(1000) COMMENT '型号、品牌：资产的型号或品牌',
--     specification NVARCHAR(60) COMMENT '规格：资产的规格',
--     manufacturer NVARCHAR(200) COMMENT '厂家/产地/出版社/开发方：资产的厂家、产地、出版社或开发方',
--     factory_code NVARCHAR(30) COMMENT '出厂号/车辆识别号：资产的出厂号、车辆识别号、档案号或证书号',
--     storage_location NVARCHAR(60) COMMENT '存放地：资产的存放地点',
--     status_code NVARCHAR(2) COMMENT '现状编号：资产的现状编号（取值与字典XZ中的编号）',
--     status_name NVARCHAR(20) COMMENT '现状：资产的现状名称（取值与字典XZ中的名称）',
--     funding_subject_code NVARCHAR(2) COMMENT '经费科目号：经费科目编号（取值与字典JFKM中的编号）',
--     funding_subject_name NVARCHAR(20) COMMENT '经费科目：经费科目名称（取值与字典JFKM中的名称）',
--     original_asset_code NVARCHAR(20) COMMENT '原资产编号：原资产号（原设备号）',
--     usage_direction_name NVARCHAR(20) COMMENT '使用方向：资产的使用方向名称（取值与字典SYFX中的名称）',
--     asset_origin_code NVARCHAR(2) COMMENT '资产来源号：资产来源编号（取值与字典ZCLY中的编号）',
--     asset_origin_name NVARCHAR(20) COMMENT '资产来源：资产来源名称（取值与字典ZCLY中的名称）',
--     document_code NVARCHAR(20) COMMENT '单据号：资产相关的单据号',
--     national_standard_code NVARCHAR(10) COMMENT '国标分类号：资产的国标分类号（财政部分类号）',
--     national_standard_name NVARCHAR(60) COMMENT '国标分类名：资产的国标分类名（财政部分类名）',
--     storage_location_code NVARCHAR(30) COMMENT '存放地编号：资产存放地编号',
--     asset_category_code NVARCHAR(2) COMMENT '资产类别号：资产的类别编号（例如1：土地房屋，2：通用设备等）',
--     asset_category_name NVARCHAR(20) COMMENT '资产类别：资产类别名称（例如土地房屋、通用设备等）',
--     user_department_code NVARCHAR(12) COMMENT '使用单位号：使用单位编号（取hzxt_prd_dw中的末级单位号）',
--     user_department_name NVARCHAR(60) COMMENT '使用单位：使用单位名称（取hzxt_prd_dw中的末级单位名）',
--     supplier NVARCHAR(50) COMMENT '销售商/电话：供货商的名称及联系方式',
--     value_type_code NVARCHAR(2) COMMENT '价值类型号：资产的价值类型编号（字典JZLX代码）',
--     value_type_name NVARCHAR(20) COMMENT '价值类型：资产的价值类型名称（字典JZLX名称）',
--     invoice_number NVARCHAR(300) COMMENT '发票号：资产相关的发票号',
--     contract_number NVARCHAR(40) COMMENT '合同号：资产相关的合同号',
--     depreciation_years INT COMMENT '折旧年限(月)：资产的折旧年限（月数）',
--     purchase_form_code NVARCHAR(2) COMMENT '采购形式号：资产的采购形式编号（字典CGXS代码）',
--     purchase_form_name NVARCHAR(20) COMMENT '采购形式：资产的采购形式名称（字典CGXS名称）',
--     amount NUMERIC(18, 2) COMMENT '金额(元)：资产的金额',
--     financial_voucher NVARCHAR(60) COMMENT '财务凭单：与资产相关的财务凭单',
--     depreciation_method NVARCHAR(20) COMMENT '折旧方式：资产的折旧方式（字典ZJFS名称）',
--     financial_review_code NVARCHAR(2) COMMENT '财务审核号：资产的财务审核编号（字典ZWSH编号）',
--     financial_review_name NVARCHAR(20) COMMENT '财务审核：资产的财务审核名称（字典ZWSH名称）',
--     financial_reviewer NVARCHAR(30) COMMENT '财务审核人：资产的财务审核人',
--     accumulated_depreciation NUMERIC(18, 2) COMMENT '累计折旧额(元)：资产的累计折旧额',
--     monthly_depreciation NUMERIC(18, 2) COMMENT '月折旧额(元)：资产的月折旧额',
--     net_value NUMERIC(18, 2) COMMENT '净值(元)：资产的净值',
--     financial_entry_date DATETIME COMMENT '财务入账日期：资产的财务入账日期',
--     depreciation_year_month INT COMMENT '折旧年月：资产的折旧年月',
--     depreciation_date DATETIME COMMENT '折旧日期：资产的折旧日期',
--     asset_entry_date DATETIME COMMENT '资产入账日期：资产的入库时间',
--     user_code NVARCHAR(20) COMMENT '使用人编号：资产使用人的编号',
--     user_name NVARCHAR(50) COMMENT '使用人：资产使用人的名称',
--     fiscal_asset_code NVARCHAR(30) COMMENT '财政资产号：财政资产号',
--     financial_allocation NUMERIC(18, 2) COMMENT '财政拨款(元)：财政拨款金额',
--     depreciation_method_code INT COMMENT '折旧方法：折旧方法编号（字典ZJFF编号）',
--     flag INT COMMENT '标志位：标识资产状态或其他特定状态，设置为911',
--     audit_status NVARCHAR(30) DEFAULT '资产登记已终审' COMMENT '审核状态：资产的审核状态，用汉字表示，默认值为“资产登记已终审”',
--     license_plate NVARCHAR(20) COMMENT '车牌号：资产相关的车牌号',
--     engine_number VARCHAR(1000) COMMENT '发动机号：资产相关的发动机号'
-- ) COMMENT = '资产登记表：存储和管理资产信息，包括数量、分类、价值、折旧等数据';
--

-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_workflow_data`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_workflow_data` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_workflow_data` (
--     id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     business_type VARCHAR(100) COMMENT '业务类型',
--     document_number VARCHAR(100) COMMENT '单据号',
--     audit_status VARCHAR(100) COMMENT '审核状态',
--     using_department VARCHAR(255) COMMENT '使用单位',
--     asset_name VARCHAR(255) COMMENT '资产名称',
--     initiator VARCHAR(100) COMMENT '发起人',
--     total_quantity INT COMMENT '总数量',
--     total_amount DECIMAL(10,2) COMMENT '总金额',
--     task_executor VARCHAR(100) COMMENT '任务执行人',
--     stay_hours INT COMMENT '业务停留时间（单位：小时）',
--     start_time DATETIME COMMENT '发起时间',
--     update_time DATETIME COMMENT '更新时间')
--     ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产审批流程数据表';


-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_workflow_trace`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_workflow_trace` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_workflow_trace` (
--     `id` VARCHAR(36) NOT NULL,
--     PRIMARY KEY (`id`))
--     ENGINE = InnoDB;

--
-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_workflow_his`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_workflow_his` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_workflow_his` (
--     id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     business_type VARCHAR(100) COMMENT '业务类型',
--     document_number VARCHAR(100) COMMENT '单据号',
--     audit_status VARCHAR(100) COMMENT '审核状态',
--     using_department VARCHAR(255) COMMENT '使用单位',
--     asset_name VARCHAR(255) COMMENT '资产名称',
--     initiator VARCHAR(100) COMMENT '发起人',
--     total_quantity INT COMMENT '总数量',
--     total_amount DECIMAL(10,2) COMMENT '总金额',
--     stay_hours INT COMMENT '整体审批时间（单位：小时）',
--     start_time DATETIME COMMENT '发起时间',
--     end_time DATETIME COMMENT '结束时间'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产审批流程历史表';
--
-- --
-- -- -----------------------------------------------------
-- -- Table `fswp`.`assets_expire`
-- -- -----------------------------------------------------
-- DROP TABLE IF EXISTS `fswp`.`assets_expire` ;
--
-- CREATE TABLE IF NOT EXISTS `fswp`.`assets_expire` (
--     id INT AUTO_INCREMENT PRIMARY KEY,
--     department VARCHAR(255) COMMENT '部门',
--     department_code VARCHAR(50) COMMENT '部门编号',
--     name VARCHAR(100) COMMENT '姓名',
--     employee_id VARCHAR(50) COMMENT '人员工号',
--     employee_status VARCHAR(50) COMMENT '人员状态',
--     fiscal_type_code VARCHAR(50) COMMENT '财政分类号',
--     fiscal_type_name VARCHAR(255) COMMENT '财政分类名',
--     education_type_code VARCHAR(50) COMMENT '教育部分类编号',
--     education_type_name VARCHAR(255) COMMENT '教育部分类名称',
--     asset_name VARCHAR(255) COMMENT '名称',
--     storage_date DATE COMMENT '入库日期',
--     service_months INT COMMENT '使用年限(月份)',
--     quantity DOUBLE COMMENT '数量',
--     amount DECIMAL(15,2) COMMENT '金额'
-- )COMMENT '资产到期表' ENGINE = InnoDB;
--
CREATE TABLE IF NOT EXISTS `fswp`.`assets_workflow_trace` (
    `id` VARCHAR(36) NOT NULL,
    PRIMARY KEY (`id`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `fswp`.`assets_workflow_his`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_workflow_his` ;

CREATE TABLE IF NOT EXISTS `fswp`.`assets_workflow_his` (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    business_type VARCHAR(100) COMMENT '业务类型',
    document_number VARCHAR(100) COMMENT '单据号',
    audit_status VARCHAR(100) COMMENT '审核状态',
    using_department VARCHAR(255) COMMENT '使用单位',
    asset_name VARCHAR(255) COMMENT '资产名称',
    initiator VARCHAR(100) COMMENT '发起人',
    total_quantity INT COMMENT '总数量',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    stay_hours INT COMMENT '整体审批时间（单位：小时）',
    start_time DATETIME COMMENT '发起时间',
    end_time DATETIME COMMENT '结束时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产审批流程历史表';


-- -----------------------------------------------------
-- Table `fswp`.`assets_expire`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_expire` ;

CREATE TABLE IF NOT EXISTS `fswp`.`assets_expire` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    department VARCHAR(255) COMMENT '部门',
    department_code VARCHAR(50) COMMENT '部门编号',
    name VARCHAR(100) COMMENT '姓名',
    employee_id VARCHAR(50) COMMENT '人员工号',
    employee_status VARCHAR(50) COMMENT '人员状态',
    fiscal_type_code VARCHAR(50) COMMENT '财政分类号',
    fiscal_type_name VARCHAR(255) COMMENT '财政分类名',
    education_type_code VARCHAR(50) COMMENT '教育部分类编号',
    education_type_name VARCHAR(255) COMMENT '教育部分类名称',
    asset_name VARCHAR(255) COMMENT '名称',
    storage_date DATE COMMENT '入库日期',
    service_months INT COMMENT '使用年限(月份)',
    quantity DOUBLE COMMENT '数量',
    amount DECIMAL(15,2) COMMENT '金额'
)COMMENT '资产到期表' ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `fswp`.`assets_teacher_leave`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_teacher_leave`;
create table assets_teacher_leave
(
    id              int auto_increment comment '编号',
    user_name       varchar(50) null comment '用户名',
    user_code       varchar(20) null comment '用户编号',
    department_name varchar(60) null comment '部门名称',
    department_code varchar(12) null comment '部门编号',
    state           varchar(45) null comment '人员状态',
    asset_name      varchar(60) null comment '资产名称',
    jy_category_name     varchar(60) null comment '教育资产类别名称',
    jy_category_code     varchar(10) null comment '教育资产类别编号',
    cz_category_name     varchar(60)    null comment '财政资产类别名称',
    cz_category_code     varchar(10)    null comment '财政资产类别编号',
    quantity        decimal(18, 2)         null comment '数量',
    amount          decimal(18, 2)          null comment '金额',
    constraint assets_teacher_leave_pk
        primary key (id)
)
    comment '人员离职资产异常表';


-- -----------------------------------------------------
-- Table `fswp`.`assets_dept_change`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_dept_change`;
create table assets_dept_change
(
    id             int  auto_increment   comment '编号',
    old_department varchar(60) null comment '原部门',
    old_department_code varchar(12) null comment '原部门编号',
    new_department varchar(60)  null comment '新部门',
    new_department_code varchar(12) null comment '新部门编号',
    user_name      varchar(50) null comment '用户名',
    user_code      varchar(20) null comment '用户编号',
    state          varchar(45) null comment '人员状态',
    asset_name     varchar(60) null comment '资产名称',
    jy_category_name     varchar(60) null comment '教育资产类别名称',
    jy_category_code     varchar(10) null comment '教育资产类别编号',
    cz_category_name     varchar(60)    null comment '财政资产类别名称',
    cz_category_code     varchar(10)    null comment '财政资产类别编号',
    quantity       decimal(18, 2)         null comment '数量',
    amount         decimal(18, 2)          null comment '金额',
    constraint assets_dept_change_pk
        primary key (id)
)
    comment '部门调动资产异常表';

-- -----------------------------------------------------
-- Table `fswp`.`assets_over_regis`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `fswp`.`assets_over_regis`;
create table assets_over_regis
(
    id              int auto_increment comment '编号',
    user_name       varchar(50) null comment '用户名',
    user_code       varchar(20) null comment '用户编号',
    department_name varchar(60) null comment '部门名称',
    department_code varchar(12) null comment '部门编号',
    state           varchar(45) null comment '人员状态',
    asset_name      varchar(60) null comment '资产名称',
    jy_category_name     varchar(60) null comment '教育资产类别名称',
    jy_category_code     varchar(10) null comment '教育资产类别编号',
    cz_category_name     varchar(60)    null comment '财政资产类别名称',
    cz_category_code     varchar(10)    null comment '财政资产类别编号',
    quantity        decimal(18, 2)         null comment '数量',
    amount          decimal(18, 2)          null comment '金额',
    constraint assets_over_regis_pk
        primary key (id)
)
    comment '个人资产数量产异常表';

DROP TABLE IF EXISTS `warning_2ed_level`;
CREATE TABLE `warning_2ed_level` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `warning_id` varchar(36) NOT NULL,
                                     `bussiness` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '归类（如资产管理）',
                                     `process` varchar(20) DEFAULT NULL,
                                     `sub_process` varchar(20) DEFAULT NULL,
                                     `status` varchar(45) DEFAULT NULL COMMENT '指标状态（字符型指标）',
                                     `value` decimal(10,0) DEFAULT NULL COMMENT '指标值（数值型指标）',
                                     `warning_level` varchar(45) DEFAULT NULL COMMENT '告警级别',
                                     `dept_name` varchar(45) DEFAULT NULL,
                                     `bussiness_id` varchar(36) DEFAULT NULL,
                                     `indicator_id` varchar(36) NOT NULL,
                                     `indicator_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称（审批如耗时、数量）',
                                     `original_warning_id` varchar(36) DEFAULT NULL COMMENT '原始一阶预警ID',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `isClosed` int NOT NULL DEFAULT '0',
                                     `start_time` datetime DEFAULT NULL,
                                     `end_time` datetime DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `idx_warning_list_second_level` (`warning_level`)
) ENGINE=InnoDB AUTO_INCREMENT=805 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目监管二阶指标实例';

CREATE TABLE asset_usage_rate (
    -- 基础标识字段
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_code VARCHAR(20) NOT NULL COMMENT '资产编号，关联asset_Registration表',
    asset_name VARCHAR(100) COMMENT '资产名称',
    asset_category VARCHAR(50) COMMENT '资产类别',
    department_name VARCHAR(100) COMMENT '使用部门',
    
    -- 统计周期
    statistics_year INT NOT NULL COMMENT '统计年度',
    statistics_month INT COMMENT '统计月份（可选）',
    
    -- 核心机时统计字段
    annual_rated_hours INT DEFAULT 1600 COMMENT '年额定机时数（小时）',
    annual_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年使用机时数（小时）',
    annual_shared_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年共享机时数（小时）',
    annual_fault_downtime_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年故障停机机时数（小时）',
    
    -- 服务统计字段
    teaching_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '教学服务机时数',
    research_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '科研服务机时数',
    external_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '对外服务机时数',
    training_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '培训服务机时数',
    
    -- 收费统计字段
    internal_fee_income DECIMAL(15,2) DEFAULT 0 COMMENT '校内收费收入（元）',
    external_fee_income DECIMAL(15,2) DEFAULT 0 COMMENT '对外服务收费收入（元）',
    maintenance_cost DECIMAL(15,2) DEFAULT 0 COMMENT '维护运行成本（元）',
    
    -- 人员配备字段
    operator_count INT DEFAULT 0 COMMENT '操作人员数量',
    certified_operator_count INT DEFAULT 0 COMMENT '持证上岗人员数量',
    technician_count INT DEFAULT 0 COMMENT '技术人员数量',
    
    -- 计算结果字段
    effective_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '有效使用机时数',
    usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '使用率（%）',
    effective_usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '有效使用率（%）',
    shared_rate DECIMAL(5,2) DEFAULT 0 COMMENT '共享率（%）',
    
    -- 设备分类字段
    is_large_equipment TINYINT(1) DEFAULT 0 COMMENT '是否为大型设备（≥10万元）',
    is_precious_equipment TINYINT(1) DEFAULT 0 COMMENT '是否为贵重设备（≥40万元）',
    equipment_value DECIMAL(15,2) COMMENT '设备价值（元）',
    purchase_date DATE COMMENT '购置日期',
    
    -- 共享管理字段
    is_shared_platform TINYINT(1) DEFAULT 0 COMMENT '是否纳入共享平台',
    shared_platform_name VARCHAR(100) COMMENT '共享平台名称',
    open_time_per_week INT DEFAULT 0 COMMENT '每周开放时间（小时）',
    
    -- 效益评价字段
    annual_benefit_score DECIMAL(5,2) DEFAULT 0 COMMENT '年度效益评分',
    benefit_evaluation_level VARCHAR(20) COMMENT '效益评价等级',
    
    -- 审计字段
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 索引设计
    UNIQUE KEY uk_asset_year_month (asset_code, statistics_year, statistics_month),
    INDEX idx_department_year (department_name, statistics_year),
    INDEX idx_usage_level (usage_level),
    INDEX idx_large_equipment (is_large_equipment),
    INDEX idx_precious_equipment (is_precious_equipment),
    INDEX idx_shared_platform (is_shared_platform),
    INDEX idx_warning_status (warning_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产使用率统计表（符合教育部政策要求）';

SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;

create definer = application@`%` procedure proc_etl_stat() comment '统计从其他系统收集来的数据数目'
BEGIN
    -- 声明变量
    DECLARE v_job_id VARCHAR(50);
    DECLARE v_start_time DATETIME;
    DECLARE v_current_time DATETIME;
    DECLARE v_asset_total INT DEFAULT 0;
    DECLARE v_teacher_total INT DEFAULT 0;

    -- 初始化时间和作业ID
    SET v_start_time = NOW();
    SET v_job_id = CONCAT('ASSET_STATS_', DATE_FORMAT(v_start_time, '%Y%m%d%H%i%s'));  -- 使用时间戳创建唯一ID

    -- 1. 统计资产登记表(asset_Registration)
    SET v_current_time = NOW();
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ar'),
    '资产管理',
    'asset_Registration',
    '资产登记表',
    COUNT(*),
    v_current_time
FROM
    asset_Registration;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ar'));

    -- 2. 统计资产到期表(assets_expire)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ae'),
    '资产管理',
    'assets_expire',
    '资产到期表',
    COUNT(*),
    v_current_time
FROM
    assets_expire;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ae'));

    -- 3. 统计资产登记表备份(assets_registration_bak)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_arb'),
    '资产管理',
    'assets_registration_bak',
    '资产登记表备份',
    COUNT(*),
    v_current_time
FROM
    assets_registration_bak;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_arb'));

    -- 4. 统计资产统计表(assets_statistic)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_as'),
    '资产管理',
    'assets_statistic',
    '资产统计表',
    COUNT(*),
    v_current_time
FROM
    assets_statistic;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_as'));

    -- 5. 统计资产审批流程数据表(assets_workflow_data)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_awd'),
    '资产管理',
    'assets_workflow_data',
    '资产审批流程数据表',
    COUNT(*),
    v_current_time
FROM
    assets_workflow_data;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_awd'));

    -- 6. 统计资产审批流程历史表(assets_workflow_his)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_awh'),
    '资产管理',
    'assets_workflow_his',
    '资产审批流程历史表',
    COUNT(*),
    v_current_time
FROM
    assets_workflow_his;

SET v_asset_total = v_asset_total + (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_awh'));

    -- 7. 统计教师信息表(teacher_infomation)
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
SELECT
    CONCAT(v_job_id, '_ti'),
    '人事管理',
    'teacher_infomation',
    '教师信息表',
    COUNT(*),
    v_current_time
FROM
    teacher_infomation;

SET v_teacher_total = (SELECT counts FROM etl_stat WHERE id = CONCAT(v_job_id, '_ti'));

    -- 8. 插入资产管理总计
INSERT INTO etl_stat (id, dept_name, table_name, table_comment, counts, update_time)
VALUES (
           CONCAT(v_job_id, '_tot'),
           '资产管理',
           'all_asset_tables',
           '资产管理相关表合计',
           v_asset_total,
           v_current_time
       );


-- 返回作业ID
SELECT v_job_id AS job_id, v_asset_total AS asset_total, v_teacher_total AS teacher_total;
END;

create
definer = application@`%` procedure proc_warning_upgrade() comment '根据规则将一阶预警中的相关预警升级为二阶预警'
BEGIN
    DECLARE v_warning_id VARCHAR(100);
    DECLARE v_indicator_id VARCHAR(100);
    DECLARE v_new_warning_id VARCHAR(100);
    DECLARE done INT DEFAULT FALSE;
    DECLARE yellow_count INT DEFAULT 0;
    DECLARE red_count INT DEFAULT 0;
    DECLARE direct_red_count INT DEFAULT 0;
    DECLARE close_count INT DEFAULT 0;

    -- 查找需要升级到二阶黄色的一阶预警
    DECLARE cur_yellow CURSOR FOR
SELECT wl.warning_id, wl.indicator_id
FROM warning_list wl
         JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '1' OR il.warning_level = 1)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
  AND NOT EXISTS (
    SELECT 1 FROM warning_2ed_level wls
    WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
);

-- 查找需要直接升级到二阶红色的一阶预警
DECLARE cur_direct_red CURSOR FOR
SELECT wl.warning_id, wl.indicator_id
FROM warning_list wl
         JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '2' OR il.warning_level = 2)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
  AND NOT EXISTS (
    SELECT 1 FROM warning_2ed_level wls
    WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
);


DECLARE cur_red CURSOR FOR
SELECT wls.warning_id, wls.indicator_id
FROM warning_2ed_level wls
         JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
         JOIN indicator_level il ON wls.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
    AND (il.warning_level = '2' OR il.warning_level = 2)
    AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
WHERE wls.warning_level = '1'
  AND wls.isClosed = 0
  AND wl.isClosed = 0
  AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold;


DECLARE cur_close_second CURSOR FOR
SELECT wls.warning_id
FROM warning_2ed_level wls
         JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
WHERE wls.isClosed = 0
  AND wl.isClosed = 1;

DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

START TRANSACTION;


OPEN cur_yellow;

yellow_loop: LOOP
        FETCH cur_yellow INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE yellow_loop;
END IF;

        SET v_new_warning_id = UUID();


INSERT INTO warning_2ed_level
(warning_id, indicator_id, indicator_name, warning_level,
 update_time, status, original_warning_id,
 bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
 start_time, end_time)
SELECT
    v_new_warning_id, wl.indicator_id, wl.indicator_name, '1',
    NOW(), 'active', wl.warning_id,
    wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
    wl.start_time, wl.end_time
FROM warning_list wl
WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


UPDATE warning_list
SET status = 'upgraded',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_new_warning_id, v_indicator_id, '0', '1',
     NOW(), '自动升级', '一阶预警未处理时间超过阈值，自动升级为二阶黄色预警', 'system');

SET yellow_count = yellow_count + 1;
END LOOP;

CLOSE cur_yellow;
SET done = FALSE;


OPEN cur_direct_red;

direct_red_loop: LOOP
        FETCH cur_direct_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE direct_red_loop;
END IF;

        SET v_new_warning_id = UUID();


INSERT INTO warning_2ed_level
(warning_id, indicator_id, indicator_name, warning_level,
 update_time, status, original_warning_id,
 bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
 start_time, end_time)
SELECT
    v_new_warning_id, wl.indicator_id, wl.indicator_name, '2',
    NOW(), 'active', wl.warning_id,
    wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
    wl.start_time, wl.end_time
FROM warning_list wl
WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


UPDATE warning_list
SET status = 'upgraded_to_red_directly',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_new_warning_id, v_indicator_id, '0', '2',
     NOW(), '自动升级', '一阶预警未处理时间超过红色预警阈值，直接升级为二阶红色预警', 'system');

SET direct_red_count = direct_red_count + 1;
END LOOP;

CLOSE cur_direct_red;
SET done = FALSE;


OPEN cur_red;

red_loop: LOOP
        FETCH cur_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE red_loop;
END IF;


UPDATE warning_2ed_level
SET warning_level = '2',
    update_time = NOW(),
    status = 'upgraded_to_red'
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


INSERT INTO warning_trace
(trace_id, warning_id, indicator_id, original_level, new_level,
 change_time, change_type, change_reason, operator)
VALUES
    (UUID(), v_warning_id, v_indicator_id, '1', '2',
     NOW(), '自动升级', '二阶黄色预警未处理时间超过阈值，自动升级为二阶红色预警', 'system');

SET red_count = red_count + 1;
END LOOP;

CLOSE cur_red;
SET done = FALSE;


OPEN cur_close_second;

close_loop: LOOP
        FETCH cur_close_second INTO v_warning_id;
        IF done THEN
            LEAVE close_loop;
END IF;


UPDATE warning_2ed_level
SET isClosed = 1,
    status = 'closed_with_first_level',
    update_time = NOW()
WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;

SET close_count = close_count + 1;
END LOOP;

CLOSE cur_close_second;

COMMIT;


SELECT
    yellow_count AS yellow_upgraded,
    direct_red_count AS directly_to_red_upgraded,
    red_count AS yellow_to_red_upgraded,
    close_count AS second_level_closed,
    (yellow_count + direct_red_count + red_count + close_count) AS total_processed;

END;

create
    definer = application@`%` procedure proc_capture_asset_exceptions()
BEGIN
    -- 清空异常表
    TRUNCATE TABLE assets_over_regis;
    TRUNCATE TABLE assets_dept_change;
    TRUNCATE TABLE assets_teacher_leave;

    -- 插入个人资产超量异常数据，只取符合最大 `threshold` 的值
    INSERT INTO assets_over_regis (
        user_name, user_code, department_name, department_code, quantity, amount, ranking
    )
    SELECT
        MAX(ar.user_name),
        ar.user_code,
        MAX(ar.user_department_name),
        MAX(ar.user_department_code),
        SUM(ar.quantity),
        SUM(ar.amount),
        DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
    FROM asset_Registration ar
             JOIN (
        -- 先计算用户的 `SUM(quantity)`
        SELECT ar.user_code, SUM(ar.quantity) AS total_quantity
        FROM asset_Registration ar
        GROUP BY ar.user_code
    ) AS aggregated_quantity ON aggregated_quantity.user_code = ar.user_code
             JOIN (
        -- 计算最大 `threshold`
        SELECT il.indicator_id, il.threshold
        FROM indicator_level il
        WHERE il.indicator_id = 'zc003' AND il.supervisor_level=1
        ORDER BY il.threshold DESC
        LIMIT 1
    ) AS max_threshold ON aggregated_quantity.total_quantity > max_threshold.threshold
    GROUP BY ar.user_code;

    -- 插入部门变动异常数据
    INSERT INTO assets_dept_change (
        old_department, old_department_code, new_department, new_department_code,
        user_name, user_code, quantity, amount, ranking
    )
    SELECT
        MAX(ar.user_department_name),
        MAX(ar.user_department_code),
        MAX(ti.unit),
        MAX(ti.unit_code),
        MAX(ar.user_name),
        ar.user_code,
        SUM(ar.quantity),
        SUM(ar.amount),
        DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
    FROM asset_Registration ar
             JOIN teacher_infomation ti ON ti.log_account = ar.user_code
    WHERE LOWER(REGEXP_REPLACE(ar.user_department_name, '\\（.*?\\）', ''))
              != LOWER(REGEXP_REPLACE(ti.unit, '\\（.*?\\）', ''))
    GROUP BY ar.user_code;

    -- 插入离职人员异常数据
    INSERT INTO assets_teacher_leave (
        user_name, user_code, department_name, department_code, state, quantity, amount, ranking
    )
    SELECT
        MAX(ar.user_name),
        ar.user_code,
        MAX(ar.user_department_name),
        MAX(ar.user_department_code),
        '已离职/已退休',
        SUM(ar.quantity),
        SUM(ar.amount),
        DENSE_RANK() OVER (ORDER BY SUM(ar.quantity) DESC, ar.user_code ASC) AS quantity_rank
    FROM asset_Registration ar
    WHERE NOT EXISTS (
        SELECT 1 FROM teacher_infomation ti WHERE ar.user_code = ti.log_account
    )
    GROUP BY ar.user_code;
END;

create
    definer = application@`%` procedure proc_update_warning_list()
BEGIN
    -- 关闭已过时的资产数量警告，并更新 end_time、update_time 和 closed_by
    UPDATE warning_list wl
    SET isClosed = 1,
        end_time = NOW(),
        update_time = NOW(),
        closed_by = 'system'
    WHERE warning_id LIKE 'QUANT_%'
      AND NOT EXISTS (
        SELECT 1 FROM assets_over_regis aor WHERE aor.user_code = SUBSTRING(wl.warning_id, 7)
    )
      AND (closed_by IS NULL OR closed_by = 'system');

    -- 插入或更新资产数量异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('QUANT_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        CASE
            WHEN quantity > (
                SELECT threshold FROM indicator_level
                WHERE indicator_id = 'zc003' AND warning_level = 2 LIMIT 1
            ) THEN 2
            ELSE 1
            END AS warning_level,
        department_name,
        user_code,
        'zc003',
        '个人资产数量异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_over_regis
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = NOW(),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != 'system' THEN closed_by
                                         WHEN VALUES(isClosed) = 1 THEN 'system'
                                         ELSE closed_by
                             END;


    -- 关闭部门变动的过时警告，并更新 end_time、update_time 和 closed_by
    UPDATE warning_list wl
    SET isClosed = 1,
        end_time = NOW(),
        update_time = NOW(),
        closed_by = 'system'
    WHERE warning_id LIKE 'DEPARTMENT_%'
      AND NOT EXISTS (
        SELECT 1 FROM assets_dept_change adc WHERE adc.user_code = SUBSTRING(wl.warning_id, 11)
    )
      AND (closed_by IS NULL OR closed_by = 'system');

    -- 插入或更新资产使用单位异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('DEPARTMENT_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        1,
        new_department,
        user_code,
        'zc004',
        '资产使用单位异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_dept_change
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = NOW(),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != 'system' THEN closed_by
                                         WHEN isClosed = 0 THEN 'system' -- 提前计算逻辑条件
                                         ELSE closed_by
                             END;


    -- 关闭人员状态的过时警告，并更新 end_time、update_time 和 closed_by
    UPDATE warning_list wl
    SET isClosed = 1,
        end_time = NOW(),
        update_time = NOW(),
        closed_by = 'system'
    WHERE warning_id LIKE 'MOVE_%'
      AND NOT EXISTS (
        SELECT 1 FROM assets_teacher_leave atl WHERE atl.user_code = SUBSTRING(wl.warning_id, 6)
    )
      AND (closed_by IS NULL OR closed_by = 'system');

    -- 插入或更新人员离职状态异常预警
    INSERT INTO warning_list (
        warning_id, bussiness, process, status, value, warning_level, dept_name,
        bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
    )
    SELECT
        CONCAT('MOVE_', user_code),
        '资产管理',
        '资产登记',
        '正常',
        quantity,
        1,
        department_name,
        user_code,
        'zc005',
        '资产登记人员状态异常',
        NOW(),
        NOW(),
        NULL,
        0,
        NULL
    FROM assets_teacher_leave
    ON DUPLICATE KEY UPDATE
                         value = VALUES(value),
                         warning_level = VALUES(warning_level),
                         update_time = VALUES(update_time),
                         isClosed = CASE
                                        WHEN isClosed = 1 THEN isClosed
                                        ELSE VALUES(isClosed)
                             END,
                         closed_by = CASE
                                         WHEN closed_by IS NOT NULL AND closed_by != 'system' THEN closed_by
                                         WHEN VALUES(isClosed) = 1 THEN 'system'
                                         ELSE closed_by
                             END;

END;

create
    definer = application@`%` procedure proc_warning_list_update() comment '1.数据筛选和动态匹配: 从 assets_workflow_data 表中筛选符合条件的记录，业务停留时间 (stay_hours) 达到一定阈值。
             2.动态从 indicator_level 表中提取对应的 indicator_id 和 indicator_name 等字段，确保警告信息精准匹配。
             3.预警规则计算: 根据 stay_hours 与 indicator_level.threshold 的比较，动态计算警告级别 (warning_level)。 判断每条记录属于哪种预警等级，例如 1 或 2。
             4.自动插入或更新: 将筛选出的记录插入到 warning_list 表中，如果记录已存在，则更新相关字段。 更新的字段包括停留时间 (value)、警告等级 (warning_level)、更新时间 (update_time)、以及关闭状态 (isClosed)。
             5.关闭状态计算: 根据 assets_workflow_his 表的历史记录判断预警是否关闭 (isClosed)，分为以下情况： 如果记录没有历史信息或审核状态发生变化，则标记为关闭 (isClosed = 1)。 否则标记为未关闭 (isClosed = 0)。
             6.事务处理: 使用事务 (START TRANSACTION 和 COMMIT) 确保所有数据操作是原子化的，避免因某些操作失败导致的数据不一致。
             7.返回结果: 最后通过 SELECT COUNT(*) 返回更新的警告记录数量，方便后续监控或分析。'
BEGIN
    -- 定义标志变量，用于标记游标是否完成遍历
    DECLARE done INT DEFAULT FALSE;

    -- 定义变量，用于存储从游标中提取的数据
    DECLARE v_warning_id VARCHAR(100);
    DECLARE v_indicator_id VARCHAR(100);
    DECLARE v_business_type VARCHAR(100);
    DECLARE v_using_department VARCHAR(255);
    DECLARE v_document_number VARCHAR(100);
    DECLARE v_stay_hours INT;
    DECLARE v_warning_level VARCHAR(45);
    DECLARE v_indicator_name VARCHAR(45);

    -- 定义游标，动态查询需要处理的数据
    DECLARE cur_warning CURSOR FOR
        SELECT
            CONCAT('ZC_FLOW_', aw.document_number) AS warning_id,
            il.indicator_id,
            aw.business_type,
            aw.using_department,
            aw.document_number,
            aw.stay_hours,
            il.warning_level,
            il.indicator_name
        FROM assets_workflow_data aw
                 LEFT JOIN indicator_level il
                           ON il.threshold <= aw.stay_hours
                               AND il.indicator_id = 'zc001'
        WHERE aw.stay_hours IS NOT NULL
          AND il.warning_level IS NOT NULL;

    -- 定义异常处理器，当游标遍历完成时，设置 done = TRUE
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 开始事务
    START TRANSACTION;

    -- 处理仍存在于 assets_workflow_data 表中的记录
    OPEN cur_warning;

    warning_loop: LOOP
        FETCH cur_warning INTO
            v_warning_id, v_indicator_id, v_business_type, v_using_department,
            v_document_number, v_stay_hours, v_warning_level, v_indicator_name;

        IF done THEN
            LEAVE warning_loop;
        END IF;

        -- 插入或更新警告列表
        INSERT INTO warning_list (
            warning_id, bussiness, process, status, value, warning_level, dept_name,
            bussiness_id, indicator_id, indicator_name, update_time, start_time, end_time, isClosed, closed_by
        )
        SELECT
            v_warning_id, '资产管理', v_business_type, '正常',
            v_stay_hours, v_warning_level, v_using_department,
            v_document_number, v_indicator_id, v_indicator_name,
            NOW(), aw.start_time, aw.update_time,0,NULL
        FROM assets_workflow_data aw
        WHERE aw.document_number = v_document_number
        ON DUPLICATE KEY UPDATE
                             value = VALUES(value),
                             warning_level = VALUES(warning_level),
                             update_time = NOW(),
                             isClosed = IF(isClosed = 0, VALUES(isClosed), isClosed), -- 只有 isClosed 仍为 0 时才更新
                             closed_by = IF(VALUES(isClosed) = 1 AND (closed_by IS NULL OR closed_by = 'system'), 'system', closed_by);
    END LOOP;

    -- 关闭游标
    CLOSE cur_warning;

    -- 处理已经从 assets_workflow_data 消失的记录，但只更新 closed_by 为空或system的记录
    UPDATE warning_list wl
    SET isClosed = 1,
        end_time = NOW(),
        update_time = NOW(),
        closed_by = 'system'
    WHERE NOT EXISTS (
        SELECT 1 FROM assets_workflow_data aw
        WHERE aw.document_number = wl.bussiness_id
    ) AND indicator_id = 'zc001'
      AND (closed_by IS NULL OR closed_by = 'system');

    -- 提交事务
    COMMIT;

    -- 返回更新的警告记录数
    SELECT COUNT(*) AS updated_count FROM warning_list;
END;

create
    definer = develop@`%` procedure proc_warning_upgrade() comment '根据规则将一阶预警中的相关预警升级为二阶预警'
BEGIN
    DECLARE v_warning_id VARCHAR(100);
    DECLARE v_indicator_id VARCHAR(100);
    DECLARE v_new_warning_id VARCHAR(100);
    DECLARE done INT DEFAULT FALSE;
    DECLARE yellow_count INT DEFAULT 0;
    DECLARE red_count INT DEFAULT 0;
    DECLARE direct_red_count INT DEFAULT 0;
    DECLARE close_count INT DEFAULT 0;

    -- 查找需要升级到二阶黄色的一阶预警
    DECLARE cur_yellow CURSOR FOR
        SELECT wl.warning_id, wl.indicator_id
        FROM warning_list wl
                 JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
            AND (il.warning_level = '1' OR il.warning_level = 1)
            AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
        WHERE wl.isClosed = 0
          AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
          AND NOT EXISTS (
            SELECT 1 FROM warning_2ed_level wls
            WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
        );

    -- 查找需要直接升级到二阶红色的一阶预警
    DECLARE cur_direct_red CURSOR FOR
        SELECT wl.warning_id, wl.indicator_id
        FROM warning_list wl
                 JOIN indicator_level il ON wl.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
            AND (il.warning_level = '2' OR il.warning_level = 2)
            AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
        WHERE wl.isClosed = 0
          AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold
          AND NOT EXISTS (
            SELECT 1 FROM warning_2ed_level wls
            WHERE wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
        );


    DECLARE cur_red CURSOR FOR
        SELECT wls.warning_id, wls.indicator_id
        FROM warning_2ed_level wls
                 JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
                 JOIN indicator_level il ON wls.indicator_id COLLATE utf8mb4_general_ci = il.indicator_id COLLATE utf8mb4_general_ci
            AND (il.warning_level = '2' OR il.warning_level = 2)
            AND (il.supervisor_level = '2' OR il.supervisor_level = 2)
        WHERE wls.warning_level = '1'
          AND wls.isClosed = 0
          AND wl.isClosed = 0
          AND TIMESTAMPDIFF(HOUR, wl.end_time, NOW()) > il.threshold;


    DECLARE cur_close_second CURSOR FOR
        SELECT wls.warning_id
        FROM warning_2ed_level wls
                 JOIN warning_list wl ON wls.original_warning_id COLLATE utf8mb4_general_ci = wl.warning_id COLLATE utf8mb4_general_ci
        WHERE wls.isClosed = 0
          AND wl.isClosed = 1;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;


    OPEN cur_yellow;

    yellow_loop: LOOP
        FETCH cur_yellow INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE yellow_loop;
        END IF;

        SET v_new_warning_id = UUID();


        INSERT INTO warning_2ed_level
        (warning_id, indicator_id, indicator_name, warning_level,
         update_time, status, original_warning_id,
         bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
         start_time, end_time)
        SELECT
            v_new_warning_id, wl.indicator_id, wl.indicator_name, '1',
            NOW(), 'active', wl.warning_id,
            wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
            wl.start_time, wl.end_time
        FROM warning_list wl
        WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


        UPDATE warning_list
        SET status = 'upgraded',
            update_time = NOW()
        WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


        INSERT INTO warning_trace
        (trace_id, warning_id, indicator_id, original_level, new_level,
         change_time, change_type, change_reason, operator)
        VALUES
            (UUID(), v_new_warning_id, v_indicator_id, '0', '1',
             NOW(), '自动升级', '一阶预警未处理时间超过阈值，自动升级为二阶黄色预警', 'system');

        SET yellow_count = yellow_count + 1;
    END LOOP;

    CLOSE cur_yellow;
    SET done = FALSE;


    OPEN cur_direct_red;

    direct_red_loop: LOOP
        FETCH cur_direct_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE direct_red_loop;
        END IF;

        SET v_new_warning_id = UUID();


        INSERT INTO warning_2ed_level
        (warning_id, indicator_id, indicator_name, warning_level,
         update_time, status, original_warning_id,
         bussiness, process, sub_process, dept_name, value, bussiness_id, isClosed,
         start_time, end_time)
        SELECT
            v_new_warning_id, wl.indicator_id, wl.indicator_name, '2',
            NOW(), 'active', wl.warning_id,
            wl.bussiness, wl.process, wl.sub_process, wl.dept_name, wl.value, wl.bussiness_id, 0,
            wl.start_time, wl.end_time
        FROM warning_list wl
        WHERE wl.warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


        UPDATE warning_list
        SET status = 'upgraded_to_red_directly',
            update_time = NOW()
        WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


        INSERT INTO warning_trace
        (trace_id, warning_id, indicator_id, original_level, new_level,
         change_time, change_type, change_reason, operator)
        VALUES
            (UUID(), v_new_warning_id, v_indicator_id, '0', '2',
             NOW(), '自动升级', '一阶预警未处理时间超过红色预警阈值，直接升级为二阶红色预警', 'system');

        SET direct_red_count = direct_red_count + 1;
    END LOOP;

    CLOSE cur_direct_red;
    SET done = FALSE;


    OPEN cur_red;

    red_loop: LOOP
        FETCH cur_red INTO v_warning_id, v_indicator_id;
        IF done THEN
            LEAVE red_loop;
        END IF;


        UPDATE warning_2ed_level
        SET warning_level = '2',
            update_time = NOW(),
            status = 'upgraded_to_red'
        WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;


        INSERT INTO warning_trace
        (trace_id, warning_id, indicator_id, original_level, new_level,
         change_time, change_type, change_reason, operator)
        VALUES
            (UUID(), v_warning_id, v_indicator_id, '1', '2',
             NOW(), '自动升级', '二阶黄色预警未处理时间超过阈值，自动升级为二阶红色预警', 'system');

        SET red_count = red_count + 1;
    END LOOP;

    CLOSE cur_red;
    SET done = FALSE;


    OPEN cur_close_second;

    close_loop: LOOP
        FETCH cur_close_second INTO v_warning_id;
        IF done THEN
            LEAVE close_loop;
        END IF;


        UPDATE warning_2ed_level
        SET isClosed = 1,
            status = 'closed_with_first_level',
            update_time = NOW()
        WHERE warning_id COLLATE utf8mb4_general_ci = v_warning_id COLLATE utf8mb4_general_ci;

        SET close_count = close_count + 1;
    END LOOP;

    CLOSE cur_close_second;

    COMMIT;


    SELECT
        yellow_count AS yellow_upgraded,
        direct_red_count AS directly_to_red_upgraded,
        red_count AS yellow_to_red_upgraded,
        close_count AS second_level_closed,
        (yellow_count + direct_red_count + red_count + close_count) AS total_processed;

END;




