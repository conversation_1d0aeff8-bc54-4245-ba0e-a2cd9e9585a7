-- 设置SQL模式及禁用唯一和外键检查
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

USE fswp;

-- 删除旧表（如存在）
DROP TABLE IF EXISTS department_duties;
DROP TABLE IF EXISTS annual_work_plan;
DROP TABLE IF EXISTS project_progress;
DROP TABLE IF EXISTS task_assignment;
DROP TABLE IF EXISTS task_progress;
DROP TABLE IF EXISTS project_task;

-- 项目/任务表
CREATE TABLE project_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务/项目ID',
    type ENUM('项目', '任务') NOT NULL COMMENT '类型',
    name VARCHAR(255) NOT NULL COMMENT '任务/项目名称',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    status VARCHAR(50) NOT NULL COMMENT '项目/任务状态',
    owner_id VARCHAR(30) NOT NULL COMMENT '负责人ID',
    owner_name VARCHAR(30) NOT NULL COMMENT '负责人姓名',
    department_id VARCHAR(50) NOT NULL COMMENT '所属部门id',
    department_name VARCHAR(50) NOT NULL COMMENT '所属部门名称'
);

-- 任务进展表
CREATE TABLE task_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务进展ID',
    sub_task_name VARCHAR(255) NOT NULL COMMENT '子任务名称',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    reporter_id VARCHAR(30) NOT NULL COMMENT '填报人ID',
    reporter_name VARCHAR(30) NOT NULL COMMENT '填报人姓名',
    work_content TEXT COMMENT '工作内容',
    work_result TEXT COMMENT '工作结果',
    issues TEXT COMMENT '问题',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 任务/项目分工表
CREATE TABLE task_assignment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分工ID',
    type ENUM('项目', '任务') NOT NULL COMMENT '类型',
    ref_id BIGINT NOT NULL COMMENT '任务/项目ID',
    position VARCHAR(30) NOT NULL COMMENT '职位',
    content TEXT COMMENT '负责内容',
    participant_id VARCHAR(30) NOT NULL COMMENT '参与人ID',
    participant_name VARCHAR(30) NOT NULL COMMENT '参与人姓名',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 项目进展表
CREATE TABLE project_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目进展ID',
    progress_name VARCHAR(255) NOT NULL COMMENT '进展名称',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    reporter_id VARCHAR(30) NOT NULL COMMENT '填报人ID',
    reporter_name VARCHAR(30) NOT NULL COMMENT '填报人姓名',
    last_week_plan TEXT COMMENT '上周计划',
    completion TEXT COMMENT '完成情况',
    gap_reason TEXT COMMENT '完成差距和原因',
    next_week_plan TEXT COMMENT '下周计划',
    current_progress TEXT COMMENT '当前进展描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    progress_percent DECIMAL(5,2) COMMENT '进度百分比',
    deviation_percent DECIMAL(5,2) COMMENT '进度偏差百分比',
    week_number BIGINT NOT NULL COMMENT '周数'
);

-- 项目总进展表
CREATE TABLE project_whole_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目总进展ID',
    progress_name VARCHAR(255) NOT NULL COMMENT '进展名称',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    reporter_id VARCHAR(30) NOT NULL COMMENT '填报人ID',
    reporter_name VARCHAR(30) NOT NULL COMMENT '填报人姓名',
    last_week_plan TEXT COMMENT '上周计划',
    completion TEXT COMMENT '完成情况',
    gap_reason TEXT COMMENT '完成差距和原因',
    next_week_plan TEXT COMMENT '下周计划',
    current_progress TEXT COMMENT '当前进展描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    progress_percent DECIMAL(5,2) COMMENT '进度百分比',
    deviation_percent DECIMAL(5,2) COMMENT '进度偏差百分比',
    week_number BIGINT NOT NULL COMMENT '周数'
);

-- 年度工作计划维护表
CREATE TABLE annual_work_plan (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '计划维护ID',
    title VARCHAR(255) NOT NULL COMMENT '计划工作标题',
    content TEXT NOT NULL COMMENT '计划工作内容',
    progress TEXT COMMENT '计划进展',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    progress_percent DECIMAL(5,2) COMMENT '进度百分比',
    deviation_percent DECIMAL(5,2) COMMENT '偏差百分比',
    department_id VARCHAR(50) NOT NULL COMMENT '所属部门id',
    department_name VARCHAR(50) NOT NULL COMMENT '所属部门名称'
);

-- 部门工作职责维护表
CREATE TABLE department_duties (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '职责维护ID',
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    department_id VARCHAR(50) NOT NULL COMMENT '所属部门id',
    department_name VARCHAR(50) NOT NULL COMMENT '所属部门名称'
);