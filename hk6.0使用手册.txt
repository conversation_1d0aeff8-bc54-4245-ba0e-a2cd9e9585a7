平台使用手册
成都淞幸科技有限责任公司
2024年1月
目 录
1.引言
1.1编写目的
1.2定义
1.2.1术语
1.2.2缩略语
1.3参考文档
2.dm中的方法
2.1.方法
2.1.1.$Q.list
2.1.2.sum
2.1.3.group
2.1.4.avg
2.1.5.max
2.1.6.min
2.1.7.retain
2.1.8.top
2.1.9.count
2.1.10.order
2.1.11.extend
2.2.变量
2.2.1.$P
2.2.2.$S
2.2.3.$C
2.2.4.$Q
2.2.5.扩展
3.dm中的标签
3.1.chart
3.1.1.属性说明
3.1.2.使用方式
3.1.3.返回值
3.1.4.调用方式
3.2.filelist
3.2.1.属性说明
3.2.2.使用方式
3.2.3.返回值
3.3.data
3.3.1.属性说明
3.3.2.使用方式
3.3.3.返回值
3.4.database
3.4.1.属性说明
3.4.2.使用方式
3.4.3.返回值
4.多数据库
4.1.配置方式
4.2.使用方式
1. 引言
1.1 编写目的
本操作手册的宗旨，就是帮助您更好的使用我们的软件。并且更深入的突出本软件的显著特点，让您系统、全面地掌握系统操作，从而提高工作效率和工作质量，减少您许多重复性的劳动。
本文档适用于核格开发平台6.0用户使用。
1.2 定义
1.2.1 术语
术语名称	定义
构件	构件是软件开发平台中具有相对独立功能、可以明确辨识、接口由契约指定、和语境有明显的依赖关系、可独立部署的可组装软件实体
1.2.2 缩略语
缩略语如表1所示：
表1缩略语
缩略语	英文名称	注释
B/S	Browser/Server	浏览器/服务器
XML	EXtensible Markup Language	可扩展标记语言
DPS	Date Processing System	数据加工处理系统
FTP	File Transfer Protocol	文件传输协议
HTTP	HyperText Transfer Protocol	超文本传输协议
SVN	Subversion	版本控制系统
1.3 参考文档
《计算机软件工程规范国家标准汇编2000》，中国标准出版社
《计算机软件文档编制规范GBT8567-2006》，中国标准出版社
《计算机软件产品开发文件编制指南GB 8567-88》，中国标准出版社
2. 开发环境搭建
2.1. 环境信息
基于springboot3框架，使用Jdk21版本，开发工具为idea或eclipse
2.2. 新建Maven工程
以eclipse为例新建Maven工程，如下：
在项目资源管理器中，右键->新建->其他

选择Maven->Maven project

点击“下一步”，选择工作空间，如下所示：

点击“下一步”，选择如下所示的框架

点击下一步，输入名称为hearkenTest的项目信息，如下：

点击完成，在项目控制台中，输入y

点击“enter”键，项目新建成功，如下：

2.3修改本地maven仓库的setting.xml文件
找到setting.xml文件，内容修改为如下：
<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<!--
| This is the configuration file for Maven. It can be specified at two levels:
|
|  1. User Level. This settings.xml file provides configuration for a single user,
|                 and is normally provided in ${user.home}/.m2/settings.xml.
|
|                 NOTE: This location can be overridden with the CLI option:
|
|                 -s /path/to/user/settings.xml
|
|  2. Global Level. This settings.xml file provides configuration for all Maven
|                 users on a machine (assuming they're all using the same Maven
|                 installation). It's normally provided in
|                 ${maven.conf}/settings.xml.
|
|                 NOTE: This location can be overridden with the CLI option:
|
|                 -gs /path/to/global/settings.xml
|
| The sections in this sample file are intended to give you a running start at
| getting the most out of your Maven installation. Where appropriate, the default
| values (values used when the setting is not specified) are provided.
|
|-->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <!-- localRepository
  | The path to the local repository maven will use to store artifacts.
  |
  | Default: ${user.home}/.m2/repository
  <localRepository>/path/to/local/repo</localRepository>
  -->
  <localRepository>D:/mavenstore</localRepository>
  <!-- interactiveMode
  | This will determine whether maven prompts you when it needs input. If set to false,
  | maven will use a sensible default value, perhaps based on some other setting, for
  | the parameter in question.
  |
  | Default: true
  <interactiveMode>true</interactiveMode>
  -->

  <!-- offline
  | Determines whether maven should attempt to connect to the network when executing a build.
  | This will have an effect on artifact downloads, artifact deployment, and others.
  |
  | Default: false
  <offline>false</offline>
  -->

  <!-- pluginGroups
  | This is a list of additional group identifiers that will be searched when resolving plugins by their prefix, i.e.
  | when invoking a command line like "mvn prefix:goal". Maven will automatically add the group identifiers
  | "org.apache.maven.plugins" and "org.codehaus.mojo" if these are not already contained in the list.
  |-->
  <pluginGroups>
    <!-- pluginGroup
     | Specifies a further group identifier to use for plugin lookup.
    <pluginGroup>com.your.plugins</pluginGroup>
    -->
  </pluginGroups>

  <!-- proxies
   | This is a list of proxies which can be used on this machine to connect to the network.
   | Unless otherwise specified (by system property or command-line switch), the first proxy
   | specification in this list marked as active will be used.
   |-->
  <proxies>
    <!-- proxy
     | Specification for one proxy, to be used in connecting to the network.
     |
    <proxy>
      <id>optional</id>
      <active>true</active>
      <protocol>http</protocol>
      <username>proxyuser</username>
      <password>proxypass</password>
      <host>proxy.host.net</host>
      <port>80</port>
      <nonProxyHosts>local.net|some.host.com</nonProxyHosts>
    </proxy>
    -->
  </proxies>

  <!-- servers
   | This is a list of authentication profiles, keyed by the server-id used within the system.
   | Authentication profiles can be used whenever maven must make a connection to a remote server.
   |-->
  <servers>
	<server>
		<id>hearken-releases</id>
		<username>deploy</username>
		<password>deploy</password>
	</server>
	<server>
		<id>hearken-snapshots</id>
		<username>deploy</username>
		<password>deploy</password>
	</server>
    <!-- server
     | Specifies the authentication information to use when connecting to a particular server, identified by
     | a unique name within the system (referred to by the 'id' attribute below).
     |
     | NOTE: You should either specify username/password OR privateKey/passphrase, since these pairings are
     |       used together.
     |
    <server>
      <id>deploymentRepo</id>
      <username>repouser</username>
      <password>repopwd</password>
    </server>
    -->

    <!-- Another sample, using keys to authenticate.
    <server>
      <id>siteServer</id>
      <privateKey>/path/to/private/key</privateKey>
      <passphrase>optional; leave empty if not used.</passphrase>
    </server>
    -->
  </servers>

  <!-- mirrors
   | This is a list of mirrors to be used in downloading artifacts from remote repositories.
   |
   | It works like this: a POM may declare a repository to use in resolving certain artifacts.
   | However, this repository may have problems with heavy traffic at times, so people have mirrored
   | it to several places.
   |
   | That repository definition will have a unique id, so we can create a mirror reference for that
   | repository, to be used as an alternate download site. The mirror site will be the preferred
   | server for that repository.
   |-->
  <mirrors>
	<mirror>
    	<id>hearken-releases</id>
        <mirrorOf>hearken-releases</mirrorOf>
        <name>hearken-releases</name>
        <url>http://maven.hearker.com/repository/maven-public/</url>
     </mirror>
    <!-- mirror
     | Specifies a repository mirror site to use instead of a given repository. The repository that
     | this mirror serves has an ID that matches the mirrorOf element of this mirror. IDs are used
     | for inheritance and direct lookup purposes, and must be unique across the set of mirrors.
     |
    <mirror>
      <id>mirrorId</id>
      <mirrorOf>repositoryId</mirrorOf>
      <name>Human Readable Name for this Mirror.</name>
      <url>http://my.repository.com/repo/path</url>
    </mirror>
     -->
	 
	 <mirror>
        <id>nexus-aliyun</id>
        <mirrorOf>*</mirrorOf>
        <name>Nexus aliyun</name>
        <url>http://maven.aliyun.com/nexus/content/groups/public</url>
	</mirror>
  </mirrors>

  <!-- profiles
   | This is a list of profiles which can be activated in a variety of ways, and which can modify
   | the build process. Profiles provided in the settings.xml are intended to provide local machine-
   | specific paths and repository locations which allow the build to work in the local environment.
   |
   | For example, if you have an integration testing plugin - like cactus - that needs to know where
   | your Tomcat instance is installed, you can provide a variable here such that the variable is
   | dereferenced during the build process to configure the cactus plugin.
   |
   | As noted above, profiles can be activated in a variety of ways. One way - the activeProfiles
   | section of this document (settings.xml) - will be discussed later. Another way essentially
   | relies on the detection of a system property, either matching a particular value for the property,
   | or merely testing its existence. Profiles can also be activated by JDK version prefix, where a
   | value of '1.4' might activate a profile when the build is executed on a JDK version of '1.4.2_07'.
   | Finally, the list of active profiles can be specified directly from the command line.
   |
   | NOTE: For profiles defined in the settings.xml, you are restricted to specifying only artifact
   |       repositories, plugin repositories, and free-form properties to be used as configuration
   |       variables for plugins in the POM.
   |
   |-->
  <profiles>
	<profile>
		<id>hearken-profile</id>
		<repositories>
			<repository>
				<id>hearken-releases</id>
				<name>hearken-releases</name>
				<url>http://maven.hearker.com/repository/maven-releases/</url>
				<layout>default</layout>
        <releases>
					<enabled>true</enabled>
				</releases>
				<snapshots>
					<enabled>true</enabled>
				</snapshots>
			</repository>
			<repository>
				<id>hearken-snapshots</id>
				<name>hearken-snapshots</name>
				<url>http://maven.hearker.com/repository/maven-snapshots/</url>
      	<releases>
					<enabled>true</enabled>
				</releases>
				<snapshots>
					<enabled>true</enabled>
				</snapshots>
			</repository>
		</repositories>

		<pluginRepositories>

			<pluginRepository>
				<id>hearken-releases</id>
				<name>hearken-releases</name>
				<url>http://maven.hearker.com/repository/maven-releases/</url>
				<releases>
					<enabled>true</enabled>
				</releases>
				<snapshots>
					<enabled>true</enabled>
				</snapshots>
			</pluginRepository>
      
      <pluginRepository>
				<id>hearken-snapshots</id>
				<name>hearken-snapshots</name>
				<url>http://maven.hearker.com/repository/maven-snapshots/</url>
				<releases>
					<enabled>true</enabled>
				</releases>
				<snapshots>
					<enabled>true</enabled>
				</snapshots>
			</pluginRepository>

		</pluginRepositories>
	</profile>
    <!-- profile
     | Specifies a set of introductions to the build process, to be activated using one or more of the
     | mechanisms described above. For inheritance purposes, and to activate profiles via <activatedProfiles/>
     | or the command line, profiles have to have an ID that is unique.
     |
     | An encouraged best practice for profile identification is to use a consistent naming convention
     | for profiles, such as 'env-dev', 'env-test', 'env-production', 'user-jdcasey', 'user-brett', etc.
     | This will make it more intuitive to understand what the set of introduced profiles is attempting
     | to accomplish, particularly when you only have a list of profile id's for debug.
     |
     | This profile example uses the JDK version to trigger activation, and provides a JDK-specific repo.
    <profile>
      <id>jdk-1.4</id>

      <activation>
        <jdk>1.4</jdk>
      </activation>

      <repositories>
        <repository>
          <id>jdk14</id>
          <name>Repository for JDK 1.4 builds</name>
          <url>http://www.myhost.com/maven/jdk14</url>
          <layout>default</layout>
          <snapshotPolicy>always</snapshotPolicy>
        </repository>
      </repositories>
    </profile>
    -->

    <!--
     | Here is another profile, activated by the system property 'target-env' with a value of 'dev',
     | which provides a specific path to the Tomcat instance. To use this, your plugin configuration
     | might hypothetically look like:
     |
     | ...
     | <plugin>
     |   <groupId>org.myco.myplugins</groupId>
     |   <artifactId>myplugin</artifactId>
     |
     |   <configuration>
     |     <tomcatLocation>${tomcatPath}</tomcatLocation>
     |   </configuration>
     | </plugin>
     | ...
     |
     | NOTE: If you just wanted to inject this configuration whenever someone set 'target-env' to
     |       anything, you could just leave off the <value/> inside the activation-property.
     |
    <profile>
      <id>env-dev</id>

      <activation>
        <property>
          <name>target-env</name>
          <value>dev</value>
        </property>
      </activation>

      <properties>
        <tomcatPath>/path/to/tomcat/instance</tomcatPath>
      </properties>
    </profile>
    -->
	<profile>
        <id>jdk-1.8</id>
        <activation>
            <activeByDefault>true</activeByDefault>
            <jdk>1.8</jdk>
        </activation>
        <properties>
            <maven.compiler.source>1.8</maven.compiler.source>
            <maven.compiler.target>1.8</maven.compiler.target>
            <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
        </properties>
    </profile>
  </profiles>

  <!-- activeProfiles
   | List of profiles that are active for all builds.
   |
  <activeProfiles>
    <activeProfile>alwaysActiveProfile</activeProfile>
    <activeProfile>anotherAlwaysActiveProfile</activeProfile>
  </activeProfiles>
  -->
  <activeProfiles>
	  <activeProfile>hearken-profile</activeProfile>
  </activeProfiles>
</settings>


2.4. 一键生成6.0平台项目开发工程
执行一个cmd命令，直接生成6.0平台的项目开发工程，生成的工程里面包括：
1、pom.xml文件，已经添加好了依赖包
2、application.yml文件，已经完成了配置，包括：数据库配置等
3、Demo.dm文件，包括了所有标签的使用示例
4、已经配置好的项目启动文件ArchetypeDemoApplication.java
生成的项目什么都不用修改，就可以直接启动。
生成此项目的步骤如下：
1、引入hearken-project-archetype包（模版包）
随便打开一个maven工程，引入以下包，将它下载到maven本地仓库中
<dependency>
<groupId>com.sunsheen</groupId>
<artifactId>hearken-project-archetype</artifactId>
<version>0.0.1</version>
</dependency>

2、在工作空间根目录执行cmd命令
如：新建的业务工程名称为demo（以下红色字体，com.sunsheen不可更改）
mvn archetype:generate -B -DarchetypeGroupId=com.sunsheen -DarchetypeArtifactId=hearken-project-archetype -DarchetypeVersion=0.0.1 -DgroupId=com.sunsheen -DartifactId=demo -Dversion=1.0.0 -Dpackage=com.sunsheen.demo

执行成功后，如下图：

将新建的工程导入eclipse或idea即可

2.5手动创建6.0平台项目工程
打开项目pom.xml文件
文件内容改为如下：
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.1</version>
    <relativePath /> <!-- lookup parent from repository -->
  </parent>
  <groupId>com.sunsheen</groupId>
  <artifactId>hearkenTest</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <properties>
    <java.version>21</java.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.sunsheen.jfids.das.spring</groupId>
      <artifactId>hearken-das-spring-registry-client-starter</artifactId>
      <version>3.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.sunsheen</groupId>
      <artifactId>hearken-service</artifactId>
      <version>0.0.3</version>
    </dependency>
  </dependencies>

  <!-- 打依赖包-->
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <classifier>exec</classifier>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>hearken-releases</id>
      <name>hearken-releases</name>
      <url>http://maven.hearker.com/repository/maven-releases/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>hearken-snapshots</id>
      <name>hearken-snapshots</name>
      <url>http://maven.hearker.com/repository/maven-snapshots/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <pluginRepositories>

    <pluginRepository>
      <id>hearken-releases</id>
      <name>hearken-releases</name>
      <url>http://maven.hearker.com/repository/maven-releases/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>

    <pluginRepository>
      <id>hearken-snapshots</id>
      <name>hearken-snapshots</name>
      <url>http://maven.hearker.com/repository/maven-snapshots/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>
</project>


2.5.1引入HK6.0平台依赖包
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.1</version>
    <relativePath /> <!-- lookup parent from repository -->
  </parent>
  <groupId>com.sunsheen</groupId>
  <artifactId>hearkenTest</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <properties>
    <java.version>21</java.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.sunsheen.jfids.das.spring</groupId>
      <artifactId>hearken-das-spring-registry-client-starter</artifactId>
      <version>3.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.sunsheen</groupId>
      <artifactId>hearken-service</artifactId>
      <version>0.0.3</version>
    </dependency>
  </dependencies>

  <!-- 打依赖包-->
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <classifier>exec</classifier>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>hearken-releases</id>
      <name>hearken-releases</name>
      <url>http://maven.hearker.com/repository/maven-releases/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>hearken-snapshots</id>
      <name>hearken-snapshots</name>
      <url>http://maven.hearker.com/repository/maven-snapshots/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <pluginRepositories>

    <pluginRepository>
      <id>hearken-releases</id>
      <name>hearken-releases</name>
      <url>http://maven.hearker.com/repository/maven-releases/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>

    <pluginRepository>
      <id>hearken-snapshots</id>
      <name>hearken-snapshots</name>
      <url>http://maven.hearker.com/repository/maven-snapshots/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>
</project>
2.5.2新建application.yml文件
在src/main/java目录上右键->新建->文件夹

选中main文件夹，输入名称resources，如下：

点击“完成”，在新建的resourecs文件夹上右键->新建->文件，如下：

在弹出框中选择“resources”输入“application.yml”

点击“完成”
在新建的application.yml中，输入如下内容：
management:
  endpoints:
    web:
      exposure:
        include: "*"
server:
  port: 8080
  servlet:
    context-path: /hearkenTest

spring:
  application:
    #拉取配置时使用，若未设置，使用HKDAS配置application.profile.code或application.profile.name
    name: 核格测试
    
  datasource:
    dialect: MySQLDialect
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: hkzbxt
    username: hkzbxt
    url: ******************************************************rue&characterEncoding=UTF-8&serverTimezone=UTC
    
    # 连接池配置
    druid:
      # 初始化连接数
      initialSize: 5
      #连接池中的最小空闲连接数
      minIdle: 5
      #控制连接池中的最大活动连接数
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存时间
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM user
      testWhileIdle: true
      testOnBorrow: false
      # 打开 PSCache，并且指定每个连接上 PSCache 的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的 Filter，去掉后监控界面 SQL 无法统计，wall 用于防火墙
      filters: stat,wall,slf4j
      # 通过 connectProperties 属性打开 mergeSql 功能；慢 SQL 记录
      connectProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
  web:
    resources:
      static-locations: classpath:/static/
#HKDAS相关配置,springboot同类项未配置相关信息使用HKDAS中的配置
application:
  registry:
    address: **************:9998
    web-address: **************:8999
    client:
      enabled: false

dubbo:
  application:
    name: ${application.profile.code}
    serialize-check-status: DISABLE
    check-serializable: false
    qos-enable: false
  hessian:
    allowNonSerializable: true
  protocol:
    name: dubbo
    port: -1
    #transporter: custom
  provider:
    #listener: custom
    dispatcher: custom
    #consumer:
    #listener: custom
  registry:
    #check: false
    address: "N/A"  #zookeeper://${zookeeper.address:127.0.0.1}:2181?subscribe=false&register=false
  use-as-config-center: false
  use-as-metadata-report: false



其中，以下信息根据项目的实际情况进行更改：

2.5.3更改项目启动文件App.java
打开项目启动文件App.java，输入如下内容：
package com.sunsheen.jfids.hearken.hearkenTest;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication(scanBasePackages = { "com.sunsheen" })
@EnableTransactionManagement // 开始注解版事务
public class App 
{   
    public static void main( String[] args )
    {
    	SpringApplication.run(App.class, args);
    }
}



3.开发示例
从数据库表demo_amd中查询数据
向表demo_amd中插入数据
开发示例项目工程地址：http://git.hearker.com/shujian/hearkenTest.git
账号/密码：hearkenTest/hearkenTest
3.1.从数据库表demo_amd中查询数据
在resources下新建文件夹dm（名称、层级可自定义），在dm下新建文件Test.dm，如下：

Test.dm文件内容如下：
<?xml version="1.0" encoding="UTF-8"?>
<sqlMap namespace="Test" description="示例配置">

	<database id="select" resultMap="java.util.HashMap" description="查询语句简单配置">
		select * from demo_amd
	</database>
</sqlMap>

运行App.java，启动项目
接口调用
访问查询接口：
http://localhost:8080/hearkenTest/data/QueryDataForList.svt?dataId=Test.select
如下图所示：

访问成功，返回List<Map>类型的数据
说明：database标签表示从数据库里查询数据
代码调用
在调用类中，注入bean，如下：
@Autowired
@Qualifier("QueryDataForListComponent")
IDataPort queryForList;

执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put("dataId","Test.select")
List<Map<String, Object>> dataList = (List<Map<String, Object>>)this.queryForList.run(param);

3.2.向表demo_amd中插入数据
在Test.dm文件中添加如下片段
<database id="insert" resultMap="java.util.HashMap" description="新增数据库表数据">
  insert into demo_amd(id,text) values(:id,:text)
</database>

重启项目
接口调用
使用post请求，传输参数，访问如下地址，
http://localhost:8080/hearkenTest/json/SaveData.json
如下：

代码调用
在调用类中注入bean
@Autowired
@Qualifier("SaveDataComponent")
IDataPort saveData;

执行以下代码完成调用
//paramMap为map对象，属性包括dataId与data
int ret = (int)this.saveData.run(paramMap);

说明：database标签表示将数据插入数据库
4.四个通用组件
保存组件：SaveDataComponent
查询组件：
QueryDataForListComponent
执行dataId查询数据，返回数据格式为List<Map>
QueryDataForMapComponent
执行dataId查询数据，返回数据格式为Map
QueryDataForObjectComponent
执行dataId查询数据，返回数据格式为Object
4.1.保存组件-SaveDataComponent
6.0平台的数据保存通用组件，包括数据库的增删改、elasticsearch的增删改等
参数格式如下：
{
    "dataId": "Demo.insert",
    "data": {
        "id": "023",
        "staffid": "21122",
        "name": "蛤蛤",
        "text": "请假原因"
    }
}

其中，dataId与data为固定参数，data可为数组，表示一次插入多条数据。
接口调用
http://ip:port/项目前缀名/json/SaveData.json
请求类型：POST

代码调用
在调用类中注入bean
@Autowired
@Qualifier("SaveDataComponent")
IDataPort saveData;

执行以下代码完成调用
//paramMap为map对象，属性包括dataId与data
int ret = (int)this.saveData.run(paramMap);

4.2.查询组件-QueryDataForListComponent
接口调用
http://ip:port/项目前缀名/data/QueryDataForList.svt
请求类型：GET

代码调用
在调用类中，注入bean，如下：
@Autowired
@Qualifier("QueryDataForListComponent")
IDataPort queryForList;

执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put("dataId","Demo.select")
List<Map<String, Object>> dataList = (List<Map<String, Object>>)this.queryForList.run(param);

4.3.查询组件-QueryDataForMapComponent
接口调用
http://ip:port/项目前缀名/data/QueryDataForMap.svt
请求类型：GET

注意：QueryDataForMapComponent要求dataId的执行结果为一条数据
代码调用
在调用类中，注入bean，如下：
@Autowired
@Qualifier("QueryDataForMapComponent")
IDataPort queryForMap;

执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put("dataId","Demo.selectMap")
List<Map<String, Object>> dataMap = (Map<String, Object>)this.queryForMap.run(param);

4.4.查询组件-QueryDataForObjectComponent
使用方式同上
此组件暂时与data标签配合使用，返回自定义的字符串。
dm文件data标签内容：
<data id="test_data" description="原样返回数据">
		<![CDATA[
			根据文字模版生成字符串，自定义的数据$P.test的数据
		]]>
	</data>

执行结果：

5.dm中的方法
5.1. $Q.list
dm文件为项目数据源配置文件，与核格5.0平台的sqlMap文件类似，其中dataId（命名空间.标签id）表示dm文件中的数据源id，值全局唯一
方法列表
序号	方法名称	描述
1	$Q.list	执行dm文件中的dataId，返回List<Map>类型的数据，调用其他方法的起点，其他方法不管是group还是avg等都需要先执行此方法获取数据。
2	group	分组函数，对标数据库中的group by，返回$Q.list
3	retain	保留多少位小数，四舍五入，返回$Q.list
4	avg	求平均值函数，返回$Q.list
5	sum	对字段求和操作，返回$Q.list
6	max	取一个字段的最大值，获取整条数据，返回$Q.list
7	min	取一个字段的最小值，获取整条数据，返回$Q.list
8	top	取List<Map>的前多少条或前百分之多少的数据，返回$Q.list
9	count	返回数据条数
10	order	对数据进行排序，返回$Q.list
11	extend	执行自定义函数，为了后面继续执行其他方法，建议返回ResultReprocessingMap/List对象，可执行$Q.list/map的方法
12	getMap	取出Map类型数据的子项并返回，返回$Q.map
13	getFields	取出List类型数据的指定字段，返回$Q.list
14	getObject	取出List类型数据的指定字段，组装为List<Object>，返回$Q.object
15	getObject	取出List类型数据的多个指定字段，返回对应数据的List<Object>，$Q.object
16	remove	移除指定字段，返回$Q.list
17	getString	将结果转换为String类型的字符串，相当与java中的toString方法，结果不带双引号
18	getJson	将结果转换为Json类型的字符串，结果带双引号，默认此方式返回
5.1.1. $Q.list
*******. 形式
$Q.list(dataId)或$Q.list(dataId,params)
*******. 参数介绍
参数	类型	说明	是否必须
dataId	String	dm文件数据源id	是
params	Map	执行dataId的参数	否
$Q.list执行的dataId，获取参数的方式与其他相同，可使用$P，$S，$C.get等。
*******. 使用示例
可置于data标签中，如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi")
  ]]>
</data>

或
<data id="cma_stat3" description="$Q.list用法示例">
<![CDATA[
  $Q.list("TestScjc.cmi",{"name":"张三"})
]]>
</data>

执行此dataId，将从dataId为TestScjc.cmi的数据源中获取数据，返回List<Map>类型的数据。
5.1.2. sum
*******. 形式
sum(fields)
*******. 参数介绍
参数	类型	说明	是否必须
fields	数组	字段名称，多个在数组内以逗号隔开	是
*******. 使用示例
1、单个字段求和
对List<Map>中的字段求和，对标数据库中的sum函数，如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").sum(["val1"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>中的val1求和，返回val1字段的求和结果，返回值类型为List<Map>
2、多个字段求和
表示对$Q.list("TestScjc.cmi")返回的List<Map>中的val1与val2求和, 返回val1与val2字段的求和结果，返回值类型为List<Map>
5.1.3. group
*******. 形式
group(method,fields1,fields2)
*******. 参数介绍
参数	类型	说明	是否必须
method	String	分组时执行的函数，可为avg,sum,max,min,count	是
fields1	数组	分组时执行的函数的字段名称，多个在数组内以逗号隔开	是
fields2	数组	以那些字段进行分组，多个在数组内以逗号隔开	是
*******. 使用示例
1、单个字段分组、求和
对List<Map>类型的数据进行分组，对标数据库中的group by，如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").group("sum",["val1"],["Station_Id_C"])
  ]]>
</data>

对$Q.list("TestScjc.cmi")的执行结果分组求和,按照字段Station_Id_C进行分组，并对val1进行求和。
返回List<Map>类型的数据，包含2个字段：Station_Id_C，val1
2、多字段分组，多字段求和
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").group("sum",["val1","val2"],["Station_Id_C","Station_Id_B"])
  ]]>
</data>

对$Q.list("TestScjc.cmi")的执行结果分组求和，按照字段Station_Id_C与Station_Id_B进行分组，并对val1与val2进行求和。
返回List<Map>类型的数据，包含4个字段：Station_Id_C，Station_Id_B，val1，val2
5.1.4. avg
求平均值函数，返回List<Map>类型的数据。用法与sum函数相同，可与group函数一起使用。
5.1.5. max
*******. 形式
max(fields)
*******. 参数介绍
参数	类型	说明	是否必须
fields	数组	取字段最大值所在的数据行，只支持一个字段	是
*******. 使用示例
取一个字段的最大值，将整条数据返回，最终返回List<Map>类型的数据。如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").max(["val1"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>中的val1取最大值，获取最大值所在的数据行，返回值类型为List<Map>
其中max只支持一个参数，不能像sum一样传多个参数
结合group使用
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").group("max",["val1"],["Station_Id_C","Station_Id_B"])
  ]]>
</data>

5.1.6. min
与max函数使用一致。
5.1.7. retain
*******. 形式
retain(n,fields)
*******. 参数介绍
参数	类型	说明	是否必须
n	int	保留的小数位，四舍五入	是
fields	数组	保留小数位的字段，多个逗号隔开	是
*******. 使用示例
保留多少位小数，四舍五入，返回List<Map>类型的数据
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").group("avg",["val1","val2"],["Station_Id_C"]).retain(2,["val1","val2"])
  ]]>
</data>

5.1.8. top
*******. 形式
top(ns)
*******. 参数介绍
参数	类型	说明	是否必须
ns	数组	取前多少条数据或取前百分之多少的数据	是
*******. 使用示例
1、取前百分之多少的数据
对List<Map>中的字段求和，对标数据库中的top函数，如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").top(["5%"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>中的数据, 取前5%, 返回值类型为List<Map>
2、取前n条数据
对List<Map>中的字段求和，对标数据库中的top函数，如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").top(["5"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>中的数据，取前5条数据，返回值类型为List<Map>
5.1.9. count
*******. 形式
count(["*"])或count(fields)或count(["* as counts"])
*******. 参数介绍
参数	类型	说明	是否必须
fields	数组	计算数据条数，支持字段重命名	是
*******. 使用示例
计算数据量，返回List<Map>类型的数据。如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").count(["*"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>中的总条数，返回值类型为List<Map>
字段重命名
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").count(["* as counts"])
  ]]>
</data>

返回值中counts字段表示数量
其中count只支持一个参数，不能像sum一样传多个参数
结合group使用
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").group("count",["*"], ["Station_Id_C","Station_Id_B"])
  ]]>
</data>	

5.1.10. order
********. 形式
order(["col1 desc"]) 或 order(["col1 desc", "col2 asc"])
********. 参数介绍
参数	类型	说明	是否必须
fields	数组	排序，desc倒序排列，asc正序排列	是
********. 使用示例
计算数据量，返回List<Map>类型的数据。如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").order(["col1 desc"])
  ]]>
</data>

表示对$Q.list("TestScjc.cmi")返回的List<Map>数据，按照col1字段进行倒序排列，返回值类型为List<Map>
5.1.11. extend
执行自定义函数类，为了后面继续执行max、top等方法，建议返回ResultReprocessingList对象。在自定义函数类中，可获取$P、$S等常用对象
********. 形式
extend(componentName)或extend(componentName, map)
********. 参数介绍
参数	类型	说明	是否必须
componentName	String	自定义的构件bean名称，必须继承ABaseComponent
并以Component结尾	是
map	Map	自定义参数	否
********. 使用示例
扩展了一个数据处理组件，名称：ReprocessingExtendComponent.java
1、无自定义参数
调用方式如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
    $Q.list("TestScjc.cmi").extend("ReprocessingExtend")
  ]]>
</data>

以上表示，先获取获取dataId为TestScjc.cmi的数据，将数据传入自定义的ReprocessingExtendComponent.java中再作处理，ReprocessingExtendComponent.java的返回值即为cma_stat3最终的返回值。
2、有自定义参数
调用方式如下所示：
<data id="cma_stat3" description="$Q.list用法示例">
  <![CDATA[
  $Q.list("TestScjc.cmi").extend("ReprocessingExtend",{"name":"张三"})
  ]]>
</data>

以上表示，在ReprocessingExtendComponent.java中可以获取到{"name":"张三"}。
获取方式如下所示：
Map custom_params = (Map) this.getCallParam(param, "custom_params");
3、在自定义函数类中获取$P、$S等对象
//公共的实现类，建议返回此对象
ResultReprocessingList resultReprocessingList = (ResultReprocessingList)this.getCallParam(param, "resultReprocessingList");

VelocityContext current_context = resultReprocessingList.getCurrent_context();
//获取$P对象
Map paramterMapP = (Map)current_context.get("P");
System.out.println("获取的$P：" + paramterMapP);
//获取$S对象
Map paramterMapS = (Map)current_context.get("S");
System.out.println("获取的$S：" + paramterMapS);


********. 使用QueryDataForListComponent执行dataId
//注入QueryDataForListComponent组件
@Autowired
@Qualifier("QueryDataForListComponent")
IDataPort queryDataForListComponent;
//将dataId放入paramterMap中，获取数据
Object values = queryDataForListComponent.run(paramterMap);


5.1.12. getFields
********. 形式
getFields(["field1", "field2"])
********. 参数介绍
参数	类型	说明	是否必须
fileds	List<String>	List中Map的字段名	是
********. 使用示例
1、获取List类型数据的指定字段
获取map类型数据的子项
<data id=" getFields " description=" getFields测试">
  <![CDATA[
    $Q.list("Demo.test_data").getFields([“column1”, “column2”])
  ]]>
</data>

表示获取list中对于dataId的执行结果，取出其中info字段中column1, column2 的数据
结果示例：
[
  {
    "column1": "nice",
    "column2": "well"
  },
  {
    "column1": "nice",
    "column2": "well"
  }
]

5.1.13. getObject
********. 形式
getObject("field")
********. 参数介绍
参数	类型	说明	是否必须
filed	String	List中数据的字段名 	是
********. 使用示例
1、获取List类型数据的指定字段
<data id=" getObject " description=" getObject测试">
  <![CDATA[
    $Q.list("Demo.test_data").getObject("field")
  ]]>
</data>

表示对list获取的结果，取出其中名为field的数据，返回List<Object>
结果示例：
[
  {
    "field": "a"
  },
  {
    "field": "b"
  }
]

5.1.14. getObject 多字段
********. 形式
getObject(["colume1", "column2"])
********. 参数介绍
参数	类型	说明	是否必须
fileds	字符串数组	List中数据的字段名	是
********. 使用示例
1、获取List类型数据的指定字段
获取map类型数据的子项
<data id=" getObject" description="getObject测试">
  <![CDATA[
    $Q.list("Demo.test_data"). getObject(["colume1", "column2"])
  ]]>
</data>

表示对list获取的结果，取出其中名为colume1, column2数据，返回List<Objecct>
结果示例：
[
  {
    "colume1": "nice",
    "column2": "data"
  },
  {
    "colume1": "nice",
    "column2": "nice1"
  }
]

5.1.15. remove
********. 形式
remove(["column1", "column2"])
********. 参数介绍
参数	类型	说明	是否必须
fileds	List<String>	List类型中数据的字段名	是
********. 使用示例
1、移除List类型数据中的指定字段
	<data id=" remove" description=" remove测试">
	   <![CDATA[
	       $Q.list("Demo.test_data").remove([“column1”])
	   ]]>
	</data> 

表示对list获取的结果，移除字段column1
结果示例：
[
  {
    "column2": "data"
  },
  {
    "column2": "nice1"
  }
]

5.1.16. getString
********. 形式

getString()

********. 参数介绍
无
********. 使用示例
1、调用当前List的toString()方法，将之返回
<data id="map_getMap" description="map测试">
  <![CDATA[
    $Q.list("Demo.select_demo_char_income").getString()
  ]]>
</data>
结果示例：
[
			{id=1, month=一月, income=322111, outgoings=30000
			},
			{id=2, month=二月, income=580980, outgoings=40000
			},
			{id=3, month=三月, income=432122, outgoings=40000
			},
			{id=4, month=四月, income=553533, outgoings=50000
			},
			{id=5, month=五月, income=678888, outgoings=50000
			},
			{id=6, month=六月, income=300020, outgoings=10000
			},
			{id=7, month=七月, income=213123, outgoings=15000
			},
			{id=8, month=八月, income=434231, outgoings=30000
			},
			{id=9, month=九月, income=523242, outgoings=50000
			},
			{id=10, month=十月, income=362367, outgoings=25000
			},
			{id=11, month=十一月, income=708236, outgoings=40000
			},
			{id=12, month=十二月, income=786503, outgoings=60000
			}
		]
5.1.17. getJson
********. 形式
getJson()
********. 参数介绍
无
********. 使用示例
1、将当前List转换为json类型的字符串返回
<data id="map_getMap" description="map测试">
  <![CDATA[
    $Q.list("Demo.select_demo_char_income").getJson()
  ]]>
</data>
结果示例：
[
			{
				"id": 1,
				"month": "一月",
				"income": 322111,
				"outgoings": 30000
			},
			{
				"id": 2,
				"month": "二月",
				"income": 580980,
				"outgoings": 40000
			},
			{
				"id": 3,
				"month": "三月",
				"income": 432122,
				"outgoings": 40000
			},
			{
				"id": 4,
				"month": "四月",
				"income": 553533,
				"outgoings": 50000
			},
			{
				"id": 5,
				"month": "五月",
				"income": 678888,
				"outgoings": 50000
			},
			{
				"id": 6,
				"month": "六月",
				"income": 300020,
				"outgoings": 10000
			},
			{
				"id": 7,
				"month": "七月",
				"income": 213123,
				"outgoings": 15000
			},
			{
				"id": 8,
				"month": "八月",
				"income": 434231,
				"outgoings": 30000
			},
			{
				"id": 9,
				"month": "九月",
				"income": 523242,
				"outgoings": 50000
			},
			{
				"id": 10,
				"month": "十月",
				"income": 362367,
				"outgoings": 25000
			},
			{
				"id": 11,
				"month": "十一月",
				"income": 708236,
				"outgoings": 40000
			},
			{
				"id": 12,
				"month": "十二月",
				"income": 786503,
				"outgoings": 60000
			}
		]


5.1.18. getValue
********. 形式
getValue(“filed”)
********. 参数介绍
参数	类型	说明	是否必须
filed	String	字段名	是
********. 使用示例
1、获取List<Map>类型中字段为名称为filed的值，并将结果组装为数组返回
<data id="map_getMap" description="map测试">
  <![CDATA[
    $Q.list("Demo.select_demo_char_income").getValue("month")
  ]]></data>
结果示例：
[
			"一月",
			"二月",
			"三月",
			"四月",
			"五月",
			"六月",
			"七月",
			"八月",
			"九月",
			"十月",
			"十一月",
			"十二月"
		]
5.2 $Q.map
方法列表
序号	方法名称	描述
1	$Q.map	执行dm文件中的dataId，返回Map类型的数据，调用其他map方法的起点
2	getList	获取Map类型数据中的List对象，返回$Q.list
3	getMap	获取Map类型数据中的Map对象，返回$Q.map
4	getFields	获取map类型数据的指定字段名对应数据，组装为Map 返回$Q.map
5	getObject	取出Map中指定字段，返回$Q.object
6	getObject	取出Map中多个指定字段，返回$Q.object
7	extend	执行自定义函数，为了后面继续执行其他方法，建议返回ResultReprocessingMap/List对象，可执行$Q.list/map的方法
8	remove	移除Map类型数据中的指定字段，返回$Q.map
9	getString	将结果转换为String类型的字符串，相当与java中的toString方法，结果不带双引号
10	getJson	将结果转换为Json类型的字符串，结果带双引号，默认此方式返回
5.2.1. $Q.map
*******. 形式
$Q.map("dataId")
*******. 参数介绍
参数	类型	说明	是否必须
dataId	String	dm文件数据源id	是
params	Map	执行dataId的参数	否
*******. 使用示例
1、获取Map类型数据的子项
获取map类型数据的子项
<data id="map" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data")
  ]]>
</data>

结果示例：
{
  "dataId": "Demo.insert_demo_vacate",
  "data": {
    "id": "1",
    "staffId": "123456",
    "name": "小明",
    "text": "天气真不错",
    "notes": "放假",
    "counts": "4"
  },
  "list": [
    {
      "yesterday": "hello",
      "today": "2",
      "tomorrow": "阴天"
    },
    {
      "yesterday": "java",
      "today": "1",
      "tomorrow": "阴天"
    },
    {
      "yesterday": "world",
      "today": "3",
      "tomorrow": "阴天"
    }
  ]
}

5.2.2. getList
*******. 形式
getList("field")
*******. 参数介绍
参数	类型	说明	是否必须
filed	String	Map类型数据的子项字段名 	是
*******. 使用示例
1、获取Map类型数据的List类型子项
<data id="list" description="map测试">
  <![CDATA[
    $Q.map("TagTest.url_get").getList(“list”)
  ]]>
</data>

结果示例：
[
  {
    "yesterday": "hello",
    "today": "2",
    "tomorrow": "阴天"
  },
  {
    "yesterday": "java",
    "today": "1",
    "tomorrow": "阴天"
  },
  {
    "yesterday": "world",
    "today": "3",
    "tomorrow": "阴天"
  }
]

5.2.3. getMap
*******. 形式
getMap("filed")
*******. 参数介绍
参数	类型	说明	是否必须
filed	String	Map类型数据的子项字段名 	是
*******. 使用示例
1、获取Map类型数据的Map子项
获取map类型数据的子项
<data id="map_getMap" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getMap("data")
  ]]>
</data>

结果示例：
{
  "data": {
    "id": "1",
    "staffId": "123456",
    "name": "小明",
    "text": "天气真不错",
    "notes": "放假",
     "counts": "4"
  }
}

5.2.4. getFields
*******. 形式
getFields(["field1", "field2"])
*******. 参数介绍
参数	类型	说明	是否必须
fileds	List<String>	Map类型数据的子项字段名 	是
*******. 使用示例
1、获取Map类型数据的指定子项，组合为新的Map
<data id="map_getFields" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getMap("data").getFields(["name", "text"])
  ]]>
</data>

结果示例：
{
  "name": "小明",
  "text": "天气真不错"
}

5.2.5. getObject
*******. 形式
getObject("filed")
*******. 参数介绍
参数	类型	说明	是否必须
filed	String	Map类型数据的子项字段名 	是
*******. 使用示例
1、 获取Map类型数据的子项, 返回Object类型数据
获取map类型数据的子项
<data id="map_getObject" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getMap("data").getObject("notes")
  ]]>
</data>

结果示例：
{
  "note字段内容"
}

5.2.6. getObject 多字段
*******. 形式
getObject([“field1”, “field2”])
*******. 参数介绍
参数	类型	说明	是否必须
fileds	List<String>	Map类型数据的子项字段名 	是
*******. 使用示例
1、获取Map类型数据的子项
获取map类型数据的子项
<data id="map_getObjects" description="map测试">
   <![CDATA[
    $Q.map("TagTest.test_data").getMap("data").getObject(["notes", "name"])
  ]]>
</data>

结果示例：
[
  {
    resultA,
    resultB
  }
]

5.2.7. remove
*******. 形式
remove(["column1", "column2"])
*******. 参数介绍
参数	类型	说明	是否必须
fileds	List<String>	Map类型数据的子项字段名 	是
*******. 使用示例
1、获取Map类型数据的子项
获取map类型数据的子项
<data id="map_remove" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getMap("data").remove(["id", "staffid"])
  ]]>
</data>

结果示例：
"data": {
  "name": "小明",
  "text": "天气真不错",
  "notes": "放假",
  "counts": "4"
}

5.1.8. getString
*******. 形式
getString()
*******. 参数介绍
无
*******. 使用示例
1、将Map转换为字符串返回，相当于调用Map的toString()方法
<data id="map_remove" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getString()
  ]]>
</data>
结果示例：
{id=1, month=一月, income=322111, outgoings=30000
			}
5.2.9. getJson
*******. 形式
getJson()
*******. 参数介绍
无
*******. 使用示例
1、将Map转换为Json字符串返回
<data id="map_remove" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getJson()
  ]]>
</data>
结果示例：
{
				"id": 1,
				"month": "一月",
				"income": 322111,
				"outgoings": 30000
			}

5.2.10. getValue
********. 形式
getValue(“filed”)
********. 参数介绍
参数	类型	说明	是否必须
filed	String	字段名	是
********. 使用示例
1、获取Map类型中字段为名称为filed的值
<data id="map_getMap" description="map测试">
  <![CDATA[
    $Q.map("TagTest.test_data").getValue("month")
  ]]></data>
结果示例：
"二月"
5.3 $Q.object
方法列表
序号	方法名称	描述
1	$Q.object	执行dm文件中的dataId，返回Object类型的数据
2	getString	将结果转换为String类型的字符串，相当与java中的toString方法，结果不带双引号
3	getJson	将结果转换为Json类型的字符串，结果带双引号，默认此方式返回
4	extend	执行自定义函数，为了后面继续执行其他方法，建议返回ResultReprocessingMap/List对象，可调用$Q.list/map的方法
使用方式同map 标签 
5.4. 变量
目前dm文件中的辅助方法包括以下：
5.4.1. $P
主要获取前端url上传递的参数，如：获取dataId的参数值，$P.dataId
5.4.2. $S
主要获取session连接中的信息，目前只有请求者的ip，获取方法：$S.ip
5.4.3. $C
1、获取application.yml/properties的配置文件内容
如，获取配置文件中端口信息，获取方法：$C.get(“server.port”)
2、生成匹配时间段的正则表达式
如：生成匹配202401020800前3小时，后5小时的时间范围表达式。
$C.hourRange("202401020800","-3","5")
其中，第二个参数-3表示前三个小时，5表示后五小时，表达式最后输出结果将匹配202401020500至202401021300之间的时间。
以下为目前的方法：
方法名称	说明	使用方式
hourRange	小时	$C.hourRange("2024010208","-3","5")
dayRange	日	$C.dayRange("20240102","-3","5")
minuteRange	分钟	$C.minuteRange("202401020800","-3","5")
yearRange	年	$C.yearRange("2024","-1","2")
monthRange	月	$C.monthRange("202401","-1","2")
secondRange	秒	$C.secondRange("20240102080000","-55","60")
注意：目前支持的时间格式支持yyyyMMddHHmmss形式，暂时不支持yyyy-MM-dd HH:mm:ss以及其他格式
5.4.4. $Q
目前有三个方法list、map、object，可以执行其他dm标签，一般配合data标签使用。
1、用于自定义数据的返回格式
2、用于对其他标签返回的数据做初步统计与处理
用法参考 $Q.list、$Q.map、$Q.object
5.4.5. 扩展$S中的属性
平台允许用户自己扩展$S中的属性，分为以下3步。
1、创建Component组件
创建平台的Component组件，取名为TestWComponent.java
如下所示：
package com.sunsheen.hearken.meteo.base.data.dm.velocity;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.sunsheen.jfids.system.bizass.annotation.BixComponentPackage;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;


/**
 * 
 */
@Service("TestWComponent")
@BixComponentPackage(dirname = "扩展dm文件中的$S变量示例", type = "SYSTEM")
public class TestWComponent extends ABaseComponent {
		
    @Override
    @Component(name = "TestWComponent", memo = "扩展dm文件中的$S变量示例")
    @Params({
            @ParamItem(type = "java.lang.String", name = "dataId", comment = "数据源id"),
            @ParamItem(type = "java.lang.Object...", name = "params", comment = "数据源查询参数")})
    @Returns(retValue = {@ReturnItem(type = "java.lang.String", name = "data", comment = "返回查询结果")})
    public Object run(Map param) {
    	System.out.println("++++++TestWComponent++++++");

    	Map S = (Map)param.get("S");
    	S.put("ss", "测试1234567");
    	

        return S;
    }
    
}


从参数中获取S对象，此对象即为$S，类型为Map。
向S中添加属性"ss"
S.put("ss", "测试1234567");

2、创建配置文件
在项目的src/main/resources目录下创建后缀名为.velocity的文件，如下图：

内容为“dmcomponent = TestW”
可设置多个组件，使用逗号隔开
3、在dm文件中应用
应用方式与其他属性一致，获取属性ss的值，写法：$S.ss
5.5其他扩展方法
5.5.1.#idString()
在dm文件中自动生成String类型的id，不超过64位，使用方式如下：
<database id="insert_demo_vacate" resultMap="java.lang.Integer" description="请假管理新增">
insert into demo_vacate (id,name) values('#idString()',:name)
</database>
5.5.2.#idLong()
在dm文件中自动生成Long类型的id，不超过64位，使用方式如下：
<database id="insert_demo_vacate" resultMap="java.lang.Integer" description="请假管理新增">
insert into demo_vacate (id,name) values('#idLong()',:name)
</database>
5.5.3扩展自定义的#方法
如：在dm文件中扩展#testString，实现两个字符串的拼接功能。
使用方式为：#testString(“test1”,“test2”)
1、创建TestStringVelocity.java（名称可自定义）
继承org.apache.velocity.runtime.directive.Directive类
package com.sunsheen.jfids.app.stinprline.equ.service;

import java.io.IOException;
import java.io.Writer;

import org.apache.velocity.context.InternalContextAdapter;
import org.apache.velocity.exception.MethodInvocationException;
import org.apache.velocity.exception.ParseErrorException;
import org.apache.velocity.exception.ResourceNotFoundException;
import org.apache.velocity.runtime.directive.Directive;
import org.apache.velocity.runtime.parser.node.Node;

public class TestStringVelocity extends Directive {
@Override
public String getName() {
return "testString";
}

@Override
public int getType() {
return LINE;
}

@Override
public boolean render(InternalContextAdapter context, Writer writer,
Node node) throws IOException, ResourceNotFoundException,
ParseErrorException, MethodInvocationException {
String value0 = (String) node.jjtGetChild(0).value(context);
String value1 = (String) node.jjtGetChild(1).value(context);
writer.write(value0 + value1);
return true;
}
}
注意：在getName()方法中，返回名称为“testString”，此名称就是dm文件中的方法名称
2、创建配置文件
在项目的src/main/resources目录下创建后缀名为.velocity的文件，添加内容如下图所示：

userdirective有多个值就用逗号隔开。
3、应用
重启项目，在dm文件中如下使用：

6. dm中的标签
6.1. filelist
标签filelist表示从系统磁盘上获取文件列表，最后返回List<Map>类型的数据
6.1.1. 属性说明
名称	类型	说明	使用方式
id	通用	在dm文件中的id值，唯一	id="myDataId"
description	通用	标签备注	description="最顶层目录暂时不能为正则,递归获取文件夹下的文件"
recursion	私有	值为true，表示递归获取文件	recursion="true"
6.1.2. 使用方式
<filelist id="queryr" recursion="true" description="最顶层目录暂时不能为正则,递归获取文件夹下的文件">
  <![CDATA[
    C:/Users/<USER>/Desktop/ceshi/.*
  ]]>
</filelist>

表示从目录C:/Users/<USER>/Desktop/ceshi/中获取文件列表，此目录可为正则表达式，文件名称可为正则表达式。
如：C:/Users/<USER>/Desktop/(ceshi|test)/.*
注意：根目录暂时不支持正则表达式，目录必须使用正斜杠/
6.1.3. 返回值
[
  {
    "fileName": "文件名称",
    "filePath": "文件全路径",
    "fileParent": "文件所在的父目录"
  }
]

示例：
[
  {
    "fileName": "1112.txt",
    "filePath": "C:\\Users\\<USER>\\Desktop\\ceshi\\1112.txt",
    "fileParent": "C:\\Users\\<USER>\\Desktop\\ceshi"
  }
]

6.2. data
标签data表示自定义数据格式，最后返回自定义数据结构。返回时，将数据结构尝试转换为Map或List
支持字段重命名
6.2.1. 属性说明
名称	类型	说明	使用方式
id	通用	在dm文件中的id值，唯一	
description	通用	标签备注	description="data标签应用示例"
6.2.2. 使用方式
<data id="testU" description="data标签应用示例">
  <![CDATA[
    $Q.list("Demo.select_demo_vacate").group("max",["staffId"],["staffName"])
  ]]>
</data>

6.2.3. 结果实例
[
  {
    "staffName": "staffName1",
    "staffId": "staffId1",
  },
  {
    "staffName": "staffName2",
    "staffId": "staffId1",
  }
]

6.3. database
标签database表示对数据库做增删改查，查询时支持字段重命名。
6.3.1. 属性说明
名称	类型	说明	使用方式
id	String	在dm文件中的唯一id值	id="mydataId"
description	String	标签备注	description="data标签应用示例"
6.3.2. 使用方式
数据查询：
<database id="select_demo_char_msbar" resultMap="java.util.HashMap" description="数据查询">
  <![CDATA[
    select * from demo_chart_msbar where 1=1
    #if ($P.country and $P.country!='')
      and country in ($P.country)
    #end
  ]]>
</database>

数据新增
<database id="tree_insert" resultMap="java.lang.Integer" description="数据新增">
insert into demo_tree(id,belongto,grade,iconcls,memo,sequence,text,adddate,checkbox) 
values(:id,:belongto,:grade,:iconcls,:memo,:sequence,:text,:adddate,:checkbox)
</database>

数据修改

<database id="tree_update" resultMap="java.lang.Integer" description="更数据更新">
  update demo_tree set belongto=:belongto,
    grade=:grade,iconcls=:iconcls,memo=:memo,sequence=:sequence,text=:text,
    adddate=:adddate,checkbox=:checkbox
  where id=:id
</database>

数据删除
<database id="tree_delete" resultMap="java.lang.Integer" description="删除数据">
  delete from demo_tree where id=:id
</database>

6.3.3. 返回值
增删改返回int类型，查询返回List<Map>类型数据
6.4. Elasticsearch 
Elasticsearch标签表示对Elasticsearch数据库组件进行增删改查等操作，查询时支持字段重命名，可指定返回值类型如Map等,并支持多个ES数据源配置。
6.4.1.  属性说明
名称	类型	说明	使用方式
id	通用	dm文件中的唯一id值	Id="select_es"
method	GET、POST、DELETE、PUT、HEAD、OPTION、PATCCH	请求方式	method="GET"
interface	String	Elasticsearch请求url	interface="http://localhost:9200/my_index/_search"
persistenceUnit	String	指定ES用户	persistenceUnit="myes"
resultMap	String 	指定数据返回格式	resultMap="java.util.HashMap"
description	String 	标签说明	description="标签说明"
6.4.2. 使用方式
1. 数据查询-根据id查询 
<elasticsearch id="esSelectByDsl" method="post" resultMap="java.util.HashMap" interface="http://localhost:9200/my_index/_search" description="es通过dsl查询">
  <return-scalar column="id" filed="id1"/> 
  <![CDATA[ 
  { 
    "query": { 
      "match": { 
        "age": "$P.age" 
      }  
    } 
  }  
  ]]> 
</elasticsearch>

2. 数据查询 根据dsl查询 
<!-- 单个字段查询 -->
<elasticsearch id="select_by_dsl" method="post"
  interface="http://localhost:9200/my_index/_search"
  resultMap="java.util.HashMap" description="es通过dsl查询">
  <return-scalar column="id" filed="id1" />
  <![CDATA[
  {  
    "query": {  
      "match": {  
        "age": "$P.age"  
      }
    }
  }
  ]]>
</elasticsearch>

<!-- 多字段查询 -->
<elasticsearch id="esSelectMany" method="post"
  interface="http://localhost:9200/my_index/_search"
  resultMap="java.util.HashMap" description="es通过dsl多字段查询">
  <return-scalar column="id" filed="id1" />
  <![CDATA[
  {
  "query": {
    "bool": {
    "must": [
      { 
      "match": { "age": "$P.age" } }
      #if($P.name and $P.name!=''),
      { "match": { "name": "$P.name" } }
      #end
    ]
    }
    }
  }
  ]]>
</elasticsearch>

3. 数据修改
<elasticsearch id="esUpdate" method="post" interface="http://localhost:9200/my_index/_update/3" resultMap="java.util.HashMap" description="es修改数据测试">  
  <![CDATA[  
  {    
    "doc": {    
      "name": "$P.name"   
    }
  }  
  ]]>  
</elasticsearch>

4. 数据删除-根据id删除 
<elasticsearch id="esDelete" method="delete" interface="http://localhost:9200/my_index/_doc/4"  resultMap="java.util.HashMap" description="es根据id删除数据测试">

</elasticsearch>

5. 数据删除-根据单字段删除 
<!-- 根据单个字段删除 -->
<elasticsearch id="esDeleteByQuery" method="post"
  interface="http://localhost:9200/my_index/_delete_by_query"
  resultMap="java.util.HashMap" description="es根据某个字段的值删除数据">
  {
    "query": { 
      "match": { "name": "$P.name" } 
    }
  }
</elasticsearch>

6. 数据删除-根据多个字段删除
<elasticsearch id="esDeleteMany" method="post"
        interface="http://localhost:9200/my_index/_delete_by_query"
        resultMap="java.util.HashMap" description="es根据多个字段的值删除数据">
            {
                "query": {
                    "bool": {
                        "must": [
                            { "match": { "name": "$P.name" } },
                            { "match": { "age": "$P.age" } }
                        ]
                    }
                }
            }
    </elasticsearch>

7. 数据新增
<elasticsearch id="esInsert" method="post" interface="$C.get('elasticsearch.url')/$P.indexName/_doc/$P.id" resultMap="java.util.HashMap" description="es插入数据测试">  
  <![CDATA[  
  {  
    "id":$P.id,  
    "name": "$P.name",  
    "age": "$P.age",  
    "sequence": $P.sequence  
  }  
  ]]>  
</elasticsearch>

8. ES新建索引
<elasticsearch id="add_index" method="put"
  interface="http://localhost:9200/$P.indexName"
  resultMap="java.util.HashMap" description="es新建索引">
  <![CDATA[
  {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 2
    },
    "mappings": {
      "properties": {
        "id": { "type": "text" },
        "name": { "type": "text" },
        "age": { "type": "keyword" },
        "sequence": { "type": "integer" }
      }
    }
  }
  ]]>
</elasticsearch>

9. 删除索引
<elasticsearch id="esDelIndex" method="delete"
  interface="http://localhost:9200/$P.indexName"
  resultMap="java.util.HashMap" description="es删除索引">
</elasticsearch>

10. ES多用户配置
ES支持多个用户进行访问和操作，多用户需要在.yml文件中进行配置，yml配置示例如下 (application.yml)
# ElasticSearch配置
elasticsearch:
  url: http://localhost:9200
  # 默认es源
  default:
    username: my_username
    password: my_password
  # 其它es源，名称可自定义
  kib:
    username: my_username1
    password: _ my_password

使用示例如下: (Demo.dm)
<elasticsearch id="esInsert" method="post" interface="$C.get('elasticsearch.url')/$P.indexName/_doc/$P.id" resultMap="java.util.HashMap" description="es插入数据测试"      persistenceUnit="kib">  
  <![CDATA[  
  {  
  "id":$P.id,  
  "name": "$P.name",  
  "age": "$P.age",  
  "sequence": $P.sequence  
  }  
  ]]>  
</elasticsearch>

6.4.3. 返回值
返回结果类型根据标签中resultMap属性指定如: resultMap="java.util.HashMap"，返回List<Map>类型不加resultMap属性，原样返回ES的数据
6.5.url标签
　　　向第三方接口发送请求，获取返回值。
6.5.1. 属性说明
名称	类型	说明	使用方式
id	String	通用属性，在dm文件中的id值，唯一	id="myDataId"
description	String	通用属性，标签备注	description="最顶层目录暂时不能为正则,递归获取文件夹下的文件"
resultMap	根据需求而定	通用属性	可选值：reactor.core.publisher.Flux（返回流式类型）
其他值暂时无用

method	String	私有属性，请求类型	请求类型包括：post/get/put等
interface	String	私有属性，访问的接口地址	接口地址
6.5.2. 使用方式
1、发送post请求
<url id="testurlpost" method="post" interface="http://************:9385/hearkenDemo/json/SaveData.svt" description="访问第三方接口">
<![CDATA[
{
"dataId":"Demo.insert_demo_vacate",
"data":{
"name":"张三",
"age":12
}
}
]]>
</url>

表示使用post请求访问：http://************:9385/hearkenDemo/json/SaveData.svt
参数为：
{
　　　"dataId":"Demo.insert_demo_vacate",
　　　"data":{
　　　"name":"张三",
　　　"age":12
　　　}
}
2、发送get请求
<url id="testurlList" method="get" interface="http://localhost:9385/hearkenDemo/data/QueryDataForList.svt?dataId=$P.uu" resultMap="java.util.List" description="访问第三方接口">

</url>
表示使用get请求访问http://localhost:9385/hearkenDemo/data/QueryDataForList.svt?dataId=$P.uu
6.5.3. 返回值
　　　返回第三方接口的返回值
　　　
6.5.4调用方式
1、接口调用
　　　基于通用的地址执行url标签，“xxx项目/data/QueryDataForList.svt”，参数为dataId，发送get请求,如下图所示：

2、代码调用
在调用类中注入QueryDataForListComponent组件，如下：
@Autowired
@Qualifier("QueryDataForListComponent")
IDataPort queryDataForList;
执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put("dataId","Demo.testurlList");
Object obj = this.queryDataForList.run(param);

6.5.5在线示例
http://ip:port/xxx/xxxx
7. 多数据库配置
在6.0平台中，可同时使用多个数据库，在业务上，可根据需求在不同的数据库中存取数据。
7.1. 配置方式
配置代码示例 (application.yml)：
spring:
    # 数据源名称
    datasource1:
        # 数据库方言类型
        dialect: PostgreSQLDialect
        # 数据库驱动名称
        driver-class-name: org.postgresql.Driver
        username: myusername
        password: mypassword
        url: jdbc:databasename://hostname:port/database
        # 连接池配置
        druid:
            # 初始化连接数
            initialSize: 5
              #连接池中的最小空闲连接数
              minIdle: 5
              #控制连接池中的最大活动连接数
              maxActive: 20
              # 配置获取连接等待超时的时间
              maxWait: 60000
              # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位毫秒
              timeBetweenEvictionRunsMillis: 60000
              # 配置一个连接在池中最小生存时间
              minEvictableIdleTimeMillis: 300000
              testWhileIdle: true
              testOnBorrow: false
              # 打开 PSCache，并且指定每个连接上 PSCache 的大小
              poolPreparedStatements: true
              maxPoolPreparedStatementPerConnectionSize: 20
              # 配置监控统计拦截的 Filter，去掉后监控界面 SQL 无法统计，wall 用于防火墙
              filters: stat,wall,slf4j
              # 通过 connectProperties 属性打开 mergeSql 功能；慢 SQL 记录
              connectProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
              
    # 其它数据源配置，配置格式同上datasource1
    datasource2：
        # 数据库方言类型
        dialect: Oracle10gDialect
        ...

其中，datasource为默认数据源
7.2. 使用方式
在dm文件中的 database标签下，对数据库进行操作时，可指定数据源进行操作。指定数据源时，需要在database标签中添加 persistenceUnit 属性，可直接指定值，也可以通过$P、$C等传参。参数说明如下:
参数	说明
persistenceUnit	指定数据源参数。不添加该参数时，使用默认数据源datasource
persistenceUnit="datasource1"	直接指定数据源
persistenceUnit="$P.xxx"或"$C.get(‘xxx’)"	传参指定数据源
使用代码示例如下 (test.dm)
<?xml version="1.0" encoding="UTF-8"?>  
<sqlMap namespace="TABLE_NAME" description="测试数据表">  

  <!-- 直接指定数据源 -->
  <database id="select" resultMap="java.util.HashMap" description="查询测试数据" persistenceUnit="datasource1">  
    select * from test_table1 limit 10;
  </database>  

  <!-- 传参指定数据源 -->
  <database id="select" resultMap="java.util.HashMap" description="查询测试数据" persistenceUnit="$P.xxx">  
    select * from test_table2 limit 10;
  </database>  

</sqlMap>

8、构件的封装
1、构件的命名
【强制】需要能够体现构件的具体含义，遵从java语言的大驼峰命名规则，以“Component”结尾。
如：查询数据返回list列表构件，QueryDataForListComponent
2、构件继承的父类
【强制】构件必须继承统一的父类，com.sunsheen.jfids.system.bizass.core.ABaseComponent。
3、构件类的注解
构件类的注解包括以下几种：@Service，@Controller，@Component，@Repository
各注解的含义与springboot一致，封装业务构件时一般使用前两种注解（@Service，@Controller），@Repository注解一般用于dao数据层。
添加注解之后，springboot将把构件当成bean进行管理。
构件bean的名称一般与构件类的名称一致，且全局唯一，如：
@Service("QueryDataForListComponent")
4、构件与参数定义
在构件内部，平台自定义了一个注解@Component，包含name与memo两个属性，name的值要求与构件类定义的bean名称一致
如下：
@Component(name = "QueryDataForListComponent", memo = "查询数据返回list列表构件")

参数、返回值定义如下：
@Params({@ParamItem(type = "java.lang.String", name = "dataId", comment = "数据源id"), 
    	@ParamItem(type = "java.lang.Object...", name = "params", comment = "数据源查询参数")})
    @Returns(retValue = {@ReturnItem(type = "java.lang.Object", name = "data", comment = "返回查询结果")})
5、构件的方法
所有的构件都需要重写父类的run方法，如下：
public Object run(Map param){
         //方法重写
}
6、构件参数的获取
平台中构件的所有参数都在run方法的param中，可以使用父类的getCallParam方法进行获取，如下：
String dataId = (String) this.getCallParam(param,"dataId");
getCallParam方法已经判断要获取的参数是否为null
7、QueryDataForListComponent构件封装示例
package com.sunsheen.hearken.dev.service.datasource;

import java.util.HashMap;
import java.util.Map;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import com.sunsheen.hearken.dev.dao.jform.database.DynamicDataSourceConfig;
import com.sunsheen.hearken.dev.service.utils.HKCacheValue;
import com.sunsheen.jfids.system.bizass.annotation.Component;
import com.sunsheen.jfids.system.bizass.annotation.ParamItem;
import com.sunsheen.jfids.system.bizass.annotation.Params;
import com.sunsheen.jfids.system.bizass.annotation.ReturnItem;
import com.sunsheen.jfids.system.bizass.annotation.Returns;
import com.sunsheen.jfids.system.bizass.core.ABaseComponent;
import com.sunsheen.jfids.system.bizass.port.IDataPort;


@Service("QueryDataForListComponent")
public class QueryDataForListComponent extends ABaseComponent {
	private static final Logger logger = LoggerFactory.getLogger(QueryDataForListComponent.class);
//    不同的持久层框架需要实现不同的DaoEngine、DatabaseQueryForObjectComponent等组件
//    @Autowired
//    @DaoQualifier
//    private Map<String,IDataPort> dataPort;

    @Autowired
    @Qualifier("DaoEngine")
    IDataPort daoEngineComponent;
    

    @Autowired
    private ApplicationContext ctx;
    
    @Autowired
    HKCacheValue hkCacheValue;
    
    @Override
    @Component(name = "QueryDataForListComponent", memo = "表格数据源查询，支持关系型数据库")
    @Params({@ParamItem(type = "java.lang.String", name = "dataId", comment = "数据源id"), 
    	@ParamItem(type = "java.lang.Object...", name = "params", comment = "数据源查询参数")})
    @Returns(retValue = {@ReturnItem(type = "java.lang.Object", name = "data", comment = "返回查询结果")})
    public Object run(Map param1) {
        //start 起始，limit 每页条数
        Integer start = this.getCallParamInt(param1, "start", 0);
        Integer limit = this.getCallParamInt(param1, "limit", -1);
        
        Map param = new HashMap<>();
        param.putAll(param1);
        param.put("start", start);
        param.put("limit", limit);
        
        String dataId = null;
        if(param.containsKey("dataId")) {
        	dataId = (String) this.getCallParam(param,"dataId");
        }
        if(dataId==null) {
        	throw new RuntimeException("dataId为null");
        }
        Object cacheValue = daoEngineComponent.run("getCache", param);
        if(cacheValue!=null) {//已设置缓存
        	String key = this.hkCacheValue.getKey(param);
        	Object ret = this.hkCacheValue.getValue(key);
        	if(ret!=null) {
        		return ret;
        	}
        	ret = getData(dataId,param);
        	this.hkCacheValue.setValue(key, cacheValue, ret);
        	return ret;
        }
        return getData(dataId,param);
    }
    
    private Object getData(String dataId,Map param) {
    	//获取dm文件对应的标签名称
        String statementName = (String) daoEngineComponent.run("getStatementName", param); // jform.getDaoMapping().getStatementName(dataId);
        if (dataId != null) {//dataId不为空则按dataId的方式处理
            //通过标签名得到标签对应的组件名，#约定#
            String componentName = String.format("%s%sQueryForListComponent", statementName.substring(0, 1).toUpperCase(), statementName.substring(1));
            try {
                //获取组件并执行
                IDataPort port = (IDataPort) ctx.getBean(componentName);
                return port.run(param);
            } catch (NoSuchBeanDefinitionException e) {
                throw new RuntimeException(String.format("*.dm文件中<%s>标签错误(对应的解析对象[%s]不存在)，参数dataId可能错误", statementName, componentName));
            }
        } else {
            throw new RuntimeException("请求参数dataId不能为空");
        }
    }
}


9、hearken-service的0.0.2版本
Maven配置如下：
<dependency>
<groupId>com.sunsheen</groupId>
<artifactId>hearken-service</artifactId>
<version>0.0.2</version>
</dependency>
0.0.2版本的变化内容如下：
1、通过url访问的查询与保存数据接口返回码变化。
0.0.1版本：成功的code为0，失败为1
0.0.2版本：成功的code为1，失败为0
通过注入组件调用查询与保存的返回值无变化
2、增加查询接口/data/XXX.json
此接口的返回值使用返回码进行包装，已有的/data/XXX.svt接口无变化

10、hearken-service的0.0.3版本

Maven配置如下：
<dependency>
<groupId>com.sunsheen</groupId>
<artifactId>hearken-service</artifactId>
<version>0.0.3</version>
</dependency>

0.0.3版本
新增流式返回接口：
如：调用deepseek的流式接口，http://ip:端口/项目名称/json/DeepSeepAiStream.flux
新增文件类的接口：
1、参数为json格式，返回值为文件：http://ip:端口/项目名称/json/xxx.file
　　　如文件下载：http://ip:端口/项目名称/json/FileDown.file
2、参数为file，返回值为json
　　　如文件上传：http://ip:端口/项目名称/file/FileUpload.json/
3、参数为文件，返回值为文件：http://ip:端口/项目名称/file/xxx.file

10.1 IDataPort变更
以前的：
public Object run(String funcName,Map param);
方法名称发生变化，变更后：
public Object executeFunc(String funcName,Map param);

新增以下变参run方法，将传入的参数组装为Map，再调用run(Map param)
public Object run(Object... pas);

源码如下：
public Object run(Object... pas){
try {
Class<?> clazz = this.getClass();
// 获取 run 方法的 Method 对象
Method runMethod = clazz.getMethod("run", Map.class);

// 检查方法上是否存在 @Params 注解
if (runMethod.isAnnotationPresent(Params.class)) {
// 获取 @Params 注解
Params paramsAnnotation = runMethod.getAnnotation(Params.class);
List<String> pasName = new ArrayList<String>();
// 遍历 @Params 注解中的 @ParamItem 注解
for (ParamItem paramItem : paramsAnnotation.value()) {
// 打印每个参数的信息
pasName.add(paramItem.name());
}
Map params = new HashMap<>();
for (int i = 0; i < pas.length; i++) {
if(i+1<=pasName.size()) {
params.put(pasName.get(i), pas[i]);
}else {
System.out.println(String.format("参数太多……取前%s个参数", pasName.size()));
break;
}
}
return this.run(params);
} else {
System.out.println("run 方法上没有 @Params 注解");
}
throw new RuntimeException(String.format("%s的run 方法上没有 @Params 注解",clazz.getName()));
} catch (NoSuchMethodException e) {
// TODO 自动生成的 catch 块
throw new RuntimeException(e);
} catch (SecurityException e) {
// TODO 自动生成的 catch 块
throw new RuntimeException(e);
}
}



10.2新增git标签
　　　可用java实现git仓库的代码提交、更新操作，返回的数据为提交或更新的文件列表，包括：新增、修改、删除的文件。
10.1.1. 属性说明
名称	类型	说明	使用方式
id	String	通用属性，在dm文件中的id值，唯一	id="myDataId"
description	String	通用属性，标签备注	description="最顶层目录暂时不能为正则,递归获取文件夹下的文件"
resultMap	根据需求而定	通用属性，暂无用	
type	String	私有属性，操作类型	操作类型commit为提交，update为更新
memo	String	私有属性，提交说明	操作类型为commit时，必须要此属性
repository	String	私有属性，仓库路径	.git文件所在的目录，如：D:/workspace/hearkenTest/.git
username	String	私有属性，仓库用户名	仓库用户名
password	String	私有属性，仓库密码	仓库密码
10.1.2. 使用方式
1、Git提交
<git id="gitCommit" type="commit" memo="第一次提交" resultMap="java.util.HashMap" repository="D:/workspace/hearkenTest/.git" username="test" password="100200" description="git目录">
        <![CDATA[
			./src
      	]]>
    </git>

表示使用test账号对D:/workspace/hearkenTest/.git做提交操作。
提交git中的所有的更改：.
提交git中的某个文件夹：./src，其中，“./为.git文件所在的目录”
提交某个文件：./src/11.txt
提交多个文件：./src/11.txt,./src/12.txt
注意：type属性为commit时，memo属性不能为空
2、Git更新
<git id="gitUpdate" type="update" resultMap="java.util.HashMap" repository="D:/workspace/hearkenTest/.git" username="test" password="100200" description="git目录">
       
    </git>
表示使用test账号对D:/workspace/hearkenTest/.git做更新操作。
注意：目前只支持对仓库做全部更新，不支持只更新某个文件或文件夹
10.1.3. 返回值
{
        "add": [],
        "update": [],
        "delete": []
    }
其中，add为新增的文件列表，update为修改，delete为删除，示例：
{
        "add": ["target/4.txt"],
        "update": [
            "11.txt",
            "target/2.txt"
        ],
        "delete": ["target/3.txt"]
    }

10.1.4调用方式
3、接口调用
　　　基于通用的url地址执行git标签，“xxx项目/json/SaveData.json”，参数包括dataId与data，发送post请求,如下图所示：

4、代码调用
在调用类中注入SaveDataComponent组件，如下：
@Autowired
@Qualifier("SaveDataComponent")
IDataPort saveData;


执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put("dataId","Demo.gitCommit");
Map data = new HashMap<>();
data.put("memo","提交测试");
param.put("data",data);
Object obj = this.saveData.run(param);

10.1.5在线示例
http://ip:port/xxx/xxxx
10.3新增文件上传构件
参数
名称	类型	说明	使用方式
files	文件	文件	
 data   	 { "dataId": "Test.upLoad", "saveType":  "1"  }	dataId：配置路径
saveType:1(随机文件名),2(保持原有文件名)
	
返回值
名称	类型	说明	使用方式
path	String	文件保存路径	
fileName	String	文件本地名称	
dataId	String	路径配置	
serverName	String	文件服务器名称	

调用方式

1、接口调用
接口名：http://localhost:8080/xxx项目/file/FileUpload.json   post请求




dm标签：
 <data id="upLoad" description="上传文件">
          <![CDATA[
            ./aa
          ]]>
        </data>


10.4新增文件下载构件
参数
名称	类型	说明	使用方式
dataId	String	文件路径的配置id	
 serverName	String	服务器文件名，包含扩展名
	
fileName	String	文件本地名称，包含扩展名
	
返回值
名称	类型	说明	使用方式
文件	application/octet-stream	可下载文件	
调用方式

1、接口调用
接口名：http://localhost:8080/xxx项目/json/FileDown.file   post请求
参数：

dm标签：
 <data id="upLoad" description="上传文件">
          <![CDATA[
            ./file
          ]]>
        </data>

10.5新增deepseek类构件
deepseek构件
调用deepseek模型，构件全称：DeepSeekAiComponent
参数
名称	类型	说明	使用方式
dataId	String	dm文件中的id值，唯一，在此构件中dataId对应的标签必须为url标签	如：Demo.deepseek
data	Map	参数	{“question”:”你是谁？”}
label	String	为0则保留think等标签，为1则去掉标签，默认为1	
返回值
名称	类型	说明	使用方式
answers	String	回答内容	

调用方式
1、接口调用
　　　dm文件中必须使用url标签，其配置示例如下：

<url id="deepseekTest" method="post" resultMap="java.util.HashMap" interface="http://***********:11434/api/generate" description="访问deepseek">
<![CDATA[
{
"model": "deepseek-r1:14B",
"prompt": "$P.question",
"stream": false
}
]]>
</url>
　　　说明：url标签的属性参考本文档的6.6url标签章节
　　　model-选择的deepseek大模型，此处示例使用的是14B模型
　　　prompt-发送给deepseek的问题
　　　基于通用的url地址执行DeepSeekAiComponent构件，“xxx项目/json/DeepSeekAi.json”，参数包括dataId与data，请求类型post。
　　　如下图所示：
　　　
2、代码调用
在调用类中注入DeepSeekAiComponent组件，如下：
@Autowired
@Qualifier("DeepSeekAiComponent")
IDataPort deepSeekAi;


执行以下代码即可完成调用：	

Map param = new HashMap<>();
param.put("dataId","Demo.deepseekTest");
Map data = new HashMap<>();
data.put("question","你是谁？");
param.put("data",data);
Object run = deepSeekAi.run(param);
　　　
Deepseek构件-返回流式数据
调用deepseek模型，返回的数据类型为流式结构，构件全称：DeepSeekAiStreamComponent
参数
名称	类型	说明	使用方式
dataId	String	dm文件中的id值，唯一，在此构件中dataId对应的标签必须为url标签	如：Demo.deepseek
data	Map	参数	{“question”:”你是谁？”}
返回值
名称	类型	说明	使用方式
ret	reactor.core.publisher.Flux<String>
	流式数据结构	

调用方式
3、接口调用
　　　dm文件中必须使用url标签，其配置示例如下：
<url id="deepseekTest" method="post" resultMap="reactor.core.publisher.Flux" interface="http://***********:11434/api/generate" description="访问deepseek">
<![CDATA[
{
"model": "deepseek-r1:14B",
"prompt": "$P.question",
"stream": true
}
]]>
</url>
　　　说明：url标签的属性参考本文档的6.6url标签章节
　　　model-选择的deepseek大模型，此处示例使用的是14B模型
　　　prompt-发送给deepseek的问题
　　　stream-值必须为true，表示让deepseek返回流式结果
　　　此构件中，resultMap的值必须为"reactor.core.publisher.Flux"
　　　
　　　基于通用的url地址执行DeepSeekAiStreamComponent构件，“xxx项目/json/DeepSeekAiStream.flux”，参数包括dataId与data，请求类型post。
　　　注意：url必须以”.flux”结尾，表示接口返回值为流式。
　　　如下图所示：

4、代码调用
在调用类中注入DeepSeekAiStreamComponent组件，如下：
@Autowired
@Qualifier("DeepSeekAiStreamComponent")
IDataPort deepSeekAiStream;


执行以下代码即可完成调用：	

Map param = new HashMap<>();
param.put("dataId","Demo.deepseekTest");
Map data = new HashMap<>();
data.put("question","你是谁？");
param.put("data",data);
Flux<String> ret = (Flux<String>)this.deepSeekAiStream.run(param);
　　　总结：通过接口调用DeepSeekAiStreamComponent构件，需要满足一下三点，通过代码调用需要满足下面的1、2点。
1、dataId必须使用url标签，并且resultMap的值必须为"reactor.core.publisher.Flux"
2、参数中的stream-值必须为true，表示让deepseek返回流式结果
3、接口的url地址必须以“.flux”结尾，表示返回流式结构的数据给浏览器。

10.6表格数据源构件GridDataComponent
功能说明
传入dataId，获取表格类型的数据
参数
data/GridData.svt
参数	类型	是否必须	说明	示例
dataId	String	是	dm文件的数据源id	Demo.select_demo_char_income
其他参数，如：sex	String	否	自定义参数	
返回值
　　与前端的grid数据表格对应使用，数据展示与grid列表中
示例：
{
    "metaData": {
        "fields": [
            {
                "name": "id"
            },
            {
                "name": "staffid"
            },
            {
                "name": "name"
            },
            {
                "name": "text"
            },
            {
                "name": "timebegin"
            },
            {
                "name": "timeend"
            },
            {
                "name": "apply_state"
            },
            {
                "name": "runid"
            },
            {
                "name": "notes"
            },
            {
                "name": "counts"
            }
        ],
        "totalProperty": "count",
        "root": "data"
    },
    "count": 5,
    "data": [
        {
            "id": "023",
            "staffid": "21122",
            "name": "蛤蛤",
            "text": "请假原因",
            "timebegin": "2024-03-05",
            "timeend": "2024-03-06",
            "apply_state": "",
            "runid": "",
            "notes": "备注",
            "counts": null
        }
    ],
    "currPageStatData": null,
    "allPageStatData": null
}

调用方式
1、前端调用：
地址：/XXX项目前缀/data/GridData.svt?dataId=XXX
调用方式：GET
参数放置于url中

2、后端通过代码调用：
在调用类中注入GridDataComponent组件，如下：
@Autowired
@Qualifier("GridDataComponent")
IDataPort gridData;

执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put(“dataId”,“Demo.select”)
//其他参数
param.put(“xxx”,“xxx”)
Map<String, Object> dataList = (Map<String, Object>)this.gridData.run(param);

表格分页
在param中传入start与；limit进行分页，如下：
start表示表格页码，从1开始。limit表示每页条数。

Sql执行时，将自动在末尾添加分页语句：

注意：
分页时需要在数据库配置中设置方言，如下：


mysql的方言为：“MySQLDialect”
达梦、oracle的方言为：“Oracle10gDialect”
postgresql数据库的方言为：“PostgreSQLDialect”
10.7树的数据源构件TreeDataComponent
功能说明
传入dataId，获取树形结构类型的数据
参数
data/TreeData.svt
参数	类型	是否必须	说明	示例
dataId	String	是	dm文件的数据源id	Demo.tree
其他参数，如：sex	String	否	自定义参数	
返回值
　　与前端的树组件对应使用，数据展示为一颗树
示例：

调用方式
1、前端调用：
地址：/XXX项目前缀/data/GridData.svt?dataId=XXX
调用方式：GET
参数放置于url中

2、后端通过代码调用：
在调用类中注入TreeDataComponent组件，如下：
@Autowired
@Qualifier("TreeDataComponent")
IDataPort treeData;

执行以下代码即可完成调用：
Map param = new HashMap<>();
param.put(“dataId”,“Demo.tree”)
//其他参数
param.put(“xxx”,“xxx”)
Map<String, Object> dataList = (Map<String, Object>)this.treeData.run(param);

注意：
数据库表字段需要包括字段：
