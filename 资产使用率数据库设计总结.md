# 资产使用率数据库设计总结文档

## 1. 表结构设计

### 1.1 完整建表语句

```sql
CREATE TABLE asset_usage_rate (
    -- 基础标识字段
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_code VARCHAR(20) NOT NULL COMMENT '资产编号，关联asset_Registration表',
    asset_name VARCHAR(100) COMMENT '资产名称',
    asset_category VARCHAR(50) COMMENT '资产类别',
    department_name VARCHAR(100) COMMENT '使用部门',
    
    -- 统计周期
    statistics_year INT NOT NULL COMMENT '统计年度',
    statistics_month INT COMMENT '统计月份（可选）',
    
    -- 核心机时统计字段
    annual_rated_hours INT DEFAULT 1600 COMMENT '年额定机时数（小时）',
    annual_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年使用机时数（小时）',
    annual_shared_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年共享机时数（小时）',
    annual_fault_downtime_hours DECIMAL(10,2) DEFAULT 0 COMMENT '年故障停机机时数（小时）',
    
    -- 服务统计字段
    teaching_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '教学服务机时数',
    research_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '科研服务机时数',
    external_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '对外服务机时数',
    training_service_hours DECIMAL(10,2) DEFAULT 0 COMMENT '培训服务机时数',
    
    -- 收费统计字段
    internal_fee_income DECIMAL(15,2) DEFAULT 0 COMMENT '校内收费收入（元）',
    external_fee_income DECIMAL(15,2) DEFAULT 0 COMMENT '对外服务收费收入（元）',
    maintenance_cost DECIMAL(15,2) DEFAULT 0 COMMENT '维护运行成本（元）',
    
    -- 人员配备字段
    operator_count INT DEFAULT 0 COMMENT '操作人员数量',
    certified_operator_count INT DEFAULT 0 COMMENT '持证上岗人员数量',
    technician_count INT DEFAULT 0 COMMENT '技术人员数量',
    
    -- 计算结果字段
    effective_usage_hours DECIMAL(10,2) DEFAULT 0 COMMENT '有效使用机时数',
    usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '使用率（%）',
    effective_usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '有效使用率（%）',
    shared_rate DECIMAL(5,2) DEFAULT 0 COMMENT '共享率（%）',
    
    -- 评价字段
    usage_level VARCHAR(20) DEFAULT '待评估' COMMENT '使用等级：优秀、良好、合格、不合格、待评估',
    warning_status VARCHAR(20) DEFAULT '正常' COMMENT '预警状态：正常、关注、黄牌整改',
    warning_level INT DEFAULT 0 COMMENT '预警级别：0-正常，1-关注，2-黄牌整改',
    
    -- 设备分类字段
    is_large_equipment TINYINT(1) DEFAULT 0 COMMENT '是否为大型设备（≥10万元）',
    is_precious_equipment TINYINT(1) DEFAULT 0 COMMENT '是否为贵重设备（≥40万元）',
    equipment_value DECIMAL(15,2) COMMENT '设备价值（元）',
    purchase_date DATE COMMENT '购置日期',
    
    -- 共享管理字段
    is_shared_platform TINYINT(1) DEFAULT 0 COMMENT '是否纳入共享平台',
    shared_platform_name VARCHAR(100) COMMENT '共享平台名称',
    open_time_per_week INT DEFAULT 0 COMMENT '每周开放时间（小时）',
    
    -- 效益评价字段
    annual_benefit_score DECIMAL(5,2) DEFAULT 0 COMMENT '年度效益评分',
    benefit_evaluation_level VARCHAR(20) COMMENT '效益评价等级',
    
    -- 审计字段
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 索引设计
    UNIQUE KEY uk_asset_year_month (asset_code, statistics_year, statistics_month),
    INDEX idx_department_year (department_name, statistics_year),
    INDEX idx_usage_level (usage_level),
    INDEX idx_large_equipment (is_large_equipment),
    INDEX idx_precious_equipment (is_precious_equipment),
    INDEX idx_shared_platform (is_shared_platform),
    INDEX idx_warning_status (warning_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产使用率统计表（符合教育部政策要求）';
```

## 2. 字段分类说明

### 2.1 基础信息字段

| 字段名 | 类型 | 长度 | 是否必填 | 说明 | 政策依据 |
|--------|------|------|----------|------|----------|
| id | BIGINT | - | 是 | 主键ID | - |
| asset_code | VARCHAR | 20 | 是 | 资产编号，关联asset_Registration表 | 政策第四条 |
| asset_name | VARCHAR | 100 | 否 | 资产名称 | 政策第六条 |
| asset_category | VARCHAR | 50 | 否 | 资产类别 | 政策第三条 |
| department_name | VARCHAR | 100 | 否 | 使用部门 | 政策第二条 |
| statistics_year | INT | - | 是 | 统计年度 | 政策第二十一条 |
| statistics_month | INT | - | 否 | 统计月份（可选） | - |

### 2.2 机时统计字段

| 字段名 | 类型 | 默认值 | 说明 | 计算用途 |
|--------|------|--------|------|----------|
| annual_rated_hours | INT | 1600 | 年额定机时数（小时） | 使用率计算基准 |
| annual_usage_hours | DECIMAL(10,2) | 0 | 年使用机时数（小时） | 使用率计算分子 |
| annual_shared_hours | DECIMAL(10,2) | 0 | 年共享机时数（小时） | 共享率计算 |
| annual_fault_downtime_hours | DECIMAL(10,2) | 0 | 年故障停机机时数（小时） | 有效使用率计算 |

### 2.3 服务分类字段

| 字段名 | 类型 | 政策要求 | 收费规定 |
|--------|------|----------|----------|
| teaching_service_hours | DECIMAL(10,2) | 教学使用统计 | 不得收费（政策第十六条） |
| research_service_hours | DECIMAL(10,2) | 科研使用统计 | 可收取部分机时费 |
| external_service_hours | DECIMAL(10,2) | 对外服务统计 | 按规定收取机时费 |
| training_service_hours | DECIMAL(10,2) | 培训服务统计 | 按规定收费 |

## 3. 核心计算公式

### 3.1 使用率计算
```
使用率 = (年使用机时数 / 年额定机时数) × 100%
```

### 3.2 有效使用率计算
```
有效使用率 = ((年使用机时数 - 年故障停机机时数) / 年额定机时数) × 100%
```

### 3.3 共享率计算
```
共享率 = (年共享机时数 / 年使用机时数) × 100%
```

### 3.4 标准机时设定
- **大型仪器设备**（≥10万元）：800-1600小时/年
- **通用设备**：1200-1600小时/年
- **专用设备**：根据设备特性设定

## 4. 预警机制设计

### 4.1 预警级别划分

| 预警级别 | 使用率范围 | 预警状态 | warning_level | 管理措施 |
|----------|------------|----------|---------------|----------|
| 0级（正常） | ≥ 60% | 正常 | 0 | 常规管理 |
| 1级（关注） | 40% ≤ 使用率 < 60% | 关注 | 1 | 加强监控，分析原因 |
| 2级（黄牌） | < 40% | 黄牌整改 | 2 | 列入整改名单，限期改善 |

### 4.2 评价等级标准

| 等级 | 使用率范围 | 评价标准 | 备注 |
|------|------------|----------|------|
| 优秀 | ≥ 70% | 校级标准 | 超过教育部要求 |
| 良好 | 60% ≤ 使用率 < 70% | 教育部基准线 | 符合基本要求 |
| 合格 | 40% ≤ 使用率 < 60% | 需要改进 | 关注类设备 |
| 不合格 | < 40% | 整改要求 | 黄牌整改设备 |

## 5. 索引策略

### 5.1 主要索引设计

| 索引名 | 索引类型 | 字段 | 用途 |
|--------|----------|------|------|
| PRIMARY | 主键索引 | id | 主键约束 |
| uk_asset_year_month | 唯一索引 | asset_code, statistics_year, statistics_month | 确保统计周期唯一性 |
| idx_department_year | 复合索引 | department_name, statistics_year | 部门年度统计查询 |
| idx_usage_level | 单列索引 | usage_level | 评级查询优化 |
| idx_large_equipment | 单列索引 | is_large_equipment | 大型设备筛选 |
| idx_warning_status | 单列索引 | warning_status | 预警状态查询 |
