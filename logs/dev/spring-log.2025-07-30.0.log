22:29:59.924 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 39868 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
22:29:59.926 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
22:30:04.834 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
22:30:05.422 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
22:30:05.431 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
22:30:05.432 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
22:30:05.432 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
22:30:05.460 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
22:30:05.460 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5509 ms
22:30:05.621 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
22:30:05.644 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:30:05.646 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
22:30:05.646 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
22:30:05.646 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
22:30:05.648 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:30:05.648 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
22:30:05.648 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
22:30:05.648 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
22:30:05.787 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
22:30:05.787 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
22:30:08.819 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
22:30:09.160 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
22:30:09.977 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
22:30:10.050 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
22:30:10.062 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
22:30:10.077 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 10.75 seconds (process running for 11.277)
22:30:10.083 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:10s
22:30:10.677 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:30:10.677 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
22:30:10.679 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
22:30:10.712 [RMI TCP Connection(1)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
22:30:26.447 [http-nio-19385-exec-3] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
22:30:26.501 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
22:30:26.515 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
22:30:26.515 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:30:51.292 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
22:30:51.296 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
22:30:54.216 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 16268 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
22:30:54.217 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
22:30:55.309 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=aed10275-ba3a-38d8-a061-8955c4e36fd4
22:30:55.826 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
22:30:55.834 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
22:30:55.835 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
22:30:55.835 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
22:30:55.859 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
22:30:55.859 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1618 ms
22:30:56.128 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
22:30:56.129 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
22:30:57.797 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
22:30:58.123 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
22:30:58.888 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
22:30:58.961 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
22:30:58.972 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
22:30:58.986 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.237 seconds (process running for 5.585)
22:30:58.992 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
22:30:59.516 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:30:59.517 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
22:30:59.518 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
22:30:59.545 [RMI TCP Connection(1)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
22:32:26.218 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
22:32:26.222 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
22:32:29.009 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 45180 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
22:32:29.010 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
22:32:30.086 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
22:32:30.606 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
22:32:30.614 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
22:32:30.615 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
22:32:30.615 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
22:32:30.640 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
22:32:30.640 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1607 ms
22:32:30.805 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
22:32:30.825 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:32:30.827 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
22:32:30.827 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
22:32:30.827 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
22:32:30.828 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:32:30.829 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
22:32:30.829 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
22:32:30.829 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
22:32:30.952 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
22:32:30.952 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
22:32:32.641 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
22:32:32.998 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
22:32:33.792 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
22:32:33.865 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
22:32:33.876 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
22:32:33.892 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.349 seconds (process running for 5.698)
22:32:33.901 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
22:32:34.307 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:32:34.307 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
22:32:34.309 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
22:32:34.338 [RMI TCP Connection(3)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
22:32:39.385 [http-nio-19385-exec-2] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
22:32:39.433 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
22:32:39.445 [http-nio-19385-exec-2] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
22:32:39.446 [http-nio-19385-exec-2] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:32:39.676 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:32:40.095 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:32:40.095 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
22:32:40.095 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:32:40.095 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:32:40.106 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
22:32:40.106 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
22:32:40.111 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
22:32:40.111 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
22:32:40.120 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
22:32:40.120 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
22:32:40.126 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=0, path=views\arrivalRegistration\assetBigScreen\index.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:29}, {menuId=49, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=0, path=views\DataCollection\DataCollection.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:58}, {menuId=50, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=0, path=views\ScienceBoard\ScienceBoard.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:23}, {menuId=52, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=0, path=views\Purchase\Purchase.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:39}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=null, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=56, parentId=35, parentName=一阶管控中心, menuName=采购管理, orderNum=1, path=views\invOutBill\purchase\purchase.vue, component=null, query=null, routeName=采购管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-22T09:13:38, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=28, parentId=25, parentName=基础信息维护, menuName=字典管理, orderNum=3, path=views/borrow/dictionaryManagement/index.vue, component=null, query=null, routeName=字典管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=55, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
22:32:40.176 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:33:18.496 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
22:33:18.509 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
22:33:18.509 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:33:19.192 [http-nio-19385-exec-10] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:33:19.214 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:33:19.214 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
22:33:19.214 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:33:19.214 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:33:19.217 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
22:33:19.217 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
22:33:19.222 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
22:33:19.222 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
22:33:19.227 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
22:33:19.227 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
22:33:19.233 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=0, path=views\arrivalRegistration\assetBigScreen\index.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:29}, {menuId=49, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=0, path=views\DataCollection\DataCollection.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:58}, {menuId=50, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=0, path=views\ScienceBoard\ScienceBoard.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:23}, {menuId=52, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=0, path=views\Purchase\Purchase.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:39}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=null, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=56, parentId=35, parentName=一阶管控中心, menuName=采购管理, orderNum=1, path=views\invOutBill\purchase\purchase.vue, component=null, query=null, routeName=采购管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-22T09:13:38, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=28, parentId=25, parentName=基础信息维护, menuName=字典管理, orderNum=3, path=views/borrow/dictionaryManagement/index.vue, component=null, query=null, routeName=字典管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=55, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
22:33:19.249 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:33:36.632 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_total_amount
22:33:36.632 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_category_stats
22:33:36.632 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            month_num,
            CASE month_num
                WHEN 1 THEN '1月'
                WHEN 2 THEN '2月'
                WHEN 3 THEN '3月'
                WHEN 4 THEN '4月'
                WHEN 5 THEN '5月'
                WHEN 6 THEN '6月'
                WHEN 7 THEN '7月'
                WHEN 8 THEN '8月'
                WHEN 9 THEN '9月'
                WHEN 10 THEN '10月'
                WHEN 11 THEN '11月'
                WHEN 12 THEN '12月'
            END as month_name,

            -- 今年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pi.budget ELSE 0 END), 0) as current_year_budget,
            -- 去年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pi.budget ELSE 0 END), 0) as last_year_budget,

            -- 今年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.total_price ELSE 0 END), 0) as current_year_goods_amount,
            -- 去年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.total_price ELSE 0 END), 0) as last_year_goods_amount,

            -- 今年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as current_year_actual,
            -- 去年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as last_year_actual,

            -- 今年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_project_count,
            -- 去年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN 1 ELSE 0 END) as last_year_project_count,

            -- 今年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.pg_id ELSE NULL END) as current_year_goods_count,
            -- 去年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.pg_id ELSE NULL END) as last_year_goods_count,

            -- 今年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pa.pa_id ELSE NULL END) as current_year_attachment_count,
            -- 去年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pa.pa_id ELSE NULL END) as last_year_attachment_count

        FROM (
            SELECT 1 as month_num UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION
            SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
        ) months
        LEFT JOIN pms_project_info pi ON MONTH(pi.apply_time) = months.month_num
            AND YEAR(pi.apply_time) IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
            AND pi.is_delete = 0
        LEFT JOIN pms_project_goods pg ON pi.AppliProCode = pg.AppliProCode
        LEFT JOIN pms_project_attachment pa ON pi.AppliProCode = pa.AppliProCode
        GROUP BY month_num, month_name
        ORDER BY month_num
22:33:36.632 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN purcharse_type = '货物' THEN '货物'
                WHEN purcharse_type = '服务' THEN '服务'
                WHEN purcharse_type IN ('工程', '工程_零星维修') THEN '工程'
                ELSE '其他'
            END as category,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY category
        ORDER BY total_budget DESC
22:33:36.633 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_content_stats
22:33:36.633 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN content IS NULL OR content = '' THEN '未填写'
                ELSE content
            END as content_name,
            COUNT(*) as project_count
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY content_name
        ORDER BY project_count DESC
22:33:36.634 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_project_count
22:33:36.635 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            COUNT(*) as total_projects,
            SUM(CASE WHEN p_status = '00' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN p_status = '01' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN p_status = '02' THEN 1 ELSE 0 END) as exception_projects,
            -- 本年度项目数
            SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_projects
        FROM pms_project_info
        WHERE is_delete = 0
22:33:36.635 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.budget_execution_rate
22:33:36.635 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN 1 ELSE 0 END), 0) as over_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN 1 ELSE 0 END), 0) as under_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount = budget THEN 1 ELSE 0 END), 0) as exact_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN winner_amount - budget ELSE 0 END), 0) as over_budget_amount,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN budget - winner_amount ELSE 0 END), 0) as under_budget_amount
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE()) AND winner_amount IS NOT NULL
22:33:36.652 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.government_procurement_stats
22:33:36.652 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            p_type,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            CASE
                WHEN p_type = '政府集中采购' THEN '政采'
                WHEN p_type IN ('部门集中采购', '分散采购') THEN '非政采'
                ELSE '其他'
            END as procurement_category
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY p_type, procurement_category
22:33:36.657 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:33:36.657 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:33:36.746 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:33:36.746 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:33:37.464 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:33:37.464 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:33:37.497 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_category_stats
22:33:37.497 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN purcharse_type = '货物' THEN '货物'
                WHEN purcharse_type = '服务' THEN '服务'
                WHEN purcharse_type IN ('工程', '工程_零星维修') THEN '工程'
                ELSE '其他'
            END as category,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            ROUND(AVG(budget), 2) as avg_budget
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY category
        ORDER BY total_budget DESC
22:33:37.498 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_content_stats
22:33:37.498 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN content IS NULL OR content = '' THEN '未填写'
                ELSE content
            END as content_name,
            COUNT(*) as project_count
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY content_name
        ORDER BY project_count DESC
22:33:37.499 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_total_amount
22:33:37.499 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            month_num,
            CASE month_num
                WHEN 1 THEN '1月'
                WHEN 2 THEN '2月'
                WHEN 3 THEN '3月'
                WHEN 4 THEN '4月'
                WHEN 5 THEN '5月'
                WHEN 6 THEN '6月'
                WHEN 7 THEN '7月'
                WHEN 8 THEN '8月'
                WHEN 9 THEN '9月'
                WHEN 10 THEN '10月'
                WHEN 11 THEN '11月'
                WHEN 12 THEN '12月'
            END as month_name,

            -- 今年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pi.budget ELSE 0 END), 0) as current_year_budget,
            -- 去年数据（预算）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pi.budget ELSE 0 END), 0) as last_year_budget,

            -- 今年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.total_price ELSE 0 END), 0) as current_year_goods_amount,
            -- 去年数据（货物总价）
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.total_price ELSE 0 END), 0) as last_year_goods_amount,

            -- 今年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as current_year_actual,
            -- 去年实际支出
            COALESCE(SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 AND pi.winner_amount IS NOT NULL THEN pi.winner_amount ELSE 0 END), 0) as last_year_actual,

            -- 今年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_project_count,
            -- 去年项目数量
            SUM(CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN 1 ELSE 0 END) as last_year_project_count,

            -- 今年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pg.pg_id ELSE NULL END) as current_year_goods_count,
            -- 去年货物数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pg.pg_id ELSE NULL END) as last_year_goods_count,

            -- 今年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) THEN pa.pa_id ELSE NULL END) as current_year_attachment_count,
            -- 去年附件数量
            COUNT(DISTINCT CASE WHEN YEAR(pi.apply_time) = YEAR(CURDATE()) - 1 THEN pa.pa_id ELSE NULL END) as last_year_attachment_count

        FROM (
            SELECT 1 as month_num UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION
            SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
        ) months
        LEFT JOIN pms_project_info pi ON MONTH(pi.apply_time) = months.month_num
            AND YEAR(pi.apply_time) IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
            AND pi.is_delete = 0
        LEFT JOIN pms_project_goods pg ON pi.AppliProCode = pg.AppliProCode
        LEFT JOIN pms_project_attachment pa ON pi.AppliProCode = pa.AppliProCode
        GROUP BY month_num, month_name
        ORDER BY month_num
22:33:37.501 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.government_procurement_stats
22:33:37.501 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            p_type,
            COUNT(*) as project_count,
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            CASE
                WHEN p_type = '政府集中采购' THEN '政采'
                WHEN p_type IN ('部门集中采购', '分散采购') THEN '非政采'
                ELSE '其他'
            END as procurement_category
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE())
        GROUP BY p_type, procurement_category
22:33:37.502 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.purchase_project_count
22:33:37.502 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            COUNT(*) as total_projects,
            SUM(CASE WHEN p_status = '00' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN p_status = '01' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN p_status = '02' THEN 1 ELSE 0 END) as exception_projects,
            -- 本年度项目数
            SUM(CASE WHEN YEAR(apply_time) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as current_year_projects
        FROM pms_project_info
        WHERE is_delete = 0
22:33:37.503 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - Purchase.budget_execution_rate
22:33:37.503 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            COALESCE(SUM(budget), 0) as total_budget,
            COALESCE(SUM(CASE WHEN winner_amount IS NOT NULL THEN winner_amount ELSE 0 END), 0) as total_actual,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN 1 ELSE 0 END), 0) as over_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN 1 ELSE 0 END), 0) as under_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount = budget THEN 1 ELSE 0 END), 0) as exact_budget_count,
            COALESCE(SUM(CASE WHEN winner_amount > budget THEN winner_amount - budget ELSE 0 END), 0) as over_budget_amount,
            COALESCE(SUM(CASE WHEN winner_amount < budget THEN budget - winner_amount ELSE 0 END), 0) as under_budget_amount
        FROM pms_project_info
        WHERE is_delete = 0 AND YEAR(apply_time) = YEAR(CURDATE()) AND winner_amount IS NOT NULL
22:33:37.511 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:33:37.511 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:33:37.554 [http-nio-19385-exec-9] INFO  c.s.f.a.AssetPriceRangeCountComponent - AssetPriceRangeCountComponent - operationType: summary, userDepartmentName: null
22:33:37.555 [http-nio-19385-exec-9] INFO  c.s.f.a.AssetPriceRangeCountComponent - 开始按资产单价分档统计数量 (汇总)...userDepartmentName: null
22:33:37.558 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.countAssetsByPriceRange
22:33:37.558 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN unit_price >= 1000 AND unit_price < 100000 THEN '档次1 (1000-10万元)'
                WHEN unit_price >= 100000 AND unit_price < 500000 THEN '档次2 (10万-50万元)'
                WHEN unit_price >= 500000 AND unit_price < 1000000 THEN '档次3 (50万-100万元)'
                ELSE '档次4 (≥100万元)'
            END AS price_range,
            COUNT(*) AS asset_count,
            COALESCE(SUM(amount), 0) AS asset_total_value
        FROM
            asset_Registration
        WHERE unit_price >= 1000 -- 仅统计大于等于1000元的资产
          AND (asset_category_name IS NULL OR asset_category_name != '无形资产')
        GROUP BY
            price_range
        ORDER BY
            CASE
                WHEN price_range = '档次1 (1000-10万元)' THEN 1
                WHEN price_range = '档次2 (10万-50万元)' THEN 2
                WHEN price_range = '档次3 (50万-100万元)' THEN 3
                ELSE 4
            END
22:33:38.038 [http-nio-19385-exec-9] INFO  c.s.f.a.AssetPriceRangeCountComponent - 按资产单价分档统计数量 (汇总) 完成，查询到 4 条档次数据
22:33:38.044 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetUsedYearsAnalysisComponent - 开始按已使用年限分析资产...userDepartmentName: null
22:33:38.049 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.analyzeUsedYears
22:33:38.049 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            CASE
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 4 THEN '4年以内'
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) >= 5 AND TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 8 THEN '5-8年'
                WHEN TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) >= 9 AND TIMESTAMPDIFF(YEAR, asset_entry_date, CURDATE()) <= 15 THEN '9-15年'
                ELSE '16年以上'
            END AS year_range,
            COUNT(*) AS asset_count
        FROM
            asset_Registration
        WHERE asset_entry_date IS NOT NULL
        GROUP BY
            year_range
        ORDER BY
            CASE
                WHEN year_range = '4年以内' THEN 1
                WHEN year_range = '5-8年' THEN 2
                WHEN year_range = '9-15年' THEN 3
                ELSE 4
            END
22:33:38.284 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetUsedYearsAnalysisComponent - 按已使用年限分析资产成功，处理了 4 个分段
22:33:38.289 [http-nio-19385-exec-1] INFO  c.s.f.a.AssetTotalSummaryComponent - 开始获取资产总额及当年新增...userDepartmentName: null
22:33:38.430 [http-nio-19385-exec-1] INFO  c.s.f.a.AssetTotalSummaryComponent - 获取资产总额及当年新增成功.
22:33:38.448 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAnnualAssetPurchaseAnalysis
22:33:38.448 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            entry_year AS year,
            COALESCE(SUM(ar.amount), 0) AS newAssetValue,
            (SELECT COALESCE(SUM(sub.amount), 0)
             FROM asset_Registration sub
             WHERE YEAR(sub.asset_entry_date) <= entry_year
             AND sub.asset_entry_date IS NOT NULL
            ) AS totalAssetValue
        FROM (
            SELECT DISTINCT YEAR(asset_entry_date) AS entry_year
            FROM asset_Registration
            WHERE asset_entry_date IS NOT NULL
            -- 默认查询近五年，如果有传参则使用传入的年份范围
                AND YEAR(asset_entry_date) >= (YEAR(CURDATE()) - 4)
                AND YEAR(asset_entry_date) <= YEAR(CURDATE())
        ) years
        LEFT JOIN asset_Registration ar ON YEAR(ar.asset_entry_date) = years.entry_year
        WHERE ar.asset_entry_date IS NOT NULL
        GROUP BY entry_year
        ORDER BY entry_year
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 接收到的原始参数 inParams: {operationType=summary}
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 接收到的 operationType: 'summary'
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 接收到的 userDepartmentCode: 'null', 长度: null
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 接收到的 userDepartmentName: 'null', 长度: null
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 未提供 userDepartmentCode 或 userDepartmentName，执行汇总查询时不按部门筛选。
22:33:39.169 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 开始获取部门使用情况汇总，参数: {dataId=asset.getDepartmentAssetUsageSummary}
22:33:39.173 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getDepartmentAssetUsageSummary
22:33:39.173 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.user_department_name AS department_name,
            ar.user_department_code AS department_code,
            COALESCE(SUM(ar.amount), 0) AS asset_total_value
        FROM
            asset_Registration ar
        WHERE 1=1
        GROUP BY
            ar.user_department_name, ar.user_department_code
        ORDER BY
            asset_total_value DESC
22:33:39.369 [http-nio-19385-exec-2] INFO  c.s.f.a.DepartmentAssetAnalysisComponent - 部门使用情况汇总查询完成，返回 57 条记录
22:33:39.380 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.sumTeachingAndResearchFundingValue
22:33:39.380 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            funding_subject_name,
            SUM(amount) AS total_value
        FROM
            asset_Registration
        WHERE
            funding_subject_name IS NOT NULL
            AND funding_subject_name != ''
        GROUP BY
            funding_subject_name
        ORDER BY
            total_value DESC
        LIMIT 6
22:33:39.552 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetStatisticsByCategoryName
22:33:39.552 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            category_name,
            category_code,
            COUNT(*) AS asset_count,
            COALESCE(SUM(quantity), 0) AS total_quantity,
            COALESCE(SUM(amount), 0) AS total_amount
        FROM
            asset_Registration
        WHERE
            category_name IS NOT NULL
            AND category_name != ''
            AND category_name <> ''
        GROUP BY
            category_name, category_code
        ORDER BY
            total_amount DESC, asset_count DESC
22:33:46.863 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
22:33:46.873 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
22:33:46.873 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:33:47.044 [http-nio-19385-exec-10] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
22:33:47.443 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:33:47.443 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
22:33:47.443 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:33:47.443 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:33:47.446 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
22:33:47.446 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
22:33:47.453 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
22:33:47.453 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
22:33:47.458 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
22:33:47.458 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
22:33:47.463 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=0, path=views\arrivalRegistration\assetBigScreen\index.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:29}, {menuId=49, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=0, path=views\DataCollection\DataCollection.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:52:58}, {menuId=50, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=0, path=views\ScienceBoard\ScienceBoard.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:23}, {menuId=52, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=0, path=views\Purchase\Purchase.vue, component=null, query=null, routeName=大屏, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=2025-06-26T12:53:39}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=null, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=56, parentId=35, parentName=一阶管控中心, menuName=采购管理, orderNum=1, path=views\invOutBill\purchase\purchase.vue, component=null, query=null, routeName=采购管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-22T09:13:38, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=28, parentId=25, parentName=基础信息维护, menuName=字典管理, orderNum=3, path=views/borrow/dictionaryManagement/index.vue, component=null, query=null, routeName=字典管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=55, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
22:33:47.513 [http-nio-19385-exec-5] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:34:07.455 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.455 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
22:34:07.455 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.455 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:34:07.457 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.457 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.457 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.457 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
22:34:07.457 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
22:34:07.457 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
22:34:07.457 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.457 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
22:34:07.457 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
22:34:07.457 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.457 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
22:34:07.457 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
22:34:07.457 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.457 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
22:34:07.463 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
22:34:07.463 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
22:34:07.463 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
22:34:07.463 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
22:34:07.464 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
22:34:07.464 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
22:34:07.464 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:34:07.464 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:34:07.466 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:34:07.466 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:34:07.470 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{ID=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=测试, threshold=1, sql=select * from fswp_2.das_role, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=123, threshold=1, sql=select * from indicator_level, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=3, warning_level=1, describe=12222, threshold=3, sql=select * from indicator_level, modifer=3220704014, update_time=2025-07-03T00:57:03, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}]
22:34:07.477 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
22:34:07.477 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.477 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:07.477 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
22:34:07.477 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
22:34:07.477 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.477 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:07.477 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
22:34:07.478 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
22:34:07.480 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
22:34:07.482 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
22:34:07.482 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
22:34:07.483 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
22:34:07.498 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
22:34:07.499 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
22:34:07.502 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=51.00, amount=173723.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=84}
22:34:07.502 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
22:34:07.502 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
22:34:07.514 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
22:34:07.514 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
22:34:07.514 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
22:34:07.517 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fanj, user_name=范俊, quantity=1032.00, amount=12734244.40, ranking=15}, {department_name=计算机学院, department_code=0006, user_code=tkf, user_name=唐康芬, quantity=996.00, amount=11591140.30, ranking=16}, {department_name=信息中心, department_code=1026, user_code=csy, user_name=陈树尧, quantity=866.00, amount=5670148.80, ranking=17}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=gjm, user_name=耿金明, quantity=852.00, amount=5195766.40, ranking=18}, {department_name=计算机学院, department_code=0006, user_code=lyjing, user_name=廖雨静, quantity=707.00, amount=11417492.26, ranking=19}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2022285, user_name=梁姝, quantity=700.00, amount=17040938.66, ranking=20}, {department_name=外国语学院, department_code=0015, user_code=evazy, user_name=赵月, quantity=662.00, amount=36685.73, ranking=21}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=guoqiang, user_name=郭强, quantity=654.00, amount=21869143.77, ranking=22}, {department_name=体育部, department_code=0019, user_code=fangyan, user_name=方妍, quantity=645.00, amount=22797271.55, ranking=23}, {department_name=管理学院, department_code=0011, user_code=crstalchen, user_name=陈莹, quantity=610.00, amount=3771138.00, ranking=24}], count=72}
22:34:07.522 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
22:34:07.522 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
22:34:07.530 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
22:34:07.530 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
22:34:07.567 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=资产登记, ongoing_count=174}, {business_type=资产报废, ongoing_count=229}, {business_type=低值耐用品报废报损, ongoing_count=61}, {business_type=低值耐用品登记, ongoing_count=55}, {business_type=低值耐用品院处间调拨, ongoing_count=22}, {business_type=低值耐用品院处内调拨, ongoing_count=102}, {business_type=院处间调拨, ongoing_count=75}, {business_type=院处内调拨, ongoing_count=443}, {business_type=资产减值, ongoing_count=3}, {business_type=资产增值, ongoing_count=3}], warnings=[{business_type=资产登记, total_warnings=165, warning_level_1=27, warning_level_2=138}, {business_type=低值耐用品登记, total_warnings=55, warning_level_1=6, warning_level_2=49}, {business_type=低值耐用品院处间调拨, total_warnings=22, warning_level_1=2, warning_level_2=20}, {business_type=低值耐用品院处内调拨, total_warnings=102, warning_level_1=3, warning_level_2=99}, {business_type=院处间调拨, total_warnings=75, warning_level_1=3, warning_level_2=72}, {business_type=院处内调拨, total_warnings=443, warning_level_1=46, warning_level_2=397}, {business_type=资产减值, total_warnings=3, warning_level_1=2, warning_level_2=1}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}], completedProcesses=[{business_type=资产登记, completed_count=61}, {business_type=低值耐用品登记, completed_count=15}, {business_type=资产增值, completed_count=8}, {business_type=资产报废, completed_count=13}, {business_type=资产减值, completed_count=3}], businessTypes=[{business_type=资产登记}, {business_type=资产报废}, {business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处间调拨}, {business_type=低值耐用品院处内调拨}, {business_type=院处间调拨}, {business_type=院处内调拨}, {business_type=资产减值}, {business_type=资产增值}], submittedProcesses=[{business_type=资产登记, submitted_count=174}, {business_type=资产报废, submitted_count=229}, {business_type=低值耐用品报废报损, submitted_count=61}, {business_type=低值耐用品登记, submitted_count=55}, {business_type=低值耐用品院处间调拨, submitted_count=22}, {business_type=低值耐用品院处内调拨, submitted_count=102}, {business_type=院处间调拨, submitted_count=75}, {business_type=院处内调拨, submitted_count=443}, {business_type=资产减值, submitted_count=3}, {business_type=资产增值, submitted_count=3}]}
22:34:07.609 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
22:34:07.610 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
22:34:07.614 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=196, healthIndicator=1238, warningLevel2=851, totalIndicator=2285, totalWarnings=1047, healthScore=0.5417943107221006}
22:34:10.252 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.252 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.252 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
22:34:10.252 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.252 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
22:34:10.252 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.252 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.252 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
22:34:10.252 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
22:34:10.252 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:34:10.252 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.252 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
22:34:10.252 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.252 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
22:34:10.252 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.252 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
22:34:10.260 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
22:34:10.260 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
22:34:10.261 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
22:34:10.261 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
22:34:10.261 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
22:34:10.261 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
22:34:10.263 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:34:10.263 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
22:34:10.263 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:34:10.263 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
22:34:10.264 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{ID=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=测试, threshold=1, sql=select * from fswp_2.das_role, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=123, threshold=1, sql=select * from indicator_level, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=3, warning_level=1, describe=12222, threshold=3, sql=select * from indicator_level, modifer=3220704014, update_time=2025-07-03T00:57:03, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}]
22:34:10.264 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:34:10.264 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:34:10.266 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.266 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.266 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
22:34:10.266 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
22:34:10.266 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.266 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.266 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
22:34:10.266 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
22:34:10.272 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
22:34:10.273 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
22:34:10.274 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
22:34:10.274 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
22:34:10.275 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
22:34:10.277 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:34:10.277 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:34:10.278 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
22:34:10.280 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
22:34:10.280 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
22:34:10.280 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
22:34:10.280 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=51.00, amount=173723.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=84}
22:34:10.282 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
22:34:10.282 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fanj, user_name=范俊, quantity=1032.00, amount=12734244.40, ranking=15}, {department_name=计算机学院, department_code=0006, user_code=tkf, user_name=唐康芬, quantity=996.00, amount=11591140.30, ranking=16}, {department_name=信息中心, department_code=1026, user_code=csy, user_name=陈树尧, quantity=866.00, amount=5670148.80, ranking=17}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=gjm, user_name=耿金明, quantity=852.00, amount=5195766.40, ranking=18}, {department_name=计算机学院, department_code=0006, user_code=lyjing, user_name=廖雨静, quantity=707.00, amount=11417492.26, ranking=19}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2022285, user_name=梁姝, quantity=700.00, amount=17040938.66, ranking=20}, {department_name=外国语学院, department_code=0015, user_code=evazy, user_name=赵月, quantity=662.00, amount=36685.73, ranking=21}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=guoqiang, user_name=郭强, quantity=654.00, amount=21869143.77, ranking=22}, {department_name=体育部, department_code=0019, user_code=fangyan, user_name=方妍, quantity=645.00, amount=22797271.55, ranking=23}, {department_name=管理学院, department_code=0011, user_code=crstalchen, user_name=陈莹, quantity=610.00, amount=3771138.00, ranking=24}], count=72}
22:34:10.285 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.285 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:34:10.285 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.285 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from fswp_2.das_role}]
22:34:10.289 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
22:34:10.290 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
22:34:10.296 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
22:34:10.296 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
22:34:10.303 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{id=0, code=root, name=IAM管理员, parent=null, type=null}, {id=1, code=admin, name=管理员, parent=null, type=1}, {id=2, code=general, name=普通用户, parent=null, type=1}], count=3, pageSize=5, page=1}
22:34:10.305 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
22:34:10.305 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
22:34:10.314 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=资产登记, ongoing_count=174}, {business_type=资产报废, ongoing_count=229}, {business_type=低值耐用品报废报损, ongoing_count=61}, {business_type=低值耐用品登记, ongoing_count=55}, {business_type=低值耐用品院处间调拨, ongoing_count=22}, {business_type=低值耐用品院处内调拨, ongoing_count=102}, {business_type=院处间调拨, ongoing_count=75}, {business_type=院处内调拨, ongoing_count=443}, {business_type=资产减值, ongoing_count=3}, {business_type=资产增值, ongoing_count=3}], warnings=[{business_type=资产登记, total_warnings=165, warning_level_1=27, warning_level_2=138}, {business_type=低值耐用品登记, total_warnings=55, warning_level_1=6, warning_level_2=49}, {business_type=低值耐用品院处间调拨, total_warnings=22, warning_level_1=2, warning_level_2=20}, {business_type=低值耐用品院处内调拨, total_warnings=102, warning_level_1=3, warning_level_2=99}, {business_type=院处间调拨, total_warnings=75, warning_level_1=3, warning_level_2=72}, {business_type=院处内调拨, total_warnings=443, warning_level_1=46, warning_level_2=397}, {business_type=资产减值, total_warnings=3, warning_level_1=2, warning_level_2=1}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}], completedProcesses=[{business_type=资产登记, completed_count=61}, {business_type=低值耐用品登记, completed_count=15}, {business_type=资产增值, completed_count=8}, {business_type=资产报废, completed_count=13}, {business_type=资产减值, completed_count=3}], businessTypes=[{business_type=资产登记}, {business_type=资产报废}, {business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处间调拨}, {business_type=低值耐用品院处内调拨}, {business_type=院处间调拨}, {business_type=院处内调拨}, {business_type=资产减值}, {business_type=资产增值}], submittedProcesses=[{business_type=资产登记, submitted_count=174}, {business_type=资产报废, submitted_count=229}, {business_type=低值耐用品报废报损, submitted_count=61}, {business_type=低值耐用品登记, submitted_count=55}, {business_type=低值耐用品院处间调拨, submitted_count=22}, {business_type=低值耐用品院处内调拨, submitted_count=102}, {business_type=院处间调拨, submitted_count=75}, {business_type=院处内调拨, submitted_count=443}, {business_type=资产减值, submitted_count=3}, {business_type=资产增值, submitted_count=3}]}
22:34:10.377 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
22:34:10.377 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
22:34:10.382 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=196, healthIndicator=1238, warningLevel2=851, totalIndicator=2285, totalWarnings=1047, healthScore=0.5417943107221006}
22:34:10.458 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.458 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:34:10.458 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.458 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from indicator_level}]
22:34:10.467 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{ID=21, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=2, describe=一阶告警14天未关闭, threshold=336, sql=null, modifer=, update_time=null}, {ID=316, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=1, describe=一阶告警超过7天未处理, threshold=168, sql=null, modifer=, update_time=null}, {ID=319, indicator_id=zc003, indicator_name=个人资产数量异常, supervisor_level=1, warning_level=1, describe=个人登记资产数量过多, threshold=50, sql=null, modifer=, update_time=null}, {ID=321, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=2, describe=当前流程停留时间超过60天, threshold=1440, sql=null, modifer=, update_time=null}, {ID=322, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=1, describe=审批时间停留30天, threshold=720, sql=null, modifer=, update_time=null}], count=10, pageSize=5, page=1}
22:34:10.499 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.499 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:34:10.499 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.499 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from indicator_level}]
22:34:10.504 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{ID=21, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=2, describe=一阶告警14天未关闭, threshold=336, sql=null, modifer=, update_time=null}, {ID=316, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=1, describe=一阶告警超过7天未处理, threshold=168, sql=null, modifer=, update_time=null}, {ID=319, indicator_id=zc003, indicator_name=个人资产数量异常, supervisor_level=1, warning_level=1, describe=个人登记资产数量过多, threshold=50, sql=null, modifer=, update_time=null}, {ID=321, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=2, describe=当前流程停留时间超过60天, threshold=1440, sql=null, modifer=, update_time=null}, {ID=322, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=1, describe=审批时间停留30天, threshold=720, sql=null, modifer=, update_time=null}], count=10, pageSize=5, page=1}
22:34:10.893 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:34:10.893 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
22:34:10.893 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:34:10.893 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
22:34:10.896 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
22:34:10.896 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
22:34:10.903 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
22:34:10.906 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
22:37:09.187 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:37:09.187 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:37:09.187 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:37:09.187 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:37:09.191 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryBusiness
22:37:09.191 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT businessName business, businessId buss_id
            FROM business_manage
            ORDER BY buss_id;
22:37:09.197 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:37:09.197 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:37:09.218 [http-nio-19385-exec-5] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共15条记录
22:37:09.218 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=15, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:37:13.188 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:37:13.188 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:37:13.188 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:37:13.188 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=10, processName=, subProcessName=, businessName=, pageSize=10}]
22:37:13.193 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:37:13.193 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 10
22:37:13.202 [http-nio-19385-exec-8] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共15条记录
22:37:13.204 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=15, offset=10, success=true, pageSize=10, list=[{indicator_id=zc001, indicator_name=当前审批时间超时, describe=12, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=风险描述, prevension_measure=, refer_regu_describe=制度依据描述, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc002, indicator_name=流程整体时间超时, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc003, indicator_name=个人资产数量异常, describe=1333, business=资产管理, process=资产登记, sub_process=null, monitor_obj=31313, risk_description=1333, prevension_measure=3131, refer_regu_describe=31313, editor=admin, edit_date=2025-05-22, valid_status=1, type=内置指标}, {indicator_id=zc004, indicator_name=资产使用单位异常, describe=ca, business=资产管理, process=资产使用, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc005, indicator_name=资产登记人员状态异常, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}]}
22:37:14.519 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:37:14.519 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:37:14.519 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:37:14.519 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:37:14.524 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:37:14.524 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:37:14.535 [http-nio-19385-exec-1] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共15条记录
22:37:14.536 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=15, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:37:27.057 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:37:27.057 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.FlowByBusinessNameComponent
22:37:27.057 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:37:27.057 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{businessName=资产管理}]
22:37:27.057 [http-nio-19385-exec-3] INFO  c.s.f.R.FlowByBusinessNameComponent - 开始按业务名称查询流程信息...
22:37:27.061 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryFlowsByBusinessName
22:37:27.061 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
           ID as id,
           businessId,
           businessName,
           flows
       FROM
           business_manage
       WHERE
           1=1
               AND businessName like CONCAT('%', :businessName, '%')
       ORDER BY orders ASC
22:37:27.065 [http-nio-19385-exec-3] INFO  c.s.f.R.FlowByBusinessNameComponent - 按业务名称'资产管理'查询流程成功，共查询到3条流程记录
22:37:27.065 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {success=true, list=[{children=[], businessName=资产管理, businessId=1744275420661, flowId=1744276538964, flowName=资产登记, order=1}, {children=null, businessName=资产管理, businessId=1744275420661, flowId=1744968311451, flowName=资产使用, order=2}, {children=null, businessName=资产管理, businessId=1744275420661, flowId=1744968320223, flowName=资产报废, order=3}]}
22:37:30.729 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.querySubFlowsByBusinessAndFlow
22:37:30.729 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
           bm.businessId,
           bm.businessName,
           JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.flowId')) as flowId,
           JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.name')) as flowName,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.flowId')) as subFlowId,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.name')) as subFlowName,
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.order')) as subFlowOrder
       FROM
           business_manage bm,
           JSON_TABLE(
               bm.flows,
               '$[*]' COLUMNS (
                   flow JSON PATH '$'
               )
           ) AS f,
           JSON_TABLE(
               JSON_EXTRACT(f.flow, '$.children'),
               '$[*]' COLUMNS (
                   subflow JSON PATH '$'
               )
           ) AS sf
       WHERE
           bm.businessName = :businessName
           AND JSON_UNQUOTE(JSON_EXTRACT(f.flow, '$.name')) = :flowName
           AND JSON_LENGTH(JSON_EXTRACT(f.flow, '$.children')) > 0
       ORDER BY
           JSON_UNQUOTE(JSON_EXTRACT(sf.subflow, '$.order'))
22:37:57.775 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
      INSERT INTO knowledge_indicators (
          indicator_id,
          indicator_name,
          `describe`,
          business,
          process,
          sub_process,
          monitor_obj,
          risk_description,
          prevension_measure,
          refer_regu_describe,
          editor,
          type,
          edit_date,
          valid_status
      ) VALUES (
          :indicatorId,
          :indicatorName,
          :describe,
          :business,
          :process,
          :subProcess,
          :monitorObj,
          :riskDescription,
          :prevensionMeasure,
          :referReguDescribe,
          :editor,
          :type,
          NOW(),
          1
      )
  
22:37:57.908 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:37:57.908 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:37:57.908 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:37:57.908 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:37:57.913 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:37:57.913 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:37:57.922 [http-nio-19385-exec-4] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:37:57.922 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:38:00.136 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:38:00.136 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:38:00.136 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:38:00.136 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryKnowledgeIndicatorsComponent
22:38:00.136 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryIndicatorLevelComponent
22:38:00.136 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:38:00.136 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.GetWarningLevelsComponent
22:38:00.136 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:38:00.136 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:38:00.136 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:38:00.136 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:38:00.136 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:38:00.136 [http-nio-19385-exec-6] INFO  c.s.f.I.QueryIndicatorLevelComponent - QueryIndicatorLevelComponent run
22:38:00.140 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryWarningLevels
22:38:00.140 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            dict_label,
            dict_value
        FROM
            sys_dict_data
        WHERE
            dict_type = 'warning_level'
            AND status = '0'
        ORDER BY dict_sort ASC;
22:38:00.141 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryKnowledgeIndicatorsForDropdown
22:38:00.141 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name
        FROM
            knowledge_indicators
        WHERE
            valid_status = 1
        ORDER BY indicator_name ASC;
22:38:00.142 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryIndicatorLevels
22:38:00.142 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type,
            su.userName,
            il.modifer,
            il.update_time
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        LEFT JOIN sys_user as su on su.nickName = il.modifer
        WHERE
            1=1
        ORDER BY il.indicator_id ASC, il.warning_level ASC, il.supervisor_level ASC;
22:38:00.145 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警}, {indicator_id=zc003, indicator_name=个人资产数量异常}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改}, {indicator_id=zc001, indicator_name=当前审批时间超时}, {indicator_id=zc002, indicator_name=流程整体时间超时}, {indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款}, {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率}, {indicator_id=zc004, indicator_name=资产使用单位异常}, {indicator_id=zc005, indicator_name=资产登记人员状态异常}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低}]
22:38:00.148 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{dict_label=黄色预警, dict_value=1}, {dict_label=红色预警, dict_value=2}]
22:38:00.155 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{id=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=测试, sql=select * from fswp_2.das_role, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=123, sql=select * from indicator_level, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=12222, sql=select * from indicator_level, supervisor_level=3, threshold=3, type=自定义指标, userName=马国栋, modifer=3220704014, update_time=2025-07-03T00:57:03}, {id=322, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=审批时间停留30天, sql=null, supervisor_level=1, threshold=720, type=内置指标, userName=null, modifer=, update_time=null}, {id=874478732, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=boabgo , sql=null, supervisor_level=1, threshold=1, type=内置指标, userName=杜明山, modifer=3220704017, update_time=2025-07-02T03:27:54}, {id=316, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=321, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=当前流程停留时间超过60天, sql=null, supervisor_level=1, threshold=1440, type=内置指标, userName=null, modifer=, update_time=null}, {id=21, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=一阶告警14天未关闭, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}, {id=319, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=个人登记资产数量过多, sql=null, supervisor_level=1, threshold=50, type=内置指标, userName=null, modifer=, update_time=null}, {id=324, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=323, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=个人资产数量超过100, sql=null, supervisor_level=1, threshold=100, type=内置指标, userName=null, modifer=, update_time=null}, {id=325, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=一阶告警超过14天未处理, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}]
22:38:04.756 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:38:04.756 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryIndicatorTypeComponent
22:38:04.757 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:38:04.757 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicator_id=RK25073022371580076040998FAZ8Y7, responseType=json, componentName=QueryIndicatorType}]
22:38:04.757 [http-nio-19385-exec-5] INFO  c.s.f.I.QueryIndicatorTypeComponent - 开始查询指标ID: RK25073022371580076040998FAZ8Y7 的类型信息
22:38:04.762 [http-nio-19385-exec-5] INFO  c.s.f.I.QueryIndicatorTypeComponent - 成功查询到指标ID: RK25073022371580076040998FAZ8Y7 的类型信息，类型为: 内置指标
22:38:04.763 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, type=内置指标}
22:39:02.975 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:39:02.975 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.SaveIndicatorLevelComponent
22:39:02.975 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:39:02.975 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=1, rule_description=判断资产使用率黄色异常, threshold=40, supervisor_level=1, responseType=json, componentName=SaveIndicatorLevel}]
22:39:02.976 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - SaveIndicatorLevelComponent 接收到的参数: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=1, rule_description=判断资产使用率黄色异常, threshold=40, supervisor_level=1, responseType=json, componentName=SaveIndicatorLevel}
22:39:02.976 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 指标ID: RK25073022371580076040998FAZ8Y7
22:39:02.976 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 查询指标类型的参数: {dataId=IndicatorLevel.queryIndicatorTypeById, indicator_id=RK25073022371580076040998FAZ8Y7}
22:39:02.982 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 查询到的指标信息: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, type=内置指标}
22:39:02.982 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 指标类型: 内置指标
22:39:02.983 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 生成的数字ID: 342982474, 时间戳后缀: 342982, 随机数: 474
22:39:02.983 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 内置指标，sql字段设置为null
22:39:02.983 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 准备保存的数据: {dataId=IndicatorLevel.insertIndicatorLevel, data={warning_level=1, indicator_name=设备使用率, threshold=40, updateTime=2025-07-30 22:39:02, ID=342982474, describe=判断资产使用率黄色异常, indicator_id=RK25073022371580076040998FAZ8Y7, supervisor_level=1, modifer=**********, sql=null}, table=indicator_level}
22:39:02.987 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO indicator_level (
            ID,
            indicator_id,
            indicator_name,
            warning_level,
            `describe`,
            `sql`,
            supervisor_level,
            threshold,
            modifer,
            update_time
        ) VALUES (
            :ID,
            :indicator_id,
            :indicator_name,
            :warning_level,
            :describe,
            :sql,
            :supervisor_level,
            :threshold,
            :modifer,
            :updateTime
        );
    
22:39:02.997 [http-nio-19385-exec-8] INFO  c.s.f.I.SaveIndicatorLevelComponent - 保存操作结果: 1
22:39:02.997 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {result=success}
22:39:03.012 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:39:03.012 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryIndicatorLevelComponent
22:39:03.012 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:39:03.012 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:39:03.012 [http-nio-19385-exec-1] INFO  c.s.f.I.QueryIndicatorLevelComponent - QueryIndicatorLevelComponent run
22:39:03.016 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryIndicatorLevels
22:39:03.016 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type,
            su.userName,
            il.modifer,
            il.update_time
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        LEFT JOIN sys_user as su on su.nickName = il.modifer
        WHERE
            1=1
        ORDER BY il.indicator_id ASC, il.warning_level ASC, il.supervisor_level ASC;
22:39:03.022 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{id=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=测试, sql=select * from fswp_2.das_role, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=123, sql=select * from indicator_level, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=12222, sql=select * from indicator_level, supervisor_level=3, threshold=3, type=自定义指标, userName=马国栋, modifer=3220704014, update_time=2025-07-03T00:57:03}, {id=342982474, indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=1, rule_description=判断资产使用率黄色异常, sql=null, supervisor_level=1, threshold=40, type=内置指标, userName=陈奕孝, modifer=**********, update_time=2025-07-30T22:39:03}, {id=322, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=审批时间停留30天, sql=null, supervisor_level=1, threshold=720, type=内置指标, userName=null, modifer=, update_time=null}, {id=874478732, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=boabgo , sql=null, supervisor_level=1, threshold=1, type=内置指标, userName=杜明山, modifer=3220704017, update_time=2025-07-02T03:27:54}, {id=316, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=321, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=当前流程停留时间超过60天, sql=null, supervisor_level=1, threshold=1440, type=内置指标, userName=null, modifer=, update_time=null}, {id=21, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=一阶告警14天未关闭, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}, {id=319, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=个人登记资产数量过多, sql=null, supervisor_level=1, threshold=50, type=内置指标, userName=null, modifer=, update_time=null}, {id=324, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=323, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=个人资产数量超过100, sql=null, supervisor_level=1, threshold=100, type=内置指标, userName=null, modifer=, update_time=null}, {id=325, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=一阶告警超过14天未处理, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}]
22:39:07.932 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:39:08.024 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:39:08.024 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryIndicatorTypeComponent
22:39:08.024 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:39:08.024 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicator_id=RK25073022371580076040998FAZ8Y7, responseType=json, componentName=QueryIndicatorType}]
22:39:08.024 [http-nio-19385-exec-10] INFO  c.s.f.I.QueryIndicatorTypeComponent - 开始查询指标ID: RK25073022371580076040998FAZ8Y7 的类型信息
22:39:08.031 [http-nio-19385-exec-10] INFO  c.s.f.I.QueryIndicatorTypeComponent - 成功查询到指标ID: RK25073022371580076040998FAZ8Y7 的类型信息，类型为: 内置指标
22:39:08.031 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, type=内置指标}
22:39:08.462 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:39:10.116 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - ip：***********
22:39:23.731 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:39:23.731 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.SaveIndicatorLevelComponent
22:39:23.731 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:39:23.731 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=2, rule_description=资产使用率红色异常, threshold=60, supervisor_level=2, responseType=json, componentName=SaveIndicatorLevel}]
22:39:23.731 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - SaveIndicatorLevelComponent 接收到的参数: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=2, rule_description=资产使用率红色异常, threshold=60, supervisor_level=2, responseType=json, componentName=SaveIndicatorLevel}
22:39:23.731 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 指标ID: RK25073022371580076040998FAZ8Y7
22:39:23.731 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 查询指标类型的参数: {dataId=IndicatorLevel.queryIndicatorTypeById, indicator_id=RK25073022371580076040998FAZ8Y7}
22:39:23.737 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 查询到的指标信息: {indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, type=内置指标}
22:39:23.737 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 指标类型: 内置指标
22:39:23.737 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 生成的数字ID: 363737926, 时间戳后缀: 363737, 随机数: 926
22:39:23.737 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 内置指标，sql字段设置为null
22:39:23.738 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 准备保存的数据: {dataId=IndicatorLevel.insertIndicatorLevel, data={warning_level=2, indicator_name=设备使用率, threshold=60, updateTime=2025-07-30 22:39:23, ID=363737926, describe=资产使用率红色异常, indicator_id=RK25073022371580076040998FAZ8Y7, supervisor_level=2, modifer=**********, sql=null}, table=indicator_level}
22:39:23.741 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO indicator_level (
            ID,
            indicator_id,
            indicator_name,
            warning_level,
            `describe`,
            `sql`,
            supervisor_level,
            threshold,
            modifer,
            update_time
        ) VALUES (
            :ID,
            :indicator_id,
            :indicator_name,
            :warning_level,
            :describe,
            :sql,
            :supervisor_level,
            :threshold,
            :modifer,
            :updateTime
        );
    
22:39:23.752 [http-nio-19385-exec-7] INFO  c.s.f.I.SaveIndicatorLevelComponent - 保存操作结果: 1
22:39:23.752 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {result=success}
22:39:23.763 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:39:23.763 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.QueryIndicatorLevelComponent
22:39:23.763 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:39:23.763 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:39:23.763 [http-nio-19385-exec-2] INFO  c.s.f.I.QueryIndicatorLevelComponent - QueryIndicatorLevelComponent run
22:39:23.767 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryIndicatorLevels
22:39:23.767 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.`describe` as rule_description,
            il.`sql`,
            il.supervisor_level,
            il.threshold,
            ki.type,
            su.userName,
            il.modifer,
            il.update_time
        FROM
            indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        LEFT JOIN sys_user as su on su.nickName = il.modifer
        WHERE
            1=1
        ORDER BY il.indicator_id ASC, il.warning_level ASC, il.supervisor_level ASC;
22:39:23.773 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{id=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=测试, sql=select * from fswp_2.das_role, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=123, sql=select * from indicator_level, supervisor_level=1, threshold=1, type=自定义指标, userName=null, modifer=, update_time=null}, {id=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, warning_level=1, rule_description=12222, sql=select * from indicator_level, supervisor_level=3, threshold=3, type=自定义指标, userName=马国栋, modifer=3220704014, update_time=2025-07-03T00:57:03}, {id=342982474, indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=1, rule_description=判断资产使用率黄色异常, sql=null, supervisor_level=1, threshold=40, type=内置指标, userName=陈奕孝, modifer=**********, update_time=2025-07-30T22:39:03}, {id=363737926, indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, warning_level=2, rule_description=资产使用率红色异常, sql=null, supervisor_level=2, threshold=60, type=内置指标, userName=陈奕孝, modifer=**********, update_time=2025-07-30T22:39:24}, {id=322, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=审批时间停留30天, sql=null, supervisor_level=1, threshold=720, type=内置指标, userName=null, modifer=, update_time=null}, {id=874478732, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=boabgo , sql=null, supervisor_level=1, threshold=1, type=内置指标, userName=杜明山, modifer=3220704017, update_time=2025-07-02T03:27:54}, {id=316, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=321, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=当前流程停留时间超过60天, sql=null, supervisor_level=1, threshold=1440, type=内置指标, userName=null, modifer=, update_time=null}, {id=21, indicator_id=zc001, indicator_name=当前审批时间超时, warning_level=2, rule_description=一阶告警14天未关闭, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}, {id=319, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=个人登记资产数量过多, sql=null, supervisor_level=1, threshold=50, type=内置指标, userName=null, modifer=, update_time=null}, {id=324, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=1, rule_description=一阶告警超过7天未处理, sql=null, supervisor_level=2, threshold=168, type=内置指标, userName=null, modifer=, update_time=null}, {id=323, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=个人资产数量超过100, sql=null, supervisor_level=1, threshold=100, type=内置指标, userName=null, modifer=, update_time=null}, {id=325, indicator_id=zc003, indicator_name=个人资产数量异常, warning_level=2, rule_description=一阶告警超过14天未处理, sql=null, supervisor_level=2, threshold=336, type=内置指标, userName=null, modifer=, update_time=null}]
22:44:24.069 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.069 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.069 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.161 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
22:44:24.162 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
22:44:24.069 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.162 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
22:44:24.162 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.162 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.162 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
22:44:24.162 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
22:44:24.162 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.162 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
22:44:24.162 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
22:44:24.162 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.162 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
22:44:24.340 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
22:44:24.340 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
22:44:24.341 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
22:44:24.341 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
22:44:24.341 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
22:44:24.341 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
22:44:24.343 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:44:24.343 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:44:24.343 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
22:44:24.344 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
22:44:24.344 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
22:44:24.344 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
22:44:24.347 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: [{ID=54047071, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=测试, threshold=1, sql=select * from fswp_2.das_role, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=501240971, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=1, warning_level=1, describe=123, threshold=1, sql=select * from indicator_level, modifer=, update_time=null, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {ID=572986087, indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, supervisor_level=3, warning_level=1, describe=12222, threshold=3, sql=select * from indicator_level, modifer=3220704014, update_time=2025-07-03T00:57:03, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}]
22:44:24.348 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.348 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
22:44:24.348 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.348 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
22:44:24.348 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.349 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
22:44:24.349 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.349 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
22:44:24.380 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
22:44:24.380 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
22:44:24.381 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
22:44:24.381 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
22:44:24.383 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
22:44:24.383 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
22:44:24.389 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
22:44:24.390 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.390 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:44:24.390 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.390 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from fswp_2.das_role}]
22:44:24.393 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fanj, user_name=范俊, quantity=1032.00, amount=12734244.40, ranking=15}, {department_name=计算机学院, department_code=0006, user_code=tkf, user_name=唐康芬, quantity=996.00, amount=11591140.30, ranking=16}, {department_name=信息中心, department_code=1026, user_code=csy, user_name=陈树尧, quantity=866.00, amount=5670148.80, ranking=17}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=gjm, user_name=耿金明, quantity=852.00, amount=5195766.40, ranking=18}, {department_name=计算机学院, department_code=0006, user_code=lyjing, user_name=廖雨静, quantity=707.00, amount=11417492.26, ranking=19}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2022285, user_name=梁姝, quantity=700.00, amount=17040938.66, ranking=20}, {department_name=外国语学院, department_code=0015, user_code=evazy, user_name=赵月, quantity=662.00, amount=36685.73, ranking=21}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=guoqiang, user_name=郭强, quantity=654.00, amount=21869143.77, ranking=22}, {department_name=体育部, department_code=0019, user_code=fangyan, user_name=方妍, quantity=645.00, amount=22797271.55, ranking=23}, {department_name=管理学院, department_code=0011, user_code=crstalchen, user_name=陈莹, quantity=610.00, amount=3771138.00, ranking=24}], count=72}
22:44:24.393 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
22:44:24.395 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
22:44:24.397 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{id=0, code=root, name=IAM管理员, parent=null, type=null}, {id=1, code=admin, name=管理员, parent=null, type=1}, {id=2, code=general, name=普通用户, parent=null, type=1}], count=3, pageSize=5, page=1}
22:44:24.403 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
22:44:24.403 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
22:44:24.410 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
22:44:24.410 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
22:44:24.416 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
22:44:24.416 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
22:44:24.425 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=资产登记, ongoing_count=174}, {business_type=资产报废, ongoing_count=229}, {business_type=低值耐用品报废报损, ongoing_count=61}, {business_type=低值耐用品登记, ongoing_count=55}, {business_type=低值耐用品院处间调拨, ongoing_count=22}, {business_type=低值耐用品院处内调拨, ongoing_count=102}, {business_type=院处间调拨, ongoing_count=75}, {business_type=院处内调拨, ongoing_count=443}, {business_type=资产减值, ongoing_count=3}, {business_type=资产增值, ongoing_count=3}], warnings=[{business_type=资产登记, total_warnings=165, warning_level_1=27, warning_level_2=138}, {business_type=低值耐用品登记, total_warnings=55, warning_level_1=6, warning_level_2=49}, {business_type=低值耐用品院处间调拨, total_warnings=22, warning_level_1=2, warning_level_2=20}, {business_type=低值耐用品院处内调拨, total_warnings=102, warning_level_1=3, warning_level_2=99}, {business_type=院处间调拨, total_warnings=75, warning_level_1=3, warning_level_2=72}, {business_type=院处内调拨, total_warnings=443, warning_level_1=46, warning_level_2=397}, {business_type=资产减值, total_warnings=3, warning_level_1=2, warning_level_2=1}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}], completedProcesses=[{business_type=资产登记, completed_count=61}, {business_type=低值耐用品登记, completed_count=15}, {business_type=资产增值, completed_count=8}, {business_type=资产报废, completed_count=13}, {business_type=资产减值, completed_count=3}], businessTypes=[{business_type=资产登记}, {business_type=资产报废}, {business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处间调拨}, {business_type=低值耐用品院处内调拨}, {business_type=院处间调拨}, {business_type=院处内调拨}, {business_type=资产减值}, {business_type=资产增值}], submittedProcesses=[{business_type=资产登记, submitted_count=174}, {business_type=资产报废, submitted_count=229}, {business_type=低值耐用品报废报损, submitted_count=61}, {business_type=低值耐用品登记, submitted_count=55}, {business_type=低值耐用品院处间调拨, submitted_count=22}, {business_type=低值耐用品院处内调拨, submitted_count=102}, {business_type=院处间调拨, submitted_count=75}, {business_type=院处内调拨, submitted_count=443}, {business_type=资产减值, submitted_count=3}, {business_type=资产增值, submitted_count=3}]}
22:44:24.430 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.430 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:44:24.430 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.430 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from indicator_level}]
22:44:24.436 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{ID=21, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=2, describe=一阶告警14天未关闭, threshold=336, sql=null, modifer=, update_time=null}, {ID=316, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=1, describe=一阶告警超过7天未处理, threshold=168, sql=null, modifer=, update_time=null}, {ID=319, indicator_id=zc003, indicator_name=个人资产数量异常, supervisor_level=1, warning_level=1, describe=个人登记资产数量过多, threshold=50, sql=null, modifer=, update_time=null}, {ID=321, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=2, describe=当前流程停留时间超过60天, threshold=1440, sql=null, modifer=, update_time=null}, {ID=322, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=1, describe=审批时间停留30天, threshold=720, sql=null, modifer=, update_time=null}], count=10, pageSize=5, page=1}
22:44:24.462 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.462 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteIndicatorContentComponent
22:44:24.462 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.462 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=5, page=1, sql=select * from indicator_level}]
22:44:24.468 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
22:44:24.468 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
22:44:24.468 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {msg=success, code=1, data=[{ID=21, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=2, describe=一阶告警14天未关闭, threshold=336, sql=null, modifer=, update_time=null}, {ID=316, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=2, warning_level=1, describe=一阶告警超过7天未处理, threshold=168, sql=null, modifer=, update_time=null}, {ID=319, indicator_id=zc003, indicator_name=个人资产数量异常, supervisor_level=1, warning_level=1, describe=个人登记资产数量过多, threshold=50, sql=null, modifer=, update_time=null}, {ID=321, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=2, describe=当前流程停留时间超过60天, threshold=1440, sql=null, modifer=, update_time=null}, {ID=322, indicator_id=zc001, indicator_name=当前审批时间超时, supervisor_level=1, warning_level=1, describe=审批时间停留30天, threshold=720, sql=null, modifer=, update_time=null}], count=10, pageSize=5, page=1}
22:44:24.472 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=196, healthIndicator=1238, warningLevel2=851, totalIndicator=2285, totalWarnings=1047, healthScore=0.5417943107221006}
22:44:24.568 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
22:44:24.573 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=51.00, amount=173723.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=84}
22:44:24.757 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:24.757 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
22:44:24.757 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:24.757 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
22:44:24.761 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
22:44:24.761 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
22:44:24.769 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
22:44:24.771 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
22:44:24.849 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
22:44:24.853 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
22:44:53.795 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:53.795 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:44:53.795 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:53.795 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:44:53.912 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryBusiness
22:44:53.912 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT businessName business, businessId buss_id
            FROM business_manage
            ORDER BY buss_id;
22:44:53.913 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:44:53.913 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:44:53.921 [http-nio-19385-exec-8] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:44:53.921 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:44:56.248 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:44:56.249 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:44:56.249 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:44:56.249 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=10, processName=, subProcessName=, businessName=, pageSize=10}]
22:44:56.253 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:44:56.253 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 10
22:44:56.261 [http-nio-19385-exec-4] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:44:56.261 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=10, success=true, pageSize=10, list=[{indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, describe=设备使用率, business=资产管理, process=资产使用, sub_process=null, monitor_obj=资产, risk_description=设备使用率, prevension_measure=null, refer_regu_describe=null, editor=admin, edit_date=2025-07-30, valid_status=1, type=内置指标}, {indicator_id=zc001, indicator_name=当前审批时间超时, describe=12, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=风险描述, prevension_measure=, refer_regu_describe=制度依据描述, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc002, indicator_name=流程整体时间超时, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc003, indicator_name=个人资产数量异常, describe=1333, business=资产管理, process=资产登记, sub_process=null, monitor_obj=31313, risk_description=1333, prevension_measure=3131, refer_regu_describe=31313, editor=admin, edit_date=2025-05-22, valid_status=1, type=内置指标}, {indicator_id=zc004, indicator_name=资产使用单位异常, describe=ca, business=资产管理, process=资产使用, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc005, indicator_name=资产登记人员状态异常, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}]}
22:45:00.261 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:45:00.261 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:45:00.261 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:45:00.261 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:45:00.265 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:45:00.265 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:45:00.277 [http-nio-19385-exec-5] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:45:00.277 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:45:02.560 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:45:02.560 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:45:02.560 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:45:02.560 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=10, processName=, subProcessName=, businessName=, pageSize=10}]
22:45:02.563 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:45:02.564 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 10
22:45:02.576 [http-nio-19385-exec-7] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:45:02.576 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=10, success=true, pageSize=10, list=[{indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, describe=设备使用率, business=资产管理, process=资产使用, sub_process=null, monitor_obj=资产, risk_description=设备使用率, prevension_measure=null, refer_regu_describe=null, editor=admin, edit_date=2025-07-30, valid_status=1, type=内置指标}, {indicator_id=zc001, indicator_name=当前审批时间超时, describe=12, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=风险描述, prevension_measure=, refer_regu_describe=制度依据描述, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc002, indicator_name=流程整体时间超时, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc003, indicator_name=个人资产数量异常, describe=1333, business=资产管理, process=资产登记, sub_process=null, monitor_obj=31313, risk_description=1333, prevension_measure=3131, refer_regu_describe=31313, editor=admin, edit_date=2025-05-22, valid_status=1, type=内置指标}, {indicator_id=zc004, indicator_name=资产使用单位异常, describe=ca, business=资产管理, process=资产使用, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc005, indicator_name=资产登记人员状态异常, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}]}
22:45:33.183 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:45:33.183 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:45:33.183 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:45:33.183 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=0, processName=, subProcessName=, businessName=, pageSize=10}]
22:45:33.188 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:45:33.188 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 0
22:45:33.197 [http-nio-19385-exec-2] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:45:33.197 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=0, success=true, pageSize=10, list=[{indicator_id=RK250603105137578606509MPD4419D, indicator_name=测试内置指标, describe=123, business=资产管理, process=资产使用, sub_process=null, monitor_obj=123, risk_description=123, prevension_measure=123, refer_regu_describe=123, editor=admin, edit_date=2025-06-03, valid_status=1, type=内置指标}, {indicator_id=RK250603105159535450716VOH3XKRT, indicator_name=测试自定义指标, describe=43, business=资产管理, process=资产登记, sub_process=null, monitor_obj=123, risk_description=43, prevension_measure=34, refer_regu_describe=null, editor=admin, edit_date=2025-06-03, valid_status=1, type=自定义指标}, {indicator_id=RK2507221654374863489278R01E4GY, indicator_name=采购申请超配置标准, describe=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=使用预算资金采购办公设备及家具单价或数量超限，超标配置申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=1.《四川省省级行政事业单位通用办公设备和家具配置标准》 2.《中共中央国务院关于印发党政机关厉行节约反对浪费条例的通知》(中发〔2013〕13号)第十二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165508372401639BX29YB11, indicator_name=项目应论证未论证, describe=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要论证金额，但是否校内论证结果为“否”，申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=四川省省级行政事业单位国有资产管理办法》第二十四条："重大资产购置项目必须经过可行性论证，未履行论证程序不得立项"具体执行标准为：各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165545478683656SM5CE035, indicator_name=“三重一大”事项规避集体决策预警, describe=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=项目预算金额达到需要提供集体决策会议纪要金额，会议纪要为“空”， 申请审核结果为“通过”, prevension_measure=null, refer_regu_describe=各学校及单位的内控制度, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165618179935707DR86HDVO, indicator_name=疑似规避政采/校内统采的拆分采购, describe=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, business=采购业务, process=采购申请, sub_process=null, monitor_obj=申请部门, risk_description=同一申请人/部门/项目在30天内提交≥2笔采购申请，使用资金来源相同；, prevension_measure=null, refer_regu_describe=1.《政府采购法》第四十一条 2.《成都信息工程大学采购管理办法》第七条", editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165656403813423L0SVSY6Y, indicator_name=疑似设置排他性条款, describe=技术参数含品牌限定词 详细需求, business=采购业务, process=采购决策, sub_process=null, monitor_obj=需求编制, risk_description=技术参数含品牌限定词 详细需求, prevension_measure=null, refer_regu_describe=《政府采购货物和服务招标投标管理办法》第二十条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165721566179842J251306D, indicator_name=合同条款篡改, describe=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , business=采购业务, process=合同签订于备案, sub_process=null, monitor_obj=签订合同人, risk_description=关键条款（付款/违约/参数）与招标文件、投标文件中的实质性要求发生变化 , prevension_measure=null, refer_regu_describe=《成都信息工程大学合同管理办法》, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165757788306945ZYZLGHLT, indicator_name=验收标准降低, describe=验收结论="合格" BUT 问题项未整改完成, business=采购业务, process=采购执行, sub_process=null, monitor_obj=履约验收, risk_description=验收结论="合格" BUT 问题项未整改完成, prevension_measure=null, refer_regu_describe=《财政部关于加强政府采购履约验收管理的指导意见》第二条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}, {indicator_id=RK250722165843864118800F8VCO125, indicator_name=违规退还质保金, describe=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", business=采购业务, process=采购项目支付, sub_process=null, monitor_obj=支付双方, risk_description=退还申请日期 < 质保期截止日 OR 履约确认书状态≠"已签署", prevension_measure=null, refer_regu_describe=《行政事业单位内部控制规范》第四十七条, editor=admin, edit_date=2025-07-22, valid_status=1, type=内置指标}]}
22:45:34.074 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
22:45:34.074 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.RiskLibrary.RiskIndicatorQueryComponent
22:45:34.074 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
22:45:34.075 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{indicatorName=, offset=10, processName=, subProcessName=, businessName=, pageSize=10}]
22:45:34.078 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - RiskLibrary.queryKnowledgeIndicators
22:45:34.078 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            indicator_id,
            indicator_name,
            `describe`,
            business,
            process,
            sub_process,
            monitor_obj,
            risk_description,
            prevension_measure,
            refer_regu_describe,
            editor,
            edit_date,
            valid_status,
            type
        FROM
            knowledge_indicators
        WHERE
            1=1
        ORDER BY indicator_id
        LIMIT 10 OFFSET 10
22:45:34.086 [http-nio-19385-exec-6] INFO  c.s.f.R.RiskIndicatorQueryComponent - 风险指标查询成功，共16条记录
22:45:34.086 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {total=16, offset=10, success=true, pageSize=10, list=[{indicator_id=RK25073022371580076040998FAZ8Y7, indicator_name=设备使用率, describe=设备使用率, business=资产管理, process=资产使用, sub_process=null, monitor_obj=资产, risk_description=设备使用率, prevension_measure=null, refer_regu_describe=null, editor=admin, edit_date=2025-07-30, valid_status=1, type=内置指标}, {indicator_id=zc001, indicator_name=当前审批时间超时, describe=12, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=风险描述, prevension_measure=, refer_regu_describe=制度依据描述, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc002, indicator_name=流程整体时间超时, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc003, indicator_name=个人资产数量异常, describe=1333, business=资产管理, process=资产登记, sub_process=null, monitor_obj=31313, risk_description=1333, prevension_measure=3131, refer_regu_describe=31313, editor=admin, edit_date=2025-05-22, valid_status=1, type=内置指标}, {indicator_id=zc004, indicator_name=资产使用单位异常, describe=ca, business=资产管理, process=资产使用, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}, {indicator_id=zc005, indicator_name=资产登记人员状态异常, describe=123, business=资产管理, process=, sub_process=, monitor_obj=, risk_description=, prevension_measure=, refer_regu_describe=, editor=admin, edit_date=2025-04-14, valid_status=1, type=内置指标}]}
22:47:13.902 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
22:47:13.906 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
22:47:17.002 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 5020 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
22:47:17.003 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
22:47:22.350 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
22:47:22.889 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
22:47:22.897 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
22:47:22.898 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
22:47:22.898 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
22:47:22.929 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
22:47:22.929 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5903 ms
22:47:23.093 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
22:47:23.115 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:47:23.117 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
22:47:23.117 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
22:47:23.117 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
22:47:23.119 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:47:23.119 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
22:47:23.119 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
22:47:23.119 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
22:47:23.377 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
22:47:23.377 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
22:47:25.652 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
22:47:25.986 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
22:47:26.776 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
22:47:26.849 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
22:47:26.860 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
22:47:26.874 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 10.485 seconds (process running for 10.862)
22:47:26.880 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:10s
22:47:27.177 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:47:27.178 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
22:47:27.179 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
22:47:27.210 [RMI TCP Connection(4)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
22:48:04.835 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
22:48:04.838 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
22:48:06.800 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 34948 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
22:48:06.801 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
22:48:07.894 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
22:48:08.427 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
22:48:08.434 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
22:48:08.436 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
22:48:08.436 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
22:48:08.461 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
22:48:08.461 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1636 ms
22:48:08.633 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
22:48:08.653 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:48:08.655 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
22:48:08.655 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
22:48:08.655 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
22:48:08.657 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
22:48:08.657 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
22:48:08.657 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
22:48:08.657 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
22:48:08.783 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
22:48:08.783 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
22:48:10.546 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
22:48:10.880 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
22:48:11.673 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
22:48:11.752 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
22:48:11.763 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
22:48:11.777 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.504 seconds (process running for 5.888)
22:48:11.782 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
22:48:12.151 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:48:12.151 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
22:48:12.152 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
22:48:12.181 [RMI TCP Connection(5)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
