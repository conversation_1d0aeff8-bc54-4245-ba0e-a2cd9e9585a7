12:24:14.306 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 19460 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
12:24:14.307 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
12:24:19.013 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c9ef898-82bb-3532-80de-4164d374481c
12:24:19.663 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
12:24:19.675 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
12:24:19.676 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
12:24:19.676 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
12:24:19.711 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
12:24:19.711 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5380 ms
12:24:19.929 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
12:24:19.956 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
12:24:20.113 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
12:24:20.113 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
12:24:22.830 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
12:24:23.182 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
12:24:23.968 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
12:24:24.046 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
12:24:24.058 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
12:24:24.074 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 10.507 seconds (process running for 11.933)
12:24:24.081 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:10s
12:24:24.270 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:24:24.270 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
12:24:24.271 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
12:24:24.321 [RMI TCP Connection(4)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
