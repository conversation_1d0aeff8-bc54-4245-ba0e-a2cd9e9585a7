12:24:14.306 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 19460 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
12:24:14.307 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
12:24:19.013 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c9ef898-82bb-3532-80de-4164d374481c
12:24:19.663 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
12:24:19.675 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
12:24:19.676 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
12:24:19.676 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
12:24:19.711 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
12:24:19.711 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5380 ms
12:24:19.929 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
12:24:19.956 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
12:24:19.959 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
12:24:19.961 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
12:24:20.113 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
12:24:20.113 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
12:24:22.830 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
12:24:23.182 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
12:24:23.968 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
12:24:24.046 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
12:24:24.058 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
12:24:24.074 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 10.507 seconds (process running for 11.933)
12:24:24.081 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:10s
12:24:24.270 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:24:24.270 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
12:24:24.271 [RMI TCP Connection(5)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
12:24:24.321 [RMI TCP Connection(4)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
12:29:02.316 [http-nio-19385-exec-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
12:29:02.382 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
12:29:02.398 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
12:29:02.398 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:29:02.636 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:29:03.037 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:03.037 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
12:29:03.037 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:03.037 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:29:03.046 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
12:29:03.046 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
12:29:03.057 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
12:29:03.057 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
12:29:03.069 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
12:29:03.069 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
12:29:03.078 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=44, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=1, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=46, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=3, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=47, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=4, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=2025-06-18T08:29:43, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-18T08:29:56, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=48, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
12:29:03.102 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:29:20.958 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:20.958 [http-nio-19385-exec-2] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 开始查询所有达到预警阈值的资产信息，参数: {lookDept=全校, pageSize=10, page=1}
12:29:20.958 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:29:20.958 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:20.958 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:20.958 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:20.958 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
12:29:20.958 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:29:20.958 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:20.958 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:29:20.958 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
12:29:20.958 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:20.958 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:29:20.966 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:29:20.966 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:29:20.968 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
12:29:20.968 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
12:29:20.969 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetsExceedUsageThreshold
12:29:20.969 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
            AND aur.statistics_year = YEAR(CURDATE())
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
            LIMIT :dm_limit OFFSET :dm_offset
12:29:20.969 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:29:20.969 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:29:20.970 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:29:20.970 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:29:20.971 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:29:20.971 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:29:21.007 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
12:29:21.028 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:29:21.033 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:21.034 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
12:29:21.034 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:21.034 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
12:29:21.037 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
12:29:21.037 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
12:29:21.040 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:21.040 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
12:29:21.040 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:21.040 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
12:29:21.044 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, state=已离职/已退休, quantity=11889.00, amount=31366345.50}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=52.00, amount=179393.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=85}
12:29:21.045 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
12:29:21.047 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:29:21.051 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:21.051 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
12:29:21.051 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:21.051 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:29:21.055 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
12:29:21.055 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
12:29:21.058 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
12:29:21.058 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
12:29:21.058 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
12:29:21.058 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
12:29:21.060 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
12:29:21.063 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
12:29:21.063 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
12:29:21.064 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: []
12:29:21.071 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
12:29:21.072 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
12:29:21.072 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
12:29:21.076 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=图书馆, department_code=1027, user_code=cuitcy, user_name=陈越, quantity=39336.00, amount=9295791.87, ranking=1}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, quantity=11889.00, amount=31366345.50, ranking=2}, {department_name=工程实践中心（创新创业学院）, department_code=0018, user_code=zhsh, user_name=张双, quantity=3822.00, amount=15692375.50, ranking=3}, {department_name=电子工程学院（大气探测学院）, department_code=0003, user_code=liyangji, user_name=李扬继, quantity=3232.00, amount=56920202.87, ranking=4}, {department_name=信息中心, department_code=1026, user_code=yaoyuan, user_name=姚远, quantity=2480.00, amount=13706647.60, ranking=5}, {department_name=自动化学院, department_code=0004, user_code=yingdong, user_name=应东, quantity=2111.00, amount=22713848.41, ranking=6}, {department_name=马克思主义学院, department_code=0016, user_code=zhouyan, user_name=周艳, quantity=1809.00, amount=991695.76, ranking=7}, {department_name=信息中心, department_code=1026, user_code=wangb, user_name=王兵, quantity=1770.00, amount=21284959.15, ranking=8}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=exvi, user_name=高妮娜, quantity=1736.00, amount=19486657.39, ranking=9}, {department_name=软件工程学院, department_code=0007, user_code=huangjian, user_name=黄健, quantity=1449.00, amount=11693423.18, ranking=10}], count=86}
12:29:21.078 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
12:29:21.078 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
12:29:21.090 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=低值耐用品报废报损, ongoing_count=66}, {business_type=低值耐用品登记, ongoing_count=52}, {business_type=低值耐用品院处内调拨, ongoing_count=101}, {business_type=低值耐用品院处间调拨, ongoing_count=21}, {business_type=资产减值, ongoing_count=1}, {business_type=资产增值, ongoing_count=3}, {business_type=资产报废, ongoing_count=240}, {business_type=资产登记, ongoing_count=147}, {business_type=院处内调拨, ongoing_count=436}, {business_type=院处间调拨, ongoing_count=76}], warnings=[{business_type=院处内调拨, total_warnings=410, warning_level_1=14, warning_level_2=396}, {business_type=院处间调拨, total_warnings=74, warning_level_1=1, warning_level_2=73}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}, {business_type=资产减值, total_warnings=1, warning_level_1=0, warning_level_2=1}, {business_type=低值耐用品院处内调拨, total_warnings=97, warning_level_1=0, warning_level_2=97}, {business_type=低值耐用品院处间调拨, total_warnings=20, warning_level_1=0, warning_level_2=20}, {business_type=低值耐用品登记, total_warnings=48, warning_level_1=0, warning_level_2=48}, {business_type=资产登记, total_warnings=142, warning_level_1=1, warning_level_2=141}], completedProcesses=[{business_type=资产登记, completed_count=78}, {business_type=低值耐用品登记, completed_count=13}, {business_type=资产增值, completed_count=7}, {business_type=资产减值, completed_count=2}], businessTypes=[{business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处内调拨}, {business_type=低值耐用品院处间调拨}, {business_type=资产减值}, {business_type=资产增值}, {business_type=资产报废}, {business_type=资产登记}, {business_type=院处内调拨}, {business_type=院处间调拨}], submittedProcesses=[{business_type=院处内调拨, submitted_count=436}, {business_type=资产报废, submitted_count=240}, {business_type=院处间调拨, submitted_count=76}, {business_type=资产增值, submitted_count=3}, {business_type=资产减值, submitted_count=1}, {business_type=低值耐用品院处间调拨, submitted_count=21}, {business_type=低值耐用品院处内调拨, submitted_count=101}, {business_type=低值耐用品报废报损, submitted_count=66}, {business_type=低值耐用品登记, submitted_count=52}, {business_type=资产登记, submitted_count=147}]}
12:29:21.103 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:29:21.103 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:29:21.107 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:29:21.139 [http-nio-19385-exec-2] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 成功查询到2条达到预警阈值的资产记录
12:29:21.571 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:21.571 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:29:21.571 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:21.571 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:29:21.574 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:29:21.574 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:29:21.579 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:29:21.581 [http-nio-19385-exec-5] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:29:26.774 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
12:29:26.786 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
12:29:26.787 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:29:27.204 [http-nio-19385-exec-1] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:29:27.345 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:27.345 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
12:29:27.345 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:27.345 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:29:27.351 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
12:29:27.351 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
12:29:27.356 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
12:29:27.356 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
12:29:27.361 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
12:29:27.361 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
12:29:27.364 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=44, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=1, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=46, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=3, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=47, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=4, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=2025-06-18T08:29:43, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-18T08:29:56, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=48, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
12:29:27.966 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:29:33.113 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.113 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:29:33.113 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.113 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:29:33.114 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.114 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
12:29:33.114 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.114 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
12:29:33.114 [http-nio-19385-exec-5] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 开始查询所有达到预警阈值的资产信息，参数: {lookDept=全校, pageSize=10, page=1}
12:29:33.114 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.114 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:29:33.114 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.114 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:29:33.119 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:29:33.119 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:29:33.119 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
12:29:33.119 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
12:29:33.121 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetsExceedUsageThreshold
12:29:33.121 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
            AND aur.statistics_year = YEAR(CURDATE())
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
            LIMIT :dm_limit OFFSET :dm_offset
12:29:33.121 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:29:33.121 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:29:33.122 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:29:33.122 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:29:33.122 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:29:33.123 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:29:33.123 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.123 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
12:29:33.123 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.123 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
12:29:33.128 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:29:33.128 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
12:29:33.128 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
12:29:33.129 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
12:29:33.129 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.129 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
12:29:33.129 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.129 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
12:29:33.130 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:29:33.133 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
12:29:33.133 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
12:29:33.133 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, state=已离职/已退休, quantity=11889.00, amount=31366345.50}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=52.00, amount=179393.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=85}
12:29:33.134 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.135 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
12:29:33.135 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.135 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:29:33.135 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
12:29:33.139 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
12:29:33.139 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
12:29:33.140 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=图书馆, department_code=1027, user_code=cuitcy, user_name=陈越, quantity=39336.00, amount=9295791.87, ranking=1}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, quantity=11889.00, amount=31366345.50, ranking=2}, {department_name=工程实践中心（创新创业学院）, department_code=0018, user_code=zhsh, user_name=张双, quantity=3822.00, amount=15692375.50, ranking=3}, {department_name=电子工程学院（大气探测学院）, department_code=0003, user_code=liyangji, user_name=李扬继, quantity=3232.00, amount=56920202.87, ranking=4}, {department_name=信息中心, department_code=1026, user_code=yaoyuan, user_name=姚远, quantity=2480.00, amount=13706647.60, ranking=5}, {department_name=自动化学院, department_code=0004, user_code=yingdong, user_name=应东, quantity=2111.00, amount=22713848.41, ranking=6}, {department_name=马克思主义学院, department_code=0016, user_code=zhouyan, user_name=周艳, quantity=1809.00, amount=991695.76, ranking=7}, {department_name=信息中心, department_code=1026, user_code=wangb, user_name=王兵, quantity=1770.00, amount=21284959.15, ranking=8}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=exvi, user_name=高妮娜, quantity=1736.00, amount=19486657.39, ranking=9}, {department_name=软件工程学院, department_code=0007, user_code=huangjian, user_name=黄健, quantity=1449.00, amount=11693423.18, ranking=10}], count=86}
12:29:33.141 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: []
12:29:33.141 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
12:29:33.141 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
12:29:33.141 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
12:29:33.141 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
12:29:33.148 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
12:29:33.149 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
12:29:33.154 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
12:29:33.155 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
12:29:33.160 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
12:29:33.160 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
12:29:33.175 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=低值耐用品报废报损, ongoing_count=66}, {business_type=低值耐用品登记, ongoing_count=52}, {business_type=低值耐用品院处内调拨, ongoing_count=101}, {business_type=低值耐用品院处间调拨, ongoing_count=21}, {business_type=资产减值, ongoing_count=1}, {business_type=资产增值, ongoing_count=3}, {business_type=资产报废, ongoing_count=240}, {business_type=资产登记, ongoing_count=147}, {business_type=院处内调拨, ongoing_count=436}, {business_type=院处间调拨, ongoing_count=76}], warnings=[{business_type=院处内调拨, total_warnings=410, warning_level_1=14, warning_level_2=396}, {business_type=院处间调拨, total_warnings=74, warning_level_1=1, warning_level_2=73}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}, {business_type=资产减值, total_warnings=1, warning_level_1=0, warning_level_2=1}, {business_type=低值耐用品院处内调拨, total_warnings=97, warning_level_1=0, warning_level_2=97}, {business_type=低值耐用品院处间调拨, total_warnings=20, warning_level_1=0, warning_level_2=20}, {business_type=低值耐用品登记, total_warnings=48, warning_level_1=0, warning_level_2=48}, {business_type=资产登记, total_warnings=142, warning_level_1=1, warning_level_2=141}], completedProcesses=[{business_type=资产登记, completed_count=78}, {business_type=低值耐用品登记, completed_count=13}, {business_type=资产增值, completed_count=7}, {business_type=资产减值, completed_count=2}], businessTypes=[{business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处内调拨}, {business_type=低值耐用品院处间调拨}, {business_type=资产减值}, {business_type=资产增值}, {business_type=资产报废}, {business_type=资产登记}, {business_type=院处内调拨}, {business_type=院处间调拨}], submittedProcesses=[{business_type=院处内调拨, submitted_count=436}, {business_type=资产报废, submitted_count=240}, {business_type=院处间调拨, submitted_count=76}, {business_type=资产增值, submitted_count=3}, {business_type=资产减值, submitted_count=1}, {business_type=低值耐用品院处间调拨, submitted_count=21}, {business_type=低值耐用品院处内调拨, submitted_count=101}, {business_type=低值耐用品报废报损, submitted_count=66}, {business_type=低值耐用品登记, submitted_count=52}, {business_type=资产登记, submitted_count=147}]}
12:29:33.265 [http-nio-19385-exec-5] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 成功查询到2条达到预警阈值的资产记录
12:29:33.276 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:29:33.276 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:29:33.281 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:29:33.710 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:29:33.710 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:29:33.710 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:29:33.710 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:29:33.717 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:29:33.717 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:29:33.726 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:29:33.730 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:30:02.180 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
12:30:02.183 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
12:30:05.415 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 11428 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
12:30:05.416 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
12:30:06.476 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c9ef898-82bb-3532-80de-4164d374481c
12:30:07.015 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
12:30:07.023 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
12:30:07.024 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
12:30:07.024 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
12:30:07.049 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
12:30:07.049 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1611 ms
12:30:07.223 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
12:30:07.242 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:30:07.244 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
12:30:07.244 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
12:30:07.244 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
12:30:07.245 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:30:07.246 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
12:30:07.246 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
12:30:07.246 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
12:30:07.375 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
12:30:07.375 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
12:30:09.050 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
12:30:09.381 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
12:30:10.143 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
12:30:10.212 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
12:30:10.222 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
12:30:10.236 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.268 seconds (process running for 5.605)
12:30:10.243 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
12:30:10.783 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:30:10.783 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
12:30:10.784 [RMI TCP Connection(4)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
12:30:10.819 [RMI TCP Connection(2)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
12:30:19.783 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
12:30:19.784 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:30:19.784 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:30:19.784 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:30:19.810 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:30:19.810 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:30:19.817 [http-nio-19385-exec-3] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
12:30:19.996 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:30:19.996 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:30:20.001 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:30:38.360 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
12:30:38.374 [http-nio-19385-exec-1] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
12:30:38.374 [http-nio-19385-exec-1] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:30:38.605 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:30:38.998 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:30:38.998 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
12:30:38.998 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:30:38.998 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:30:39.004 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
12:30:39.004 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
12:30:39.012 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
12:30:39.012 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
12:30:39.024 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
12:30:39.024 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
12:30:39.030 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=44, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=1, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=46, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=3, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=47, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=4, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=2025-06-18T08:29:43, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-18T08:29:56, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=48, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
12:30:39.075 [http-nio-19385-exec-5] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:32:25.408 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.408 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:32:25.408 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.408 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:32:25.409 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.409 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.409 [http-nio-19385-exec-4] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 开始查询所有达到预警阈值的资产信息，参数: {lookDept=全校, pageSize=10, page=1}
12:32:25.409 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:32:25.409 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
12:32:25.410 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.410 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.410 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:32:25.410 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
12:32:25.416 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:32:25.416 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:32:25.418 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:32:25.418 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:32:25.421 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetsExceedUsageThreshold
12:32:25.421 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.indicator_id,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
            AND aur.statistics_year = YEAR(CURDATE())
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
            LIMIT :dm_limit OFFSET :dm_offset
12:32:25.421 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:32:25.421 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:32:25.423 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:32:25.423 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:32:25.426 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
12:32:25.426 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
12:32:25.433 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.433 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
12:32:25.433 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.433 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
12:32:25.441 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
12:32:25.455 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
12:32:25.498 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:32:25.499 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.499 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
12:32:25.499 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.499 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
12:32:25.503 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
12:32:25.503 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
12:32:25.508 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
12:32:25.512 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:32:25.513 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
12:32:25.517 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, state=已离职/已退休, quantity=11889.00, amount=31366345.50}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=52.00, amount=179393.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=85}
12:32:25.519 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=图书馆, department_code=1027, user_code=cuitcy, user_name=陈越, quantity=39336.00, amount=9295791.87, ranking=1}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, quantity=11889.00, amount=31366345.50, ranking=2}, {department_name=工程实践中心（创新创业学院）, department_code=0018, user_code=zhsh, user_name=张双, quantity=3822.00, amount=15692375.50, ranking=3}, {department_name=电子工程学院（大气探测学院）, department_code=0003, user_code=liyangji, user_name=李扬继, quantity=3232.00, amount=56920202.87, ranking=4}, {department_name=信息中心, department_code=1026, user_code=yaoyuan, user_name=姚远, quantity=2480.00, amount=13706647.60, ranking=5}, {department_name=自动化学院, department_code=0004, user_code=yingdong, user_name=应东, quantity=2111.00, amount=22713848.41, ranking=6}, {department_name=马克思主义学院, department_code=0016, user_code=zhouyan, user_name=周艳, quantity=1809.00, amount=991695.76, ranking=7}, {department_name=信息中心, department_code=1026, user_code=wangb, user_name=王兵, quantity=1770.00, amount=21284959.15, ranking=8}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=exvi, user_name=高妮娜, quantity=1736.00, amount=19486657.39, ranking=9}, {department_name=软件工程学院, department_code=0007, user_code=huangjian, user_name=黄健, quantity=1449.00, amount=11693423.18, ranking=10}], count=86}
12:32:25.522 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
12:32:25.522 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
12:32:25.523 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:25.523 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
12:32:25.524 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:25.524 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:32:25.529 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
12:32:25.529 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
12:32:25.532 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
12:32:25.532 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
12:32:25.532 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: []
12:32:25.535 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
12:32:25.535 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
12:32:25.545 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
12:32:25.545 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
12:32:25.553 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
12:32:25.553 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
12:32:25.567 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=低值耐用品报废报损, ongoing_count=66}, {business_type=低值耐用品登记, ongoing_count=52}, {business_type=低值耐用品院处内调拨, ongoing_count=101}, {business_type=低值耐用品院处间调拨, ongoing_count=21}, {business_type=资产减值, ongoing_count=1}, {business_type=资产增值, ongoing_count=3}, {business_type=资产报废, ongoing_count=240}, {business_type=资产登记, ongoing_count=147}, {business_type=院处内调拨, ongoing_count=436}, {business_type=院处间调拨, ongoing_count=76}], warnings=[{business_type=院处内调拨, total_warnings=410, warning_level_1=14, warning_level_2=396}, {business_type=院处间调拨, total_warnings=74, warning_level_1=1, warning_level_2=73}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}, {business_type=资产减值, total_warnings=1, warning_level_1=0, warning_level_2=1}, {business_type=低值耐用品院处内调拨, total_warnings=97, warning_level_1=0, warning_level_2=97}, {business_type=低值耐用品院处间调拨, total_warnings=20, warning_level_1=0, warning_level_2=20}, {business_type=低值耐用品登记, total_warnings=48, warning_level_1=0, warning_level_2=48}, {business_type=资产登记, total_warnings=142, warning_level_1=1, warning_level_2=141}], completedProcesses=[{business_type=资产登记, completed_count=78}, {business_type=低值耐用品登记, completed_count=13}, {business_type=资产增值, completed_count=7}, {business_type=资产减值, completed_count=2}], businessTypes=[{business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处内调拨}, {business_type=低值耐用品院处间调拨}, {business_type=资产减值}, {business_type=资产增值}, {business_type=资产报废}, {business_type=资产登记}, {business_type=院处内调拨}, {business_type=院处间调拨}], submittedProcesses=[{business_type=院处内调拨, submitted_count=436}, {business_type=资产报废, submitted_count=240}, {business_type=院处间调拨, submitted_count=76}, {business_type=资产增值, submitted_count=3}, {business_type=资产减值, submitted_count=1}, {business_type=低值耐用品院处间调拨, submitted_count=21}, {business_type=低值耐用品院处内调拨, submitted_count=101}, {business_type=低值耐用品报废报损, submitted_count=66}, {business_type=低值耐用品登记, submitted_count=52}, {business_type=资产登记, submitted_count=147}]}
12:32:25.575 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:32:25.575 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:32:25.580 [http-nio-19385-exec-3] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:32:25.595 [http-nio-19385-exec-4] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 成功查询到2条达到预警阈值的资产记录
12:32:26.043 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:26.043 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:32:26.043 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:26.043 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:32:26.048 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:32:26.048 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:32:26.058 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:32:26.063 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:32:32.080 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.080 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:32:32.080 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.080 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:32:32.080 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.080 [http-nio-19385-exec-5] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 开始查询所有达到预警阈值的资产信息，参数: {lookDept=全校, pageSize=10, page=1}
12:32:32.080 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
12:32:32.081 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.081 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.081 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
12:32:32.081 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:32:32.081 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.081 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:32:32.088 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:32:32.088 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:32:32.091 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetsExceedUsageThreshold
12:32:32.091 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
12:32:32.091 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.indicator_id,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
            AND aur.statistics_year = YEAR(CURDATE())
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
            LIMIT :dm_limit OFFSET :dm_offset
12:32:32.091 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
12:32:32.092 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:32:32.092 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:32:32.093 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:32:32.093 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:32:32.093 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.093 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
12:32:32.093 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.093 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
12:32:32.095 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:32:32.095 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:32:32.102 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
12:32:32.102 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
12:32:32.102 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.102 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
12:32:32.102 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.102 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
12:32:32.102 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:32:32.103 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
12:32:32.106 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
12:32:32.106 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:32:32.106 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
12:32:32.109 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, state=已离职/已退休, quantity=11889.00, amount=31366345.50}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=52.00, amount=179393.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=85}
12:32:32.110 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
12:32:32.111 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.111 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
12:32:32.111 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.111 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:32:32.115 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
12:32:32.115 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
12:32:32.115 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=图书馆, department_code=1027, user_code=cuitcy, user_name=陈越, quantity=39336.00, amount=9295791.87, ranking=1}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, quantity=11889.00, amount=31366345.50, ranking=2}, {department_name=工程实践中心（创新创业学院）, department_code=0018, user_code=zhsh, user_name=张双, quantity=3822.00, amount=15692375.50, ranking=3}, {department_name=电子工程学院（大气探测学院）, department_code=0003, user_code=liyangji, user_name=李扬继, quantity=3232.00, amount=56920202.87, ranking=4}, {department_name=信息中心, department_code=1026, user_code=yaoyuan, user_name=姚远, quantity=2480.00, amount=13706647.60, ranking=5}, {department_name=自动化学院, department_code=0004, user_code=yingdong, user_name=应东, quantity=2111.00, amount=22713848.41, ranking=6}, {department_name=马克思主义学院, department_code=0016, user_code=zhouyan, user_name=周艳, quantity=1809.00, amount=991695.76, ranking=7}, {department_name=信息中心, department_code=1026, user_code=wangb, user_name=王兵, quantity=1770.00, amount=21284959.15, ranking=8}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=exvi, user_name=高妮娜, quantity=1736.00, amount=19486657.39, ranking=9}, {department_name=软件工程学院, department_code=0007, user_code=huangjian, user_name=黄健, quantity=1449.00, amount=11693423.18, ranking=10}], count=86}
12:32:32.116 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
12:32:32.117 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
12:32:32.119 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
12:32:32.119 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: []
12:32:32.119 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
12:32:32.128 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
12:32:32.128 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
12:32:32.136 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
12:32:32.136 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
12:32:32.145 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
12:32:32.145 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
12:32:32.161 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=低值耐用品报废报损, ongoing_count=66}, {business_type=低值耐用品登记, ongoing_count=52}, {business_type=低值耐用品院处内调拨, ongoing_count=101}, {business_type=低值耐用品院处间调拨, ongoing_count=21}, {business_type=资产减值, ongoing_count=1}, {business_type=资产增值, ongoing_count=3}, {business_type=资产报废, ongoing_count=240}, {business_type=资产登记, ongoing_count=147}, {business_type=院处内调拨, ongoing_count=436}, {business_type=院处间调拨, ongoing_count=76}], warnings=[{business_type=院处内调拨, total_warnings=410, warning_level_1=14, warning_level_2=396}, {business_type=院处间调拨, total_warnings=74, warning_level_1=1, warning_level_2=73}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}, {business_type=资产减值, total_warnings=1, warning_level_1=0, warning_level_2=1}, {business_type=低值耐用品院处内调拨, total_warnings=97, warning_level_1=0, warning_level_2=97}, {business_type=低值耐用品院处间调拨, total_warnings=20, warning_level_1=0, warning_level_2=20}, {business_type=低值耐用品登记, total_warnings=48, warning_level_1=0, warning_level_2=48}, {business_type=资产登记, total_warnings=142, warning_level_1=1, warning_level_2=141}], completedProcesses=[{business_type=资产登记, completed_count=78}, {business_type=低值耐用品登记, completed_count=13}, {business_type=资产增值, completed_count=7}, {business_type=资产减值, completed_count=2}], businessTypes=[{business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处内调拨}, {business_type=低值耐用品院处间调拨}, {business_type=资产减值}, {business_type=资产增值}, {business_type=资产报废}, {business_type=资产登记}, {business_type=院处内调拨}, {business_type=院处间调拨}], submittedProcesses=[{business_type=院处内调拨, submitted_count=436}, {business_type=资产报废, submitted_count=240}, {business_type=院处间调拨, submitted_count=76}, {business_type=资产增值, submitted_count=3}, {business_type=资产减值, submitted_count=1}, {business_type=低值耐用品院处间调拨, submitted_count=21}, {business_type=低值耐用品院处内调拨, submitted_count=101}, {business_type=低值耐用品报废报损, submitted_count=66}, {business_type=低值耐用品登记, submitted_count=52}, {business_type=资产登记, submitted_count=147}]}
12:32:32.220 [http-nio-19385-exec-5] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 成功查询到2条达到预警阈值的资产记录
12:32:32.225 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:32:32.225 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:32:32.230 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:32:32.703 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:32:32.703 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:32:32.703 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:32:32.703 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:32:32.707 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:32:32.707 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:32:32.716 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:32:32.719 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:37:29.689 [http-nio-19385-exec-7] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:37:30.697 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:37:32.692 [http-nio-19385-exec-2] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:42:28.880 [http-nio-19385-exec-10] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:42:30.040 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:42:31.973 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:44:20.947 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
12:44:20.950 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
12:44:24.863 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 32692 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
12:44:24.864 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
12:44:25.981 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=6295829c-44b4-3d9b-ae99-4b764e3cd1ae
12:44:26.512 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
12:44:26.519 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
12:44:26.520 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
12:44:26.520 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
12:44:26.545 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
12:44:26.545 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1660 ms
12:44:26.717 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
12:44:26.737 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:44:26.737 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
12:44:26.737 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
12:44:26.739 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
12:44:26.740 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
12:44:26.741 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
12:44:26.741 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
12:44:26.741 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
12:44:26.866 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
12:44:26.866 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
12:44:28.728 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
12:44:29.081 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
12:44:29.863 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
12:44:29.939 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
12:44:29.949 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
12:44:29.962 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.557 seconds (process running for 5.89)
12:44:29.967 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
12:44:30.165 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:44:30.165 [RMI TCP Connection(2)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
12:44:30.167 [RMI TCP Connection(2)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
12:44:30.196 [RMI TCP Connection(5)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
12:44:42.143 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
12:44:42.144 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:44:42.144 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:42.144 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:44:42.169 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:44:42.169 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:44:42.174 [http-nio-19385-exec-2] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
12:44:42.354 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:44:42.354 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:44:42.358 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:44:52.146 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
12:44:52.161 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: **********
12:44:52.161 [http-nio-19385-exec-4] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:44:52.395 [http-nio-19385-exec-5] INFO  c.s.fswp.Controller.LoginController - 访问者:**********
12:44:52.786 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:52.786 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SysRoleClass.SelectMenuByUserComponent
12:44:52.786 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:52.786 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:44:52.790 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectRoleByUser
12:44:52.790 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
         roleId
         from
         sys_role_user
         where 1=1
              and userId=:userId
12:44:52.796 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysRole.selectMenuByRoles
12:44:52.796 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT menuId
            FROM  sys_role_menu
            WHERE roleId in (:roleIds)
12:44:52.803 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysMenu.selectMenuInList
12:44:52.803 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select * from  sys_menu  where visible =1 and menuId in (:menuIds)
12:44:52.807 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=2, data=[{menuId=41, parentId=0, parentName=顶级菜单, menuName=监管驾驶舱, orderNum=0, path=dashboard-selector, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Cpu, updateTime=null, children=[{menuId=44, parentId=41, parentName=监管驾驶舱, menuName=资产监管大屏, orderNum=1, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=45, parentId=41, parentName=监管驾驶舱, menuName=采购监管大屏, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=46, parentId=41, parentName=监管驾驶舱, menuName=数据采集大屏, orderNum=3, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=47, parentId=41, parentName=监管驾驶舱, menuName=科研看板大屏, orderNum=4, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=40, parentId=0, parentName=顶级菜单, menuName=二阶管控中心, orderNum=1, path=views/SecondControlCenter/SecondControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Grid, updateTime=null}, {menuId=35, parentId=0, parentName=顶级菜单, menuName=一阶管控中心, orderNum=2, path=null, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Reading, updateTime=2025-06-18T08:29:43, children=[{menuId=36, parentId=35, parentName=一阶管控中心, menuName=资产管理, orderNum=0, path=views/invOutBill/outMaterial/outMaterial.vue, component=null, query=null, routeName=资产管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=39, parentId=35, parentName=一阶管控中心, menuName=二级部门业务管控中心, orderNum=4, path=views/invOutBill/SecondDepartmentControlCenter/SecondDepartmentControlCenter.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=29, parentId=0, parentName=顶级菜单, menuName=监管知识库, orderNum=3, path=materialManagement, component=null, query=null, routeName=监管知识库, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Notebook, updateTime=2025-06-18T08:29:56, children=[{menuId=30, parentId=29, parentName=监管知识库, menuName=法规政策, orderNum=0, path=views/knowledgeBase/policiesLibrary/index.vue, component=null, query=null, routeName=法规政策, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=31, parentId=29, parentName=监管知识库, menuName=风险指标库, orderNum=1, path=views/knowledgeBase/riskLibrary/riskLibrary.vue, component=null, query=null, routeName=风险指标库, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=34, parentId=29, parentName=监管知识库, menuName=指标预警分配规则, orderNum=5, path=views/knowledgeBase/IndicatorRules/index.vue, component=null, query=null, routeName=指标预警分配规则, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=25, parentId=0, parentName=顶级菜单, menuName=基础信息维护, orderNum=4, path=borrow, component=null, query=null, routeName=基础信息维护, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=MessageBox, updateTime=null, children=[{menuId=26, parentId=25, parentName=基础信息维护, menuName=学校信息维护, orderNum=1, path=views/borrow/borrowingStockOut/unitDepartmentInformation.vue, component=null, query=null, routeName=学校信息维护, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=37, parentId=25, parentName=基础信息维护, menuName=任务填报, orderNum=1, path=views/invOutBill/taskFill/TaskFill.vue, component=null, query=null, routeName=null, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=27, parentId=25, parentName=基础信息维护, menuName=业务场景管理, orderNum=2, path=views/borrow/businessScenarioManagement/index.vue, component=null, query=null, routeName=业务场景管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=38, parentId=25, parentName=基础信息维护, menuName=部门工作管理中心, orderNum=2, path=views/invOutBill/departWorkCenter/departWorkCenter.vue, component=null, query=null, routeName=部门工作管理中心, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}, {menuId=20, parentId=0, parentName=顶级菜单, menuName=系统管理, orderNum=6, path=libraryManagement, component=null, query=null, routeName=系统管理, isFrame=null, isCache=null, menuType=目录, visible=1, status=null, perms=null, icon=Link, updateTime=null, children=[{menuId=21, parentId=20, parentName=系统管理, menuName=用户管理, orderNum=1, path=views/userManagement/user.vue, component=null, query=null, routeName=用户管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=22, parentId=20, parentName=系统管理, menuName=角色管理, orderNum=1, path=views/userManagement/userRole.vue, component=null, query=null, routeName=角色管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=23, parentId=20, parentName=系统管理, menuName=菜单权限管理, orderNum=1, path=views/userManagement/MenuPermissionManagement/MenuPermissionManagement.vue, component=null, query=null, routeName=菜单权限管理, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}, {menuId=48, parentId=20, parentName=系统管理, menuName=用户日志管理, orderNum=1, path=views\userManagement\userLog.vue, component=null, query=null, routeName=用户日志, isFrame=null, isCache=null, menuType=子菜单, visible=1, status=null, perms=null, icon=null, updateTime=null}]}]}
12:44:52.855 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:44:55.528 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.528 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.HealthIndicatorComponent
12:44:55.529 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.529 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{department_name=全校}]
12:44:55.529 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.529 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetMoveComponent
12:44:55.529 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.529 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.529 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, department_name=全校, page=1}]
12:44:55.529 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:44:55.529 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.529 [http-nio-19385-exec-3] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 开始查询所有达到预警阈值的资产信息，参数: {lookDept=全校, pageSize=10, page=1}
12:44:55.529 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:44:55.536 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:44:55.536 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:44:55.536 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getMove
12:44:55.536 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT department_name,
            department_code,
            user_code,
            user_name,
            state,
            SUM(quantity) AS quantity,
            SUM(amount) AS amount
        FROM
            assets_teacher_leave
        INNER JOIN warning_list wl
            ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                and wl.is_closed = '0'
        GROUP BY
            department_name,
            department_code,
            user_code,
            user_name,
            state
        ORDER BY
            quantity DESC
12:44:55.537 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:44:55.537 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:44:55.539 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptRoleNameByUserId
12:44:55.539 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT su.nickName as userId,su.deptId as deptId,su.dept as dept,sru.roleId as roleId,
            sr.roleName as roleName,sr.roleKey as roleKey
        FROM sys_user as su
        LEFT JOIN sys_role_user as sru on sru.userId = su.nickName
        LEFT JOIN (select * from sys_role where status = 1) as sr on sr.roleId = sru.roleId
        WHERE  su.nickName = :userId
12:44:55.540 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryTotalMonitoringIndicator
12:44:55.540 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_indicator
    FROM (
        SELECT id FROM assets_workflow_data
        UNION ALL
        SELECT id FROM assets_workflow_his
        UNION ALL
        SELECT user_code FROM asset_Registration
        GROUP BY user_code
    ) AS all_processes;
12:44:55.540 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetsExceedUsageThreshold
12:44:55.540 [http-nio-19385-exec-3] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            ar.asset_code,
            ar.asset_name,
            ar.asset_category_name,
            ar.user_department_name,
            ar.user_name,
            ar.amount as equipment_value,
            ar.asset_entry_date,
            ar.status_name,
            ar.storage_location,
            ar.model_brand,
            ar.specification,
            aur.statistics_year,
            aur.annual_rated_hours,
            aur.annual_usage_hours,
            aur.usage_rate,
            aur.effective_usage_rate,
            aur.shared_rate,
            aur.usage_level,
            aur.warning_status,
            aur.warning_level,
            il.indicator_id,
            il.threshold as configured_threshold,
            il.warning_level as threshold_warning_level,
            il.describe as threshold_description,
            (aur.usage_rate - il.threshold) as exceed_amount
        FROM asset_Registration ar
        INNER JOIN asset_usage_rate aur ON ar.asset_code = aur.asset_code
        INNER JOIN indicator_level il ON (
                il.indicator_name = '资产使用率'  -- 使用指标名称匹配您的配置
            AND aur.usage_rate > il.threshold  -- 使用率超过阈值
        )
        WHERE 1=1
            AND aur.statistics_year = YEAR(CURDATE())
        ORDER BY (aur.usage_rate - il.threshold) DESC, aur.usage_rate DESC
            LIMIT :dm_limit OFFSET :dm_offset
12:44:55.542 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.542 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetALLComponent
12:44:55.542 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.542 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, lookDept=全校, userDept=all}]
12:44:55.550 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getALLPri
12:44:55.555 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT count(*)
            FROM  (SELECT  department_name, department_code, user_code,
                          user_name,state
                   FROM assets_teacher_leave
                   INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('MOVE_', assets_teacher_leave.user_code)
                            and wl.is_closed = '0'
                   GROUP BY
                       department_name,
                       department_code,
                       user_code,
                       user_name,
                       state) AS a
12:44:55.558 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:44:55.559 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.559 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.ProcessMonitoringLevelOneClass.ProcessMonitoringComponent
12:44:55.559 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.559 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{lookDept=全校, startTime=, userDept=all}]
12:44:55.562 [http-nio-19385-exec-2] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select distinct department_name,
                department_code,
                user_code,
                user_name,
                quantity,
                amount,
                ranking
            FROM assets_over_regis
            INNER JOIN warning_list wl
                ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                    and wl.is_closed = '0'
             where 1=1
            ORDER BY quantity DESC limit 10
12:44:55.564 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryBusinessTypes
12:44:55.564 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT DISTINCT business_type FROM assets_workflow_data
    UNION
    SELECT DISTINCT business_type FROM assets_workflow_his;
12:44:55.600 [http-nio-19385-exec-10] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, state=已离职/已退休, quantity=11889.00, amount=31366345.50}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=dzw, user_name=丁子维, state=已离职/已退休, quantity=530.00, amount=2778805.00}, {department_name=继续教育学院, department_code=1025, user_code=xiongqian, user_name=熊倩, state=已离职/已退休, quantity=512.00, amount=2193478.30}, {department_name=已赔偿待下账资产, department_code=91, user_code=xgly, user_name=校管理员, state=已离职/已退休, quantity=474.00, amount=1747303796.46}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx2021619, user_name=沈艳云, state=已离职/已退休, quantity=300.00, amount=986657.44}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=jxply, user_name=蒲梁英, state=已离职/已退休, quantity=228.00, amount=1916485.00}, {department_name=资源环境学院, department_code=0002, user_code=wenxinyuan, user_name=文心媛, state=已离职/已退休, quantity=202.00, amount=1488938.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020003, user_name=王秀兰, state=已离职/已退休, quantity=173.00, amount=7871974.36}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020002, user_name=裴强, state=已离职/已退休, quantity=159.00, amount=438291.00}, {department_name=成信资产经营公司, department_code=1031, user_code=lili419, user_name=刘小莉, state=已离职/已退休, quantity=140.00, amount=361010.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=b2020083, user_name=周鹏飞, state=已离职/已退休, quantity=72.00, amount=245310.00}, {department_name=统计学院, department_code=0013, user_code=yangmeng, user_name=杨猛, state=已离职/已退休, quantity=71.00, amount=2726.49}, {department_name=继续教育学院, department_code=1025, user_code=kangdb, user_name=康电波, state=已离职/已退休, quantity=52.00, amount=179393.50}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=caoping, user_name=曹萍, state=已离职/已退休, quantity=50.00, amount=152029.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=zx20210001, user_name=冯科, state=已离职/已退休, quantity=25.00, amount=74897.10}, {department_name=文化艺术学院, department_code=0014, user_code=liyun, user_name=李云, state=已离职/已退休, quantity=21.00, amount=9815.90}, {department_name=资源环境学院, department_code=0002, user_code=lwj, user_name=刘文娟, state=已离职/已退休, quantity=21.00, amount=280100.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=yqh, user_name=杨清华, state=已离职/已退休, quantity=14.00, amount=63610.00}, {department_name=资源环境学院, department_code=0002, user_code=yzxiang, user_name=叶芝祥, state=已离职/已退休, quantity=11.00, amount=64761.95}, {department_name=大气科学学院, department_code=0001, user_code=donger, user_name=袁东升, state=已离职/已退休, quantity=10.00, amount=24900.96}, {department_name=管理学院, department_code=0011, user_code=chengcm, user_name=成美纯, state=已离职/已退休, quantity=9.00, amount=775700.00}, {department_name=成信资产经营公司, department_code=1031, user_code=liuxy, user_name=刘晓阳, state=已离职/已退休, quantity=5.00, amount=8129.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=xgq, user_name=徐格勤, state=已离职/已退休, quantity=5.00, amount=12320.00}, {department_name=省纪委监委驻校纪检监察组办公室（学校纪委办公室）、学校党委巡察工作办公室, department_code=1002, user_code=llqiang, user_name=李林襁, state=已离职/已退休, quantity=4.00, amount=10779.00}, {department_name=党委统战部, department_code=1006, user_code=quxing, user_name=瞿婞, state=已离职/已退休, quantity=4.00, amount=15349.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wangyue, user_name=罗望月, state=已离职/已退休, quantity=4.00, amount=17378.00}, {department_name=大气科学学院, department_code=0001, user_code=xwg, user_name=向卫国, state=已离职/已退休, quantity=4.00, amount=25138.00}, {department_name=计算机学院, department_code=0006, user_code=chenjun, user_name=陈俊, state=已离职/已退休, quantity=3.00, amount=510248.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=lisl, user_name=李胜蓝, state=已离职/已退休, quantity=3.00, amount=15125.72}, {department_name=计划财务处, department_code=1018, user_code=housy, user_name=侯嗣英, state=已离职/已退休, quantity=3.00, amount=10990.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020018, user_name=廖明全, state=已离职/已退休, quantity=3.00, amount=13150.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=baicj01, user_name=白成军, state=已离职/已退休, quantity=3.00, amount=7286.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=zhoulu, user_name=周露, state=已离职/已退休, quantity=2.00, amount=5420.00}, {department_name=软件工程学院, department_code=0007, user_code=yzr, user_name=姚紫茹, state=已离职/已退休, quantity=2.00, amount=33493.00}, {department_name=网络空间安全学院, department_code=0008, user_code=wanguogen, user_name=万国根, state=已离职/已退休, quantity=2.00, amount=10200.00}, {department_name=软件工程学院, department_code=0007, user_code=sihan, user_name=杨斯涵, state=已离职/已退休, quantity=2.00, amount=16587.00}, {department_name=继续教育学院, department_code=1025, user_code=jxzyl, user_name=周艳莉, state=已离职/已退休, quantity=2.00, amount=8264.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020272, user_name=张仕伟, state=已离职/已退休, quantity=2.00, amount=7950.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020036, user_name=卿皇友, state=已离职/已退休, quantity=2.00, amount=8120.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=grx, user_name=高瑞辛, state=已离职/已退休, quantity=2.00, amount=11000.00}, {department_name=资源环境学院, department_code=0002, user_code=cuilinlin, user_name=崔林林, state=已离职/已退休, quantity=2.00, amount=7750.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992376, user_name=唐学英, state=已离职/已退休, quantity=2.00, amount=3756.00}, {department_name=继续教育学院, department_code=1025, user_code=jx013, user_name=王玉军, state=已离职/已退休, quantity=2.00, amount=9340.00}, {department_name=软件工程学院, department_code=0007, user_code=yhcuit, user_name=余海, state=已离职/已退休, quantity=1.00, amount=10923.00}, {department_name=党委保卫部（保卫处）、武装部, department_code=1008, user_code=liangyong, user_name=梁勇, state=已离职/已退休, quantity=1.00, amount=2050.00}, {department_name=继续教育学院, department_code=1025, user_code=ycy, user_name=袁春艳, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=yanyq, user_name=严应琼, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=tuqing, user_name=涂青, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=pjx, user_name=彭佳欣, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=nearhigh, user_name=聂海, state=已离职/已退休, quantity=1.00, amount=3600.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=lquan, user_name=龙泉, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=继续教育学院, department_code=1025, user_code=lqjs0241, user_name=张钟元, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=大气科学学院, department_code=0001, user_code=lixinwei, user_name=李心伟, state=已离职/已退休, quantity=1.00, amount=5989.00}, {department_name=信息中心, department_code=1026, user_code=zz925, user_name=曾征, state=已离职/已退休, quantity=1.00, amount=9599.00}, {department_name=资源环境学院, department_code=0002, user_code=yl, user_name=杨利, state=已离职/已退休, quantity=1.00, amount=3456.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=yuhb, user_name=于红兵, state=已离职/已退休, quantity=1.00, amount=6700.00}, {department_name=文化艺术学院, department_code=0014, user_code=zhangj112, user_name=张静, state=已离职/已退休, quantity=1.00, amount=4835.00}, {department_name=继续教育学院, department_code=1025, user_code=zhaolj, user_name=赵丽君, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zhudy, user_name=朱德义, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230011, user_name=蒋雨睿, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20230012, user_name=刘孟婷, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=光电工程学院（人工影响天气学院）, department_code=0009, user_code=zp20230062, user_name=张曼, state=已离职/已退休, quantity=1.00, amount=6252.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240005, user_name=唐明媛, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=zp20240013, user_name=阳玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=fulin, user_name=付琳, state=已离职/已退休, quantity=1.00, amount=5000.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992377, user_name=崔群丽, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992381, user_name=熊诗莹, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992383, user_name=熊晓慧, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992384, user_name=陈沙, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992385, user_name=徐春梅, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992387, user_name=唐蓉, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=2019992434, user_name=朱礼娟, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=软件工程学院, department_code=0007, user_code=cdcxh, user_name=陈晓红, state=已离职/已退休, quantity=1.00, amount=15900.00}, {department_name=机关党委, department_code=1050, user_code=cl0833, user_name=陈玲, state=已离职/已退休, quantity=1.00, amount=4580.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=fenzi, user_name=辜俊, state=已离职/已退休, quantity=1.00, amount=4670.00}, {department_name=继续教育学院, department_code=1025, user_code=jxxyl, user_name=熊娅丽, state=已离职/已退休, quantity=1.00, amount=4529.00}, {department_name=物流学院, department_code=0012, user_code=guoxiaolin, user_name=郭晓林, state=已离职/已退休, quantity=1.00, amount=12112.00}, {department_name=软件工程学院, department_code=0007, user_code=gwhcuit, user_name=高文豪, state=已离职/已退休, quantity=1.00, amount=9693.00}, {department_name=继续教育学院, department_code=1025, user_code=gwl, user_name=顾雯琳, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=大气科学学院, department_code=0001, user_code=gyfa, user_name=巩远发, state=已离职/已退休, quantity=1.00, amount=9451.49}, {department_name=继续教育学院, department_code=1025, user_code=hjy, user_name=何君怡, state=已离职/已退休, quantity=1.00, amount=4960.00}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=hushiyu, user_name=胡诗宇, state=已离职/已退休, quantity=1.00, amount=6250.00}, {department_name=继续教育学院, department_code=1025, user_code=jx030, user_name=王玲, state=已离职/已退休, quantity=1.00, amount=3735.00}, {department_name=继续教育学院, department_code=1025, user_code=jxdl, user_name=邓琳, state=已离职/已退休, quantity=1.00, amount=4460.00}, {department_name=继续教育学院, department_code=1025, user_code=jxwsh, user_name=王韶鸿, state=已离职/已退休, quantity=1.00, amount=4529.00}], count=85}
12:44:55.604 [http-nio-19385-exec-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:44:55.608 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:55.608 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.IndicatorLevelClass.ExecuteAllIndicatorComponent
12:44:55.608 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:55.608 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{}]
12:44:55.612 [http-nio-19385-exec-4] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - IndicatorLevel.queryAllIndicator
12:44:55.612 [http-nio-19385-exec-4] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * from indicator_level left join knowledge_indicators on indicator_level.indicator_id
        = knowledge_indicators.indicator_id where type = "自定义指标"
12:44:55.614 [http-nio-19385-exec-2] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - select count(*)
                    FROM assets_over_regis
                    INNER JOIN warning_list wl
                        ON wl.warning_id = CONCAT('QUANT_', assets_over_regis.user_code)
                            and wl.is_closed = '0'
                     WHERE 1=1
12:44:55.615 [http-nio-19385-exec-5] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - SysUser.selectDeptList
12:44:55.615 [http-nio-19385-exec-5] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT * FROM dict_department
12:44:55.616 [http-nio-19385-exec-4] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: []
12:44:55.616 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.querySubmittedProcesses
12:44:55.616 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS submitted_count
    FROM (
        SELECT business_type, start_time FROM assets_workflow_his
        WHERE 1=1
            and using_department = :lookDept
        UNION ALL
        SELECT business_type, start_time FROM assets_workflow_data
        WHERE 1=1
    ) AS all_processes
    GROUP BY business_type;
12:44:55.618 [http-nio-19385-exec-2] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {code=1, data=[{department_name=图书馆, department_code=1027, user_code=cuitcy, user_name=陈越, quantity=39336.00, amount=9295791.87, ranking=1}, {department_name=后勤基建处（后勤服务公司）, department_code=1019, user_code=h2020012, user_name=姜亚萍, quantity=11889.00, amount=31366345.50, ranking=2}, {department_name=工程实践中心（创新创业学院）, department_code=0018, user_code=zhsh, user_name=张双, quantity=3822.00, amount=15692375.50, ranking=3}, {department_name=电子工程学院（大气探测学院）, department_code=0003, user_code=liyangji, user_name=李扬继, quantity=3232.00, amount=56920202.87, ranking=4}, {department_name=信息中心, department_code=1026, user_code=yaoyuan, user_name=姚远, quantity=2480.00, amount=13706647.60, ranking=5}, {department_name=自动化学院, department_code=0004, user_code=yingdong, user_name=应东, quantity=2111.00, amount=22713848.41, ranking=6}, {department_name=马克思主义学院, department_code=0016, user_code=zhouyan, user_name=周艳, quantity=1809.00, amount=991695.76, ranking=7}, {department_name=信息中心, department_code=1026, user_code=wangb, user_name=王兵, quantity=1770.00, amount=21284959.15, ranking=8}, {department_name=通信工程学院（微电子学院）, department_code=0005, user_code=exvi, user_name=高妮娜, quantity=1736.00, amount=19486657.39, ranking=9}, {department_name=软件工程学院, department_code=0007, user_code=huangjian, user_name=黄健, quantity=1449.00, amount=11693423.18, ranking=10}], count=86}
12:44:55.628 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryOngoingProcesses
12:44:55.628 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS ongoing_count
    FROM assets_workflow_data
    WHERE 1=1
    GROUP BY business_type;
12:44:55.635 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryCompletedProcesses
12:44:55.635 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT business_type, COUNT(*) AS completed_count
    FROM assets_workflow_his
    WHERE 1=1
    GROUP BY business_type;
12:44:55.639 [http-nio-19385-exec-9] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne_Pri.queryWarnings
12:44:55.640 [http-nio-19385-exec-9] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT  w.process AS business_type,
              COUNT(*) AS total_warnings,
              SUM(CASE WHEN w.warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
              SUM(CASE WHEN w.warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
        FROM (SELECT  * FROM assets_workflow_data
                   WHERE 1=1
)  AS a
        INNER JOIN  warning_list w
        ON
            a.document_number = w.bussiness_id
        and w.is_closed = '0'
        WHERE
            a.audit_status != "资产报废待终审" AND warning_level IN ('1', '2') AND indicator_id = 'zc001'
        GROUP BY w.process;
12:44:55.651 [http-nio-19385-exec-9] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {ongoingProcesses=[{business_type=低值耐用品报废报损, ongoing_count=66}, {business_type=低值耐用品登记, ongoing_count=52}, {business_type=低值耐用品院处内调拨, ongoing_count=101}, {business_type=低值耐用品院处间调拨, ongoing_count=21}, {business_type=资产减值, ongoing_count=1}, {business_type=资产增值, ongoing_count=3}, {business_type=资产报废, ongoing_count=240}, {business_type=资产登记, ongoing_count=147}, {business_type=院处内调拨, ongoing_count=436}, {business_type=院处间调拨, ongoing_count=76}], warnings=[{business_type=院处内调拨, total_warnings=410, warning_level_1=14, warning_level_2=396}, {business_type=院处间调拨, total_warnings=74, warning_level_1=1, warning_level_2=73}, {business_type=资产增值, total_warnings=3, warning_level_1=0, warning_level_2=3}, {business_type=资产减值, total_warnings=1, warning_level_1=0, warning_level_2=1}, {business_type=低值耐用品院处内调拨, total_warnings=97, warning_level_1=0, warning_level_2=97}, {business_type=低值耐用品院处间调拨, total_warnings=20, warning_level_1=0, warning_level_2=20}, {business_type=低值耐用品登记, total_warnings=48, warning_level_1=0, warning_level_2=48}, {business_type=资产登记, total_warnings=142, warning_level_1=1, warning_level_2=141}], completedProcesses=[{business_type=资产登记, completed_count=78}, {business_type=低值耐用品登记, completed_count=13}, {business_type=资产增值, completed_count=7}, {business_type=资产减值, completed_count=2}], businessTypes=[{business_type=低值耐用品报废报损}, {business_type=低值耐用品登记}, {business_type=低值耐用品院处内调拨}, {business_type=低值耐用品院处间调拨}, {business_type=资产减值}, {business_type=资产增值}, {business_type=资产报废}, {business_type=资产登记}, {business_type=院处内调拨}, {business_type=院处间调拨}], submittedProcesses=[{business_type=院处内调拨, submitted_count=436}, {business_type=资产报废, submitted_count=240}, {business_type=院处间调拨, submitted_count=76}, {business_type=资产增值, submitted_count=3}, {business_type=资产减值, submitted_count=1}, {business_type=低值耐用品院处间调拨, submitted_count=21}, {business_type=低值耐用品院处内调拨, submitted_count=101}, {business_type=低值耐用品报废报损, submitted_count=66}, {business_type=低值耐用品登记, submitted_count=52}, {business_type=资产登记, submitted_count=147}]}
12:44:55.697 [http-nio-19385-exec-8] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - ProcessMonitoringLevelOne.queryWarningCount
12:44:55.697 [http-nio-19385-exec-8] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT COUNT(*) AS total_warnings,
           SUM(CASE WHEN warning_level = '1' THEN 1 ELSE 0 END) AS warning_level_1,
           SUM(CASE WHEN warning_level = '2' THEN 1 ELSE 0 END) AS warning_level_2
    FROM warning_list
    WHERE warning_level IN ('1', '2')
    and is_closed = '0'
12:44:55.700 [http-nio-19385-exec-3] INFO  c.s.f.a.AssetWarningThresholdDashboardComponent - 成功查询到2条达到预警阈值的资产记录
12:44:55.701 [http-nio-19385-exec-8] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {warningLevel1=129, healthIndicator=1244, warningLevel2=892, totalIndicator=2265, totalWarnings=1021, healthScore=0.5492273730684327}
12:44:56.148 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:56.148 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.AssetStorageClass.GetTransferComponent
12:44:56.148 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:56.148 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{pageSize=10, page=1, department_name=全校}]
12:44:56.151 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - AssetStorage.getTransfer
12:44:56.151 [http-nio-19385-exec-7] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name,
                SUM(quantity) AS quantity,
                SUM(amount) AS amount
            FROM assets_dept_change
            GROUP BY
                old_department,
                old_department_code,
                new_department,
                new_department_code,
                user_code,
                user_name
            ORDER BY
                quantity DESC
            LIMIT :pageSize OFFSET :offset
12:44:56.162 [http-nio-19385-exec-7] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT COUNT(*)
            FROM (SELECT
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name,
                      SUM(quantity) AS quantity,
                      SUM(amount) AS amount
                  FROM assets_dept_change
                  GROUP BY
                      old_department,
                      old_department_code,
                      new_department,
                      new_department_code,
                      user_code,
                      user_name) As a
12:44:56.166 [http-nio-19385-exec-7] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: {data=[{old_department=管理学院, old_department_code=0011, new_department=统计学院, new_department_code=0013, user_code=zlhua, user_name=张利华, quantity=501.00, amount=2338940.00}, {old_department=大学科技园管委会办公室（成都研究院）, old_department_code=1030, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=guan, user_name=管廷岩, quantity=189.00, amount=1171245.00}, {old_department=信息中心, old_department_code=1026, new_department=党委保卫部（保卫处）、武装部, new_department_code=1008, user_code=ki, user_name=李伟, quantity=143.00, amount=886309.00}, {old_department=教务处, old_department_code=1014, new_department=实验与设备管理中心, new_department_code=1021, user_code=zjy, user_name=朱竞羽, quantity=131.00, amount=5380985.00}, {old_department=双流新型产业学院管委会, old_department_code=1029, new_department=网络空间安全学院, new_department_code=0008, user_code=czy, user_name=陈智勇, quantity=64.00, amount=169321.00}, {old_department=图书馆, old_department_code=1027, new_department=国内合作处(校友办公室), new_department_code=1012, user_code=lijun, user_name=李俊, quantity=22.00, amount=58766.00}, {old_department=网络空间安全学院, old_department_code=0008, new_department=人工智能学院（区块链产业学院）, new_department_code=0017, user_code=cuitzsb, user_name=张仕斌, quantity=15.00, amount=111203.00}, {old_department=大气科学学院, old_department_code=0001, new_department=科技处（大学科技园管委会办公室）, new_department_code=1016, user_code=zpg, user_name=赵鹏国, quantity=11.00, amount=50408.60}, {old_department=电子工程学院（大气探测学院）, old_department_code=0003, new_department=校领导, new_department_code=1000, user_code=hjx, user_name=何建新, quantity=10.00, amount=98296.36}, {old_department=党委宣传部（新闻中心）, old_department_code=1005, new_department=机关党委, new_department_code=1050, user_code=songziwei, user_name=宋子威, quantity=6.00, amount=21930.00}], count=25}
12:44:59.082 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: **********
12:44:59.082 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SecondLevelControlCenterClass.WarningDetailsComponent
12:44:59.082 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: run
12:44:59.082 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: [{start_time=2025-07-01 12:44:59, end_time=2025-07-31 12:44:59, indicator_id=zc001}]
12:44:59.091 [http-nio-19385-exec-6] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - indicatorDetails.getWarningList
12:44:59.091 [http-nio-19385-exec-6] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select
      w.warning_id,
      w.bussiness_id,
      w.indicator_id,
      w.indicator_name,
      w.warning_level,
      w.bussiness,
      w.process,
      w.sub_process,
      w.dept_name,
      w.isClosed,
      a.business_type,
      a.audit_status,
      a.using_department,
      a.asset_name,
      a.initiator,
      a.total_quantity,
      a.total_amount,
      a.task_executor,
      a.stay_hours,
      k.risk_description,
      k.refer_regu_describe
    from warning_2ed_level w
    left join assets_workflow_data a on w.bussiness_id = a.document_number
    left join knowledge_indicators k on w.indicator_id = k.indicator_id
    where 1=1
      and w.indicator_id = :indicator_id
      and w.start_time = :start_time
      and w.end_time = :end_time
    order by w.update_time desc
    limit 1
12:44:59.117 [http-nio-19385-exec-6] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: null
12:45:29.187 [http-nio-19385-exec-1] INFO  c.s.f.a.IndicatorLevelSupervisorLevel1Component - 开始查询一级监管指标配置信息，参数: {indicatorId=RK250730224834609091724GC31F2YE, responseType=json, componentName=IndicatorLevelSupervisorLevel1}
12:45:29.192 [http-nio-19385-exec-1] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getIndicatorLevelBySupervisorLevel1
12:45:29.192 [http-nio-19385-exec-1] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - SELECT
            il.ID as id,
            il.indicator_id,
            il.indicator_name,
            il.warning_level,
            il.describe as rule_description,
            il.sql,
            il.supervisor_level,
            il.threshold,
            il.modifer,
            il.update_time,
            ki.type,
            ki.business,
            ki.process,
            ki.sub_process,
            ki.monitor_obj,
            ki.risk_description,
            ki.prevension_measure,
            ki.refer_regu_describe
        FROM indicator_level il
        LEFT JOIN knowledge_indicators ki ON il.indicator_id = ki.indicator_id
        WHERE il.supervisor_level = 1
            AND il.indicator_id = :indicatorId
        ORDER BY il.warning_level ASC, il.threshold ASC
12:45:29.198 [http-nio-19385-exec-1] INFO  c.s.f.a.IndicatorLevelSupervisorLevel1Component - 成功查询到2条一级监管指标配置记录
12:49:53.694 [http-nio-19385-exec-5] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:49:53.697 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - ip：***********
12:49:55.687 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - ip：***********
