14:17:06.692 [http-nio-19385-exec-9] INFO  c.s.fswp.Controller.LoginController - ip：***********
14:17:06.702 [http-nio-19385-exec-7] INFO  c.s.fswp.Controller.LoginController - ip：***********
14:17:06.705 [http-nio-19385-exec-6] INFO  c.s.fswp.Controller.LoginController - ip：***********
14:17:22.482 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - AssetPriceRangeCountComponent - operationType: details, userDepartmentName: null
14:17:22.482 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - getAssetDetailsByPriceRange - Input: userDepartmentName=null, priceRangeIdentifier=R4, page=1, pageSize=15
14:17:22.482 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - getAssetDetailsByPriceRange - Calculated: currentPage=1, currentSize=15, offset=0
14:17:22.482 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - 价格区间资产明细查询 - listParams to be executed: {priceRangeIdentifier=R4, dataId=asset.getAssetDetailsByPriceRange, dm_limit=15, dm_offset=0}
14:17:22.485 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForListComponent - asset.getAssetDetailsByPriceRange
14:17:22.485 [http-nio-19385-exec-10] INFO  c.s.h.d.dao.jform.util.JdbcTemplate - select asset_name,
            asset_code,
            asset_category_name,
            unit_price,
            model_brand,
            specification,
            user_department_name,
            status_name,
            asset_entry_date,
            storage_location,
            quantity
        FROM
            asset_Registration
        WHERE 1=1
        AND asset_category_name LIKE '%设备%'
            AND unit_price >= 1000000
        ORDER BY
            unit_price DESC, asset_entry_date DESC
        LIMIT 15 OFFSET 0
14:17:22.620 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - 价格区间资产明细查询完成，返回 15 条记录
14:17:22.622 [http-nio-19385-exec-10] INFO  c.s.h.d.d.j.e.DatabaseQueryForObjectComponent - SELECT
            COUNT(id) AS total_assets
        FROM
            asset_Registration
        WHERE 1=1
        AND asset_category_name LIKE '%设备%'
            AND unit_price >= 1000000
14:17:22.738 [http-nio-19385-exec-10] INFO  c.s.f.a.AssetPriceRangeCountComponent - 价格区间资产总数: 34
