17:33:23.803 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 7408 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
17:33:23.806 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
17:34:14.845 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=a195e84c-903c-347f-a066-03c07c730866
17:34:28.350 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
17:34:28.365 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
17:34:28.368 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
17:34:28.368 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
17:34:28.406 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
17:34:28.406 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 64572 ms
17:34:28.719 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
17:34:28.749 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://************:19385]
17:34:28.751 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
17:34:28.751 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
17:34:28.751 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
17:34:28.753 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://************:19385]
17:34:28.754 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
17:34:28.754 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
17:34:28.754 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
17:34:29.150 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
17:34:29.150 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
17:34:37.347 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
17:34:37.937 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
17:34:40.236 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
17:34:40.426 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
17:34:40.448 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
17:34:40.567 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 90.907 seconds (process running for 95.847)
17:34:40.732 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:91s
17:34:41.223 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:34:41.223 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
17:34:41.225 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
17:34:41.267 [RMI TCP Connection(7)-************] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
18:00:00.018 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
18:00:00.019 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SecondLevelControlCenterClass.WarningUpgradeScheduleTask
18:00:00.019 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: executeWarningUpgradeTask
18:00:00.019 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: []
18:00:00.019 [scheduling-1] ERROR c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - 参数data为null……
18:00:00.022 [scheduling-1] INFO  c.s.h.dev.dao.jform.map.SqlMaps - 非web环境执行dataId
18:00:00.037 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: null
18:07:48.925 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
18:07:48.926 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
