10:56:47.295 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 32756 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
10:56:47.296 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
10:56:52.435 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
10:56:53.042 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
10:56:53.050 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
10:56:53.052 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
10:56:53.052 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
10:56:53.080 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
10:56:53.080 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5758 ms
10:56:53.249 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
10:56:53.272 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://************:19385]
10:56:53.274 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
10:56:53.274 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
10:56:53.274 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
10:56:53.276 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://************:19385]
10:56:53.276 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
10:56:53.276 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
10:56:53.276 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
10:56:53.414 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
10:56:53.414 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
10:56:56.099 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
10:56:56.445 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
10:56:57.279 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:56:57.356 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
10:56:57.368 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
10:56:57.381 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 10.808 seconds (process running for 11.222)
10:56:57.387 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:10s
10:56:57.658 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:56:57.658 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:56:57.660 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
10:56:57.691 [RMI TCP Connection(2)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:57:48.048 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
10:57:48.052 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
10:58:07.124 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 32160 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
10:58:07.125 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
10:58:08.193 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
10:58:08.733 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
10:58:08.740 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
10:58:08.741 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
10:58:08.741 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
10:58:08.766 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
10:58:08.766 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1620 ms
10:58:08.944 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
10:58:08.963 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
10:58:08.964 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
10:58:08.964 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
10:58:08.964 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
10:58:08.965 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
10:58:08.966 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
10:58:08.966 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
10:58:08.966 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
10:58:09.095 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
10:58:09.095 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
10:58:10.822 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
10:58:11.155 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
10:58:11.935 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:58:12.007 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
10:58:12.018 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 19385 (http) with context path '/fswp'
10:58:12.031 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Started ArchetypeDemoApplication in 5.357 seconds (process running for 5.685)
10:58:12.039 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动成功，耗时:5s
10:58:12.170 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:58:12.170 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:58:12.172 [RMI TCP Connection(1)-***********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
10:58:12.200 [RMI TCP Connection(5)-***********] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:59:31.778 [main] INFO  c.s.fswp.ArchetypeDemoApplication - Starting ArchetypeDemoApplication using Java 21.0.8 with PID 31940 (E:\Code\Work\SALWE-BACKEND\target\classes started by cyx11 in E:\Code\Work\SALWE-BACKEND)
10:59:31.779 [main] INFO  c.s.fswp.ArchetypeDemoApplication - The following 1 profile is active: "dev"
10:59:32.857 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=957c676f-69b0-3251-a40e-0dee64f5813b
10:59:33.385 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 19385 (http)
10:59:33.394 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-19385"]
10:59:33.395 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
10:59:33.395 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
10:59:33.421 [main] INFO  o.a.c.c.C.[.[localhost].[/fswp] - Initializing Spring embedded WebApplicationContext
10:59:33.421 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1620 ms
10:59:33.580 [main] INFO  o.s.b.web.servlet.RegistrationBean - Filter springSecurityAssertionSessionContextFilter was not registered (disabled)
10:59:33.601 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
10:59:33.603 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerLoginUrl] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver/login]
10:59:33.603 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignorePattern] loaded from FilterConfig.getInitParameter with value [(data/HealthIndicator.svt*)|(data/AssetLifecycleSelect.json*)|(/fswp/data/AssetTimeoutAlertSelect.json*)|(/fswp/data/AssetReportGenerator.json*)|(/fswp/data/AssetIndicator.json*)]
10:59:33.603 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [ignoreUrlPatternType] loaded from FilterConfig.getInitParameter with value [org.apereo.cas.client.authentication.RegexUrlPatternMatcherStrategy]
10:59:33.605 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [serverName] loaded from FilterConfig.getInitParameter with value [http://***********:19385]
10:59:33.606 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [redirectAfterValidation] loaded from FilterConfig.getInitParameter with value [true]
10:59:33.606 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [useSession] loaded from FilterConfig.getInitParameter with value [true]
10:59:33.606 [main] INFO  o.a.c.c.c.WebXmlConfigurationStrategyImpl - Property [casServerUrlPrefix] loaded from FilterConfig.getInitParameter with value [https://ywtb.cuit.edu.cn/authserver]
10:59:33.733 [main] WARN  org.apache.velocity.deprecation - configuration key 'file.resource.loader.class' has been deprecated in favor of 'resource.loader.file.class'
10:59:33.733 [main] WARN  org.apache.velocity.deprecation - configuration key 'userdirective' has been deprecated in favor of 'runtime.custom_directives'
10:59:35.395 [main] INFO  c.s.h.d.d.j.d.DynamicDataSourceConfig - 创建的数据源配置项:default,pms,research
10:59:35.730 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
10:59:36.552 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:59:36.625 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-19385"]
10:59:36.633 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
10:59:36.637 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
10:59:36.686 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
10:59:36.686 [main] INFO  c.s.j.d.s.r.c.l.DasSpringApplicationRunListener - 应用[fswp项目]启动失败，原因:Failed to start bean 'webServerStartStop'
10:59:36.749 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 19385 was already in use.

Action:

Identify and stop the process that's listening on port 19385 or configure this application to listen on another port.

10:59:52.928 [http-nio-19385-exec-3] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
10:59:52.987 [http-nio-19385-exec-3] INFO  c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - DatabaseSaveByDataMapComponent执行的sql是:
        INSERT INTO sys_user_log (nickName, userName, type, time, dept, ip)
        SELECT :nickName, :userName, :type, :time, :dept, :ip
        FROM dual
        WHERE NOT EXISTS (
            SELECT 1
            FROM sys_user_log
            WHERE nickName = :nickName
              AND type = 'logout'
            ORDER BY time DESC
            LIMIT 1
        ) OR :type != 'logout';
    
10:59:53.005 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 成功记录login日志: 2022081102
10:59:53.006 [http-nio-19385-exec-3] INFO  c.s.fswp.Controller.LoginController - 访问者:2022081102
11:00:00.002 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
11:00:00.002 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SecondLevelControlCenterClass.WarningUpgradeScheduleTask
11:00:00.002 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: executeWarningUpgradeTask
11:00:00.002 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: []
11:00:00.002 [scheduling-1] ERROR c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - 参数data为null……
11:00:00.003 [scheduling-1] INFO  c.s.h.dev.dao.jform.map.SqlMaps - 非web环境执行dataId
11:00:00.005 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: null
12:00:00.015 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
12:00:00.016 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SecondLevelControlCenterClass.WarningUpgradeScheduleTask
12:00:00.016 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: executeWarningUpgradeTask
12:00:00.016 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: []
12:00:00.017 [scheduling-1] ERROR c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - 参数data为null……
12:00:00.025 [scheduling-1] INFO  c.s.h.dev.dao.jform.map.SqlMaps - 非web环境执行dataId
12:00:00.027 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: null
13:00:00.014 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - User: anonymous
13:00:00.014 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Class: com.sunsheen.fswp.SecondLevelControlCenterClass.WarningUpgradeScheduleTask
13:00:00.015 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Method: executeWarningUpgradeTask
13:00:00.015 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Parameters: []
13:00:00.015 [scheduling-1] ERROR c.s.h.d.d.j.e.DatabaseSaveByDataMapComponent - 参数data为null……
13:00:00.020 [scheduling-1] INFO  c.s.h.dev.dao.jform.map.SqlMaps - 非web环境执行dataId
13:00:00.022 [scheduling-1] INFO  c.sunsheen.fswp.aop.LogParamsAspect - Return value: null
