<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.2.1</version> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.sunsheen</groupId>
	<artifactId>fswp</artifactId>
	<version>1.0.0</version>

  <properties>
	 <java.version>21</java.version>
  </properties>

  <dependencies>
	<dependency>
        <groupId>com.sunsheen.jfids.das.spring</groupId>
        <artifactId>hearken-das-spring-registry-client-starter</artifactId>
        <version>3.0.0</version>
    </dependency>
	  <dependency>
		  <groupId>com.sunsheen</groupId>
		  <artifactId>hearken-service</artifactId>
		  <version>0.0.3</version>
	  </dependency>

	  <dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter-web</artifactId>
	  </dependency>

	  <!-- 部署时打开以下注释，用于接入统一身份认证。cas的客户端 https://github.com/apereo/java-cas-client/tree/master-->

	  <dependency>
		  <groupId>org.apereo.cas.client</groupId>
		  <artifactId>cas-client-support-springboot</artifactId>
		  <version>4.0.2</version>
	  </dependency>

	  <dependency>
		  <groupId>mysql</groupId>
		  <artifactId>mysql-connector-java</artifactId>
		  <!-- 依据实际情况调整版本号 -->
		  <version>8.0.26</version>
	  </dependency>

	  <dependency>
		  <groupId>com.baomidou</groupId>
		  <artifactId>mybatis-plus-boot-starter</artifactId>
		  <version>3.5.3.1</version> <!-- 使用最新版本 -->
	  </dependency>

	  <!-- MyBatis-Spring 整合 -->
	  <dependency>
		  <groupId>org.mybatis.spring.boot</groupId>
		  <artifactId>mybatis-spring-boot-starter</artifactId>
		  <version>3.0.3</version> <!-- 使用最新稳定版 -->
	  </dependency>

	  <dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter</artifactId>
		  <version>3.1.5</version> <!-- 使用最新稳定版 -->
	  </dependency>

	  <!-- 可注释掉，登录认证-->
<!--	<dependency>-->
<!--		<groupId>com.sunsheen</groupId>-->
<!--		<artifactId>hearken-login</artifactId>-->
<!--		<version>0.0.3</version>-->
<!--	</dependency>-->

	  <dependency>
		  <groupId>com.fasterxml.jackson.core</groupId>
		  <artifactId>jackson-databind</artifactId>
		  <version>2.15.3</version>
	  </dependency>

	  <dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter-webflux</artifactId>
	  </dependency>

	  <!-- Markdown 转 HTML -->
	  <dependency>
		<groupId>com.vladsch.flexmark</groupId>
		<artifactId>flexmark-all</artifactId>
		<version>0.64.0</version>
	  </dependency>

	  <dependency>
		  <groupId>com.github.librepdf</groupId>
		  <artifactId>openpdf</artifactId>
		  <version>1.3.30</version>
	  </dependency>
	  <!-- iText 7 HTML to PDF -->
	  <dependency>
		  <groupId>com.itextpdf</groupId>
		  <artifactId>html2pdf</artifactId>
		  <version>5.0.3</version>
	  </dependency>

	  <!-- commonmark-java -->
	  <dependency>
		  <groupId>org.commonmark</groupId>
		  <artifactId>commonmark</artifactId>
		  <version>0.21.0</version>
	  </dependency>


	  <dependency>
		  <groupId>org.apache.httpcomponents</groupId>
		  <artifactId>httpclient</artifactId>
		  <version>4.5.13</version>
	  </dependency>

	  <dependency>
		  <groupId>org.springframework.security</groupId>
		  <artifactId>spring-security-core</artifactId>
		  <version>5.8.7</version>
	  </dependency>

	  <dependency>
		<groupId>javax.servlet</groupId>
		<artifactId>javax.servlet-api</artifactId>
		<version>4.0.1</version>
		<scope>provided</scope>
	  </dependency>


  </dependencies>
  
  <!-- 打依赖包-->
    <build>
        <plugins>
	        <plugin>
		        <groupId>org.springframework.boot</groupId>
		        <artifactId>spring-boot-maven-plugin</artifactId>
		        <configuration>
		            <classifier>exec</classifier>
		        </configuration>
	        </plugin>
        	<plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-resources-plugin</artifactId>
        	</plugin>
        	
        	<plugin>
			  <groupId>org.apache.maven.plugins</groupId>
			  <artifactId>maven-archetype-plugin</artifactId>
			  <version>3.2.0</version>
			  <configuration>

			  </configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<compilerArgs>--enable-preview</compilerArgs>
				</configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <!-- 下面的配置用于发布到淞幸私有仓库 -->
    <distributionManagement>
        <repository>
            <id>hearken-releases</id>
            <url>http://maven.hearker.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>hearken-snapshots</id>
            <url>http://maven.hearker.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
	
	<repositories>
		<repository>
			<id>hearken-releases</id>
			<name>hearken-releases</name>
			<url>http://maven.hearker.com/repository/maven-releases/</url>
			<layout>default</layout>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
		<repository>
			<id>hearken-snapshots</id>
			<name>hearken-snapshots</name>
			<url>http://maven.hearker.com/repository/maven-snapshots/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>

		<pluginRepository>
			<id>hearken-releases</id>
			<name>hearken-releases</name>
			<url>http://maven.hearker.com/repository/maven-releases/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</pluginRepository>

		<pluginRepository>
			<id>hearken-snapshots</id>
			<name>hearken-snapshots</name>
			<url>http://maven.hearker.com/repository/maven-snapshots/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
</project>
